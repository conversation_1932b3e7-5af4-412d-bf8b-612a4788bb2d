import G6 from '@antv/g6';
import {Graph, NodeConfig, EdgeConfig} from '@antv/g6';

export const useG6 = (container: HTMLElement) => {
  const width: number = container?.clientWidth;
  const height: number = container?.clientHeight;
  //画布实例
  const graph: Graph = new G6.TreeGraph({
    container: 'container', //容器
    width: width, //显示树的内容区宽度
    height: height, //显示树的内容区高度
    // pixelRatio: 2,
    linkCenter: true, //线是否从元素内中间穿过
    fitView: true, //是否自适应且剧中
    renderer: 'svg', //使用自定义dom需要设置
    //设置节点收到某些事件后的样式变化
    nodeStateStyles: {
      // 例:点击节点，节点背景颜色变为#2b90f8  注：须在下面调用
      click: {
        fill: '#2ea9f2',
        stroke: '#3094df', // 边框
      },
    },

    modes: {
      default: ['drag-canvas', 'zoom-canvas'],
    },

    // 设置node节点
    defaultNode: {
      // // 设置节点的形状 circle：圆，rect：矩形，ellipse：椭圆；polygon：多边形；fan：扇形；image：图片；marker：标记；path：路径；text：文本；
      // type: 'rect',
      // // 设置图形样式
      // style: {
      //   lineWidth: 1, // 内边距
      //   stroke: '#36b6ff', // 边框
      //   fill: '#0060a3', // 背景色
      // },
      type: 'custom-node',
      anchorPoints: [
        [0, 0.5],
        [1, 0.5],
      ],
    },

    layout: {
      type: 'compactBox', // compactBox紧凑树
      direction: 'LR', // 佩列方向 TB竖向排列
      getId: function getId(d: any) {
        return d.id + '';
      },
      getHeight() {
        return 30;
      },
      getWidth() {
        return 130;
      },
      getVGap() {
        return 20;
      },
      getHGap() {
        return 30;
      },
    },
  });
  //定义连线
  graph.edge((edge: EdgeConfig) => {
    edge.type = 'cubic-horizontal'; // 折线
    // 'polyline';
    // 'arc'; // 圆弧线
    // 'loop'; // 自环
    // 'cubic'; // 三阶贝塞尔取消
    // 'quadratic'; // 二阶贝塞尔曲线
    // 'line'; // 直线
    // 'cubic-vertical'; // 垂直方向的三阶贝塞尔曲线
    // 'cubic-horizontal'; // 水平方向的三阶贝塞尔曲线
    return {
      color: '#36b6ff', // 线的颜色
    };
  });

  G6.registerNode('custom-node', {
    draw: (cfg, group) => {
      // 点击节点
      const node = group!.addShape('dom', {
        attrs: {
          width: 100,
          height: 50,
          html: `<div
           id="node${cfg!.id}"
           class="node-box dis-flex flex-dir-column align-items"
           onclick="netNode('id:${cfg!.id},displayName:${cfg!.displayName},name:${cfg!.name},networkType:${cfg!.networkType},level:${cfg!.depth}')"
          >
            ${cfg!.depth === 0 ? `<img id="node-icon-${cfg!.id}" class="icon icon-network" src="/images/network.png" />` : `<img id="node-icon-${cfg!.id}" class="icon icon-terminal" src="/images/terminal.png" />`}
          <p class="text-ellipsis" title="${cfg!.displayName}">${cfg!.displayName}</p>
          </div>`,
        },
        name: 'node-shape',
      });

      return node;
    },
  });
  // 渲染树数据
  const renderTree = (treeData: {}) => {
    graph.data(treeData);
    graph.render();
    graph.fitView();
    graph.zoom(0.8);
    graph.translate(100, 50);
  };

  return {graph, renderTree};
};
