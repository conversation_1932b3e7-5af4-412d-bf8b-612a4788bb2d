<!--
 * @Description: 作战区域要素管理组件
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-12-23 15:35:27
 * @LastEditors: 老范
 * @LastEditTime: 2025-03-21 17:01:55
-->
<script setup>
import {ElMessage, ElMessageBox} from 'element-plus';
import {ref, watch} from 'vue';
import {getGeoJsonCenter} from '@/utils/tools';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
const stateControler = useStateStore();

const {mapInstance, MapGl} = storeToRefs(useMapStore());
const dataList = ref([]);
watch(
  () => stateControler.graphicManagerVisible,
  () => getData()
);

const add = () => {
  stateControler.drawDialogVisible = true;
  stateControler.graphicManagerVisible = false;
};
const look = data => {
  console.log(data);
  //获取中心点
  let center;
  if (data.properties.zoneType === 'polygonal') {
    center = getGeoJsonCenter(data);
  } else {
    center = data.geometry.coordinates;
  }
  mapInstance.value.map.flyTo({
    center,
    essential: true,
    speed: 0.9,
  });

  stateControler.graphicManagerVisible = false;
};
const copy = data => {
  const copyData = JSON.parse(JSON.stringify(data));
  delete copyData.id;

  copyData.globalId = stateControler.graphicId;
  copyData.type = 'Feature';
  copyData.properties.color = stateControler.entityListData.filter(i => {
    return i.side === copyData.properties.side;
  })[0]?.color;
  const displayName = copyData.displayName.split('-')[0] + `-${stateControler.graphicId}`;
  copyData.displayName = displayName;
  copyData.properties.displayName = displayName;
  // //添加到draw 绘制
  // const ids = mapInstance.value.drawInstance.add(copyData);
  // //生成的id赋值到数据中
  // copyData.id = ids[0];
  // mapInstance.value.graphicalGeojson.features.push(copyData);
  mapInstance.value.addGraphical(copyData);
  getData();
};
const edit = data => {
  stateControler.currentZoneData = data;
  console.log(stateControler.currentZoneData);
  stateControler.drawDialogVisible = true;
  stateControler.graphicManagerVisible = false;
};
const del = data => {
  ElMessageBox.confirm('此操作将永久删除该数据,是否继续?', '提示', {
    type: 'warning',
    closeOnClickModal: false,
  })
    .then(() => {
      //删除geojson
      mapInstance.value.graphicalGeojson.features = mapInstance.value.graphicalGeojson.features.filter(el => {
        return el.id !== data.id;
      });
      if (data.properties.zoneType === 'polygonal') {
        //删除控件的显示
        mapInstance.value.drawInstance.delete(data.id);
      } else {
        mapInstance.value.geojson.features = mapInstance.value.geojson.features.filter(i => i.id !== data.id);
        mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
      }
      ElMessage.success('删除成功!');
      getData();
    })
    .catch(() => ElMessage.info('已取消'));
};
const getData = () => {
  console.log(mapInstance.value.graphicalGeojson.features);
  const drawData = mapInstance.value.graphicalGeojson.features.filter(i => i.geometry.type === 'Polygon' || i.properties.zoneType === 'circular');
  console.log('🚀 ~ getData ~ drawData:', drawData);
  dataList.value = drawData;
};
</script>

<template>
  <el-dialog v-model="stateControler.graphicManagerVisible" draggable :title="'作战区域要素管理'" width="40%" center :close-on-click-modal="false" :modal-append-to-body="false">
    <div class="content">
      <el-button class="add-btn" @click="add"><i class="icon add-icon"></i> 添加区域</el-button>
      <el-table :data="dataList" max-height="400">
        <el-table-column prop="zoneType" label="类型">
          <template #default="scope"> {{ scope.row.properties.zoneType }}</template>
        </el-table-column>
        <el-table-column prop="displayName" label="名称">
          <template #default="scope"> {{ scope.row.properties.displayName }}</template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <div class="control-btn">
              <el-button @click="look(scope.row)">查看</el-button>
              <!-- <el-button @click="copy(scope.row)">复制</el-button> -->
              <el-button @click="edit(scope.row)">编辑</el-button>
              <el-button @click="del(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<style lang="less" scoped>
.content {
  height: 400px;

  .add-btn {
    margin: 5px 0;
  }
}

.control-btn {
  width: 400px;
  height: 40px;
  line-height: 40px;
}
</style>
