<!--
 * @Description: 项目主体
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-15 16:40:56
-->
<script setup lang="ts">
import GraphicEditPanel from '@/components/map_dialogs/GraphicEditPanel.vue';
import DrawDialog from '@/components/map_dialogs/draw/DrawDialog.vue';
import MapLayer from '@/components/map_dialogs/MapLayer.vue';
import EntityPanel from '@/components/entity_panel/EntityPanel.vue';
import ResourcePanel from '@/components/resource_panel/ResourcePanel.vue';
import EntityParamPanel from '@/components/map_dialogs/EntityParamPanel.vue';
import GraphicManager from '@/components/map_dialogs/GraphicManager.vue';
import GlobalScript from '@/components/map_dialogs/GlobalScript.vue';
import Group from '@/components/resource_panel/group_config/group_index.vue';
import Maplibre from '@/modules/maplibre';
import {onMounted, ref, computed} from 'vue';
import {useMapStore} from '@/pinia/map/index';
import bus from '@/utils/bus';
onMounted(() => {
  mapControler.initMap(mapContainer.value);
  bus.emit('mapLoaded');
});
const MapStore = useMapStore();
const {setMapInstance} = MapStore;
const mapControler = new Maplibre();

const mapContainer = ref('');
const drawDialog = ref();
setMapInstance(mapControler);
const isSelect = computed(() => drawDialog.value?.isSelectPoint || '');
const allDrap = (ev: Event) => ev.preventDefault();
</script>

<template>
  <div class="body-container">
    <div ref="mapContainer" :class="[isSelect ? 'crosshair' : '', 'map-container']" @dragover="allDrap">
      <!--图形参数配置-->
      <GraphicEditPanel />
      <DrawDialog ref="drawDialog" />
      <!-- 图形管理 -->
      <GraphicManager />
      <!-- 全局脚本配置 -->
      <GlobalScript />
      <!--图源切换组件 -->
      <MapLayer />
      <ResourcePanel />
      <EntityPanel />
      <Group />
      <EntityParamPanel />
    </div>
  </div>
</template>

<style lang="less" scoped>
.body-container {
  height: calc(100vh - 50px);

  .map-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  :deep(.crosshair canvas) {
    cursor: crosshair !important;
  }
}
</style>
