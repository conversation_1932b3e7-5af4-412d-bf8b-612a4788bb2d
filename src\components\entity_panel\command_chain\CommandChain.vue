<script setup>
import {ElMessage, ElMessageBox} from 'element-plus';
import {useStateStore} from '@/pinia/state/index';
import {checkPlateformHasCom1} from '@/api/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
import {ref, onMounted, reactive, computed, watch} from 'vue';
import bus from '@/utils/bus';
import {generateUuid} from '@/utils/tools';

onMounted(() => {
  stateControler.commandChainData = state.commandChainData;
  bus.on('changeCommandChain', () => {
    state.commandChainData.forEach(item => {
      item.content.forEach(element => {
        if (element.children.length > 0) findEntity(element);
      });
    });
  });
  getmaxChainId();
});
const stateControler = useStateStore();

const {mapInstance} = storeToRefs(useMapStore());
// interface chainTree {
//   title: string;
//   side: string;
//   color: string;
//   content: {name: string, displayName: string, children: never[], chainIndex: number}[];
// }
const topRange = ref('200px');
const state = reactive({
  currentSide: 'red', //阵营绑定值
  commandChainData: [
    {
      title: '红方',
      side: 'red',
      color: '#F80404',
      content: [{name: 'default', displayName: '默认指挥链', children: [], chainIndex: 0, id: generateUuid()}],
    },
    {
      title: '蓝方',
      side: 'blue',
      color: '#00B1FF',
      content: [{name: 'default', displayName: '默认指挥链', children: [], chainIndex: 0, id: generateUuid()}],
    },
  ], //指挥链数据
  entityData: '', //添加的实体数据
  chainData: {
    chainName: '',
    chainDisplayName: '',
    chainId: '',
  }, //添加的指挥链数据
  dialogVisible: false,
  isAddEntity: true,
  dialogTitle: '',
  currentCampEntityData: [],
  itemId: 1, //全局id
  currentEditData: '', //记录当前要操作的数据
});
const currentChainEntities = ref([]);
const currentChainIndex = ref(0);
const unAbleCheckNodes = ref([]);
const maxChainId = ref(-1);

const entitys = computed(() => mapInstance.value.geojson.features.map(i => i.properties));

watch(
  () => entitys.value.length,
  () => {
    getCampData(stateControler.entityListData);
  },
  {deep: true}
);

watch(
  () => stateControler.commandChainData,
  newVal => {
    if (newVal.length === 0) {
      state.commandChainData = [
        {
          title: '红方',
          side: 'red',
          color: '#F80404',
          content: [{name: 'default', displayName: '默认指挥链', children: [], chainIndex: 0, id: generateUuid()}],
        },
        {
          title: '蓝方',
          side: 'blue',
          color: '#00B1FF',
          content: [{name: 'default', displayName: '默认指挥链', children: [], chainIndex: 0, id: generateUuid()}],
        },
      ];
    } else {
      state.commandChainData = newVal;
    }
    getmaxChainId();
  },
  {deep: true}
);

// 查找实体并处理
const findEntity = data => {
  // 指挥链剔除已经被删除的实体
  data.children = data.children.filter(i => {
    const res = entitys.value.find(e => {
      return i.entityId === e.entityId;
    });
    return res;
  });

  data.children.forEach(item => {
    entitys.value.forEach(ele => {
      if (ele.entityId === item.entityId) {
        item.name = ele.entityName;
        item.displayName = ele.entityDisplayName;
      }
    });
    if (item.children && item.children.length > 0) findEntity(item);
    else return;
  });
};

const handleDragEnter = (draggingNode, dropNode) => {
  // 在节点进入时触发，可以在这里判断条件是否能放置
  if (dropNode.level > 1) {
    return true;
  } else {
    return false;
  }
};
const showAddChain = event => {
  topRange.value = event.y + 25 + 'px';
  state.dialogVisible = true;
  state.isAddEntity = false;
  state.dialogTitle = '添加指挥链';
  state.chainData.chainName = '';
  state.chainData.chainDisplayName = '';
  state.currentEditData = '';
};
const confirm = async () => {
  //获取当前阵营index
  const index = state.commandChainData.findIndex(i => i.side === state.currentSide);
  if (state.dialogTitle === '修改指挥链') {
    state.commandChainData[index].content.forEach(item => {
      if (item.id === state.chainData.chainId) {
        item.displayName = state.chainData.chainDisplayName;
        item.name = state.chainData.chainName;
      }
    });
  } else {
    if (state.isAddEntity && state.entityData === '') return ElMessage.warning('请选择实体');
    if (state.entityData !== '') {
      const res = await checkPlateformHasCom1(state.entityData.platformTypeId);
      console.log('🚀 ~ confirm ~ res:', res);
      const isHaveCommunicationModule = res.data;
      // const isHaveCommunicationModule = res.data?.modules.some(item => {
      //   return item.mainCategoryId === 6;
      // });
      if (!isHaveCommunicationModule) {
        ElMessage({
          message: `实体${res.data.name}未配置通信组件，无法完成指挥通信！`,
          grouping: true,
          type: 'warning',
          duration: 3000,
          showClose: true,
        });
      }
    }
    if (state.chainData.chainName === 'default' || state.chainData.chainDisplayName === 'default') {
      return ElMessage.error('不能添加default指挥链!');
    }
    const newChild = {
      id: generateUuid(),
      displayName: state.isAddEntity ? state.entityData.entityDisplayName : state.chainData.chainDisplayName,
      name: state.isAddEntity ? state.entityData.entityName : state.chainData.chainName,
      type: state.isAddEntity ? 'entity' : 'chain',
      children: [],
      chainIndex: state.isAddEntity ? currentChainIndex.value : maxChainId.value + 1,
    };
    if (state.isAddEntity) newChild.entityId = state.entityData.entityId;
    if (!newChild.name | !newChild.displayName) return ElMessage.warning('请输入名称!');
    //其他层级添加实体
    if (state.currentEditData) {
      state.currentEditData.children.push(newChild);
    } else {
      //根级添加实体
      state.commandChainData[index].content.push(newChild);
    }
  }

  state.dialogVisible = false;
  state.currentEditData = '';
  state.entityData = '';
  state.chainData.chainName = '';
  state.chainData.chainDisplayName = '';
};
const showAddEntity = (event, data) => {
  topRange.value = (event.y + 10 > 500 ? 500 : event.y + 10) + 'px';
  state.currentCampEntityData = mapInstance.value.geojson.features.filter(i => i.properties.side === state.currentSide && i.properties.isEntity).map(i => i.properties);
  currentChainIndex.value = data.chainIndex || 0;
  state.commandChainData.forEach(item => {
    if (item.side === state.currentSide) {
      currentChainEntities.value = item.content.filter(item => {
        return item.chainIndex === currentChainIndex.value;
      })[0].children;
    }
  });
  event.stopPropagation();
  state.isAddEntity = true;
  state.dialogTitle = '添加实体';

  state.dialogVisible = true;
  state.entityData = '';
  unAbleCheckNodes.value = [];
  state.currentEditData = data;
  findEntitysNegation();
};
// 找出不可继续添加的实体单位(该单位已出现)
const findEntitysNegation = () => {
  function flatten(arr) {
    return arr.reduce((result, item) => {
      return result.concat(item, Array.isArray(item.children) ? flatten(item.children) : []);
    }, []);
  }

  const flatTreeChain = flatten(currentChainEntities.value);
  flatTreeChain.forEach(item => {
    if (item.id) {
      unAbleCheckNodes.value.push(item.displayName);
    }
  });
  state.currentCampEntityData = state.currentCampEntityData.filter(item => {
    if (!unAbleCheckNodes.value.includes(item.entityDisplayName)) {
      return item;
    }
  });
};

// 删除指挥链
const remove = (event, node, data) => {
  event.stopPropagation();
  ElMessageBox.confirm('确定删除吗?', '提示', {type: 'warning', closeOnClickModal: false})
    .then(() => {
      //删除数据
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex(d => d.id === data.id);
      children.splice(index, 1);
      ElMessage.success('删除成功!');
      getmaxChainId();
    })
    .catch(() => ElMessage.info('已取消'));
};
// 更新数据
const getCampData = e => {
  // 解决点击滞后问题
  state.currentSide = e?.props?.name || 'red';
  // 获取当前阵营的实体列表
  state.currentCampEntityData = mapInstance.value.geojson.features.filter(i => i.properties.side === state.currentSide).map(i => i.properties);
};

const getmaxChainId = () => {
  maxChainId.value = -1;
  stateControler.commandChainData.forEach(item => {
    item.content.forEach(chain => {
      if (maxChainId.value < chain.chainIndex) {
        maxChainId.value = chain.chainIndex;
      }
    });
  });
};
const editChain = (data, e) => {
  topRange.value = e.y + 25 + 'px';
  state.dialogVisible = true;
  state.isAddEntity = false;
  state.dialogTitle = '修改指挥链';
  state.chainData.chainName = data.name;
  state.chainData.chainDisplayName = data.displayName;
  state.chainData.chainId = data.id;
};
const allowDrag = node => {
  return node => node.level > 1;
};
const dropEnd = (before, end, type) => {
  if (before.data.chainIndex !== end.data.chainIndex) {
    return false;
  } else if (type !== 'inner') {
    return false;
  } else return true;
};
</script>
<template>
  <div ref="commandChain" class="command-chain-content">
    <div class="command-chain-body">
      <el-tabs v-model="state.currentSide" type="card" @tab-click="getCampData">
        <el-tab-pane v-for="item in state.commandChainData" :key="item.side" :label="item.title" :name="item.side">
          <div class="add-chain">
            <el-button size="small" @click="showAddChain($event)"><i class="icon add-icon"></i>添加指挥链</el-button>
          </div>
          <el-tree id="commandChainTree" ref="commandChainTree" draggable :indent="6" node-key="id" default-expand-all :allow-drag="allowDrag" :data="item.content" :allow-drop="dropEnd">
            <template #default="{node, data}">
              <span class="custom-tree-node">
                <span :title="data.displayName" class="node-label">
                  <i v-show="node.level === 1" class="point"></i>
                  {{ data.displayName }}
                </span>
                <span class="node-button">
                  <i v-if="node.level === 1 && node.data.name != 'default'" title="编辑" class="icon edit-icon" @click="e => editChain(data, e)"></i>
                  <i title="添加" class="icon add-icon" @click="showAddEntity($event, data)"></i>
                  <i v-show="data.name !== 'default'" title="删除" class="icon border-delete-icon" @click="remove($event, node, data)"></i>
                </span>
              </span>
            </template>
          </el-tree>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 添加指挥链或者实体 -->

    <el-dialog v-model="state.dialogVisible" center width="10%" :top="topRange" class="dialogClass" :modal="false" :title="state.dialogTitle">
      <div class="form-content">
        <div v-if="state.isAddEntity">
          <div>实体：</div>
          <el-select v-model="state.entityData" class="entity-select" suffix-icon="CaretBottom" value-key="entityId" placeholder="请选择">
            <el-option v-for="item in state.currentCampEntityData" :key="item.value" :label="item.entityDisplayName" :value="item"> </el-option>
          </el-select>
        </div>

        <div v-else>
          <div><span>显示名称:</span> <el-input v-model="state.chainData.chainDisplayName"> </el-input></div>
          <div><span>名称:</span><el-input v-model="state.chainData.chainName"> </el-input></div>
        </div>
      </div>
      <template #footer>
        <el-button @click="state.dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.command-chain-content {
  width: 100%;
  height: calc(100vh - 190px);
  color: #fff;
  border-radius: 5px;
  box-sizing: border-box;

  .command-chain-body {
    width: 100%;
    height: 100%;

    :deep(.el-tabs__nav.is-top) {
      width: 100% !important;
    }

    :deep(.el-tabs__content) {
      height: calc(100% - 32px) !important;
    }

    .add-chain {
      height: 30px;
      margin: 5px;

      .el-button {
        float: right;
      }
    }
  }

  .form-content {
    padding: 0 10px;

    .entity-select {
      width: 100%;
    }

    div {
      margin: 5px 0;
      color: #fff;
    }

    span {
      width: 100px;
    }
  }

  .dialog-footer {
    position: relative;
    top: -60px;
  }
}

.command-chain-content .custom-tree-node {
  display: flex;
  width: 100%;
  padding-right: 10px;
  align-items: center;
  justify-content: space-between;
  font-size: 15px;
}

.command-chain-content .node-label {
  display: inline-block;
  width: 180px;
  overflow: hidden;
  text-align: left;
  text-overflow: ellipsis;
}

.point {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: var(--icon-color);
  border-radius: 50%;
}

.command-chain-content .node-button {
  position: absolute;
  right: 6px;
  display: flex;
  color: var(--border-color);
}

.command-chain-content .node-button i {
  margin: 0 5px;
}

:deep(.dialogClass) {
  position: absolute;
  right: 10px;
}
</style>
