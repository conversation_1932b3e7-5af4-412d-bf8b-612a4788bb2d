/* 样式设置，element覆盖 */
.el-scrollbar {
  --el-scrollbar-opacity: 1;
  --el-scrollbar-bg-color: #097cbc;
  --el-scrollbar-hover-opacity: 1;
  --el-scrollbar-hover-bg-color: #097cbc;
}

.el-popper.is-light .el-popper__arrow::before {
  right: 0;
  background: #00598c;
  border: 1px solid var(--border-color);
}

/* ----------------------el-message样式设置------------------- */
.el-message {
  top: 20px;
}

.el-message-box__title,
.el-message-box__content {
  color: #fff;
}

.el-message-box__input .el-input__inner {
  background: none;
}

.el-message-box__btns .el-button {
  border: 0 !important;
}

/* ----------------------el-popper样式设置------------------- */
.el-popper.is-light {
  background-color: #00598c;
  border-color: var(--border-color);
  transition: none 0.1s ease 0s;
}

.el-popper .popper__arrow,
.el-popper .popper__arrow::after {
  border-bottom-color: #3d4a50 !important;
}

/* --------------------------el-dialog样式及其内部el-form设置------------------------ */
.el-message-box {
  background: var(--panel-bg-color);
  border: 1.5px solid var(--title-bg-color);
  border-radius: 5px;
}

.el-dialog {
  padding: 0;
  background: transparent;
  border: var(--border);
}

.el-overlay-dialog {
  overflow: hidden;
}

.el-dialog--center .el-dialog__body {
  padding: 5px;
  background: var(--panel-bg-color);
}

.el-dialog__header {
  display: flex;
  padding: 5px 0;
  margin-right: 0;
  background: var(--title-bg-color);
  justify-content: center;
}

.el-dialog__header .el-dialog__title {
  color: #c3f8ff;
}

.el-dialog__body .el-form .el-input .el-input__inner {
  background: none !important;
}

/* 弹窗底部 */
.dialog-footer {
  width: 100%;
  height: 30px;
  margin: 5px 0;
  text-align: center;
  border-bottom-left-radius: var(--border-rdius-5);
  border-bottom-right-radius: var(--border-rdius-5);
}

.dialog-footer button {
  border: 0 !important;
}

.el-dialog__footer {
  padding: 10px;
  background-color: var(--panel-bg-color);
}

/* --------------------------el-input样式设置------------------------ */
.el-input__inner {
  height: 25px;
  line-height: 30px;
  color: var(--title-color) !important;
}

.el-input__wrapper {
  padding: 0 5px;
  color: var(--title-color);
  background-color: transparent;
  border-radius: 2px;
  background-image: linear-gradient(0deg, #073b5a 0%, #022f49 100%);
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

.el-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #02e0ff inset;
}

.el-select__wrapper:hover {
  border: 0;
  box-shadow: 0 0 0 1px var(--border-color) inset !important;
}

.el-select__wrapper.is-focused {
  border: 0 !important;
  box-shadow: 0 0 0 1px #02e0ff inset !important;
}

.el-input:focus {
  border-color: var(--highlight-color);
}

.el-input.is-disabled .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

.el-input.is-disabled .el-input__inner {
  -webkit-text-fill-color: var(--font-color);
}

.el-input.is-disabled .el-input__wrapper {
  color: var(--font-color);
  background-color: transparent;
}

/* 右侧箭头行高 */
.el-input__icon {
  line-height: 25px;
}

.el-pager li {
  color: #fff;
  background: none;
}

/* ----------------------------- el-table样式设置 ------------------------- */

.el-table {
  --el-table-text-color: #fff;
  --el-table-header-text-color: #fff;
  --el-table-header-bg-color: #07518e;
  --el-table-border: 1px solid #07518e;
  --el-table-border-color: var(--title-bg-color);
  --el-table-row-hover-bg-color: transparent;

  .el-table td,
  .el-table th.is-leaf {
    padding: 0 !important;
  }
}

/* stylelint-disable-next-line no-descending-specificity */
.el-table--enable-row-hover .el-table__body .current-row > td.el-table__cell {
  background-color: var(--highlight-color);
}

.el-table__expand-icon > .el-icon {
  scale: 1.5;
  color: var(--title-color);
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background: #053c5c;
}

.el-table--striped .el-table__body tr.el-table__row--striped.current-row td.el-table__cell {
  background-color: transparent !important;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: var(--highlight-color) !important;
}

.el-table,
.el-table tr {
  color: #fff;
  background-color: transparent !important;
}

.el-table th {
  padding: 0;
  color: #fff;
  background-color: var(--th-bg-color) !important;
}

.el-table__body tr > td.hover-cell {
  background-color: transparent !important;
}

.el-table__body tr.hover-row > td.el-table_cell {
  background-color: transparent !important;
}

/* 表格内容居中 */
#entityParameConfig .el-table .cell {
  text-align: left;
}

#entityParameConfig .el-tabs__content {
  height: 280px;
  max-height: 280px;
  overflow: auto;
}

/* 滚动条 */
.el-table__body-wrapper::-webkit-scrollbar {
  width: 5px;
  height: 1px;
}

.el-table__row.hover-row.hover-fixed-row {
  background-color: transparent !important;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 10px;
  box-shadow: inset 0 0 5px var(--border-color);
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background: var(--th-bg-color);
  border-radius: 10px;
  box-shadow: inset 0 0 5px var(--th-bg-color);
}

/* ----------------------------- el-select样式设置 ------------------------- */
.el-select {
  height: 25px !important;
  outline: none;
}

.el-select__wrapper,
.el-select__wrapper.is-disabled {
  min-height: 25px !important;
  color: #fff !important;
  border: var(--border);
  box-shadow: none;
  border-radius: 2px;
  outline: none;
  background-image: linear-gradient(0deg, #073b5a 0%, #022f49 100%);
}

.el-select__placeholder.is-transparent {
  color: #a8abb2 !important;
}

.el-select__selected-item {
  color: #fff !important;
}

.el-select-dropdown {
  color: #fff !important;
  border: none;
}

.el-select-group__title {
  color: #fff;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: var(--border-color);
}

.el-select-dropdown__item.is-hovering {
  background-color: var(--border-color);
}

.el-select-dropdown__item.selected,
.el-select-dropdown__item {
  color: #fff;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
  background: none !important;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: #fff;
  background: none !important;
}

.el-select .el-tag {
  padding: 0 2px !important;
  color: #fff;
  background: rgb(195 248 255 / 30%) !important;
  border: 1px solid #8fbbc1 !important;
  border-radius: 2px !important;
}

.el-select .el-tag__close.el-icon-close {
  color: #fff;
  background: none;
}

.el-select__icon {
  color: var(--font-color) !important;
}

.el-tag--small {
  height: 16px;
  padding: 0;
  line-height: 16px;
}

/* ----------------------------- el-cascader样式设置 ------------------------- */

.el-cascader__dropdown {
  background: var(--panel-bg-color);
  border: none;
}

.el-cascader-menu {
  border-right: 1px solid #8fbbc1;
  color: #fff;
}

.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: var(--selected-font-color);
}

.el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover {
  background: none;
}

/* ----------------------------- el-tooltip样式设置 ------------------------- */
.el-tooltip__popper.is-dark {
  background: #425967 !important;
}

/* ----------------------------- el-pagination样式设置 ------------------------- */

.el-pagination {
  display: flex;
  align-items: center;
  color: #fff;
}

.el-pager li {
  margin: 4px;
  color: #fff;
  background: var(--panel-bg-color) !important;
  border: 1px solid var(--border-color) !important;
}

.el-pager li.is-active {
  background: #145d89 !important;
  border: var(--border) !important;
}

.el-pagination button {
  background: var(--panel-bg-color) !important;
  border: 1px solid var(--border-color) !important;
}

.el-pagination button,
.el-pagination span:not([class*='suffix']) {
  color: #fff;
}

.el-pagination button:disabled,
.el-pagination .btn-next,
.el-pagination .btn-prev {
  color: #fff;
  background-color: var(--input-background);
}

/* ----------------------------- el-button样式设置 ------------------------- */
.el-button {
  height: 29px;
  min-width: 70px;
  color: #fff;
  background: url('@/assets/images/button-bg.png') no-repeat 0 0 / 100% 100% !important;
  border: 0 !important;
  border-radius: 0 !important;
}

.el-button:focus {
  outline: none;
}

.el-dialog__headerbtn {
  top: 5px;
  width: 34px;
  height: 25px;
}

.el-dialog__close {
  font-size: 24px !important;
  color: var(--icon-color) !important;
}

.el-dialog__headerbtn:focus,
.el-dialog__headerbtn:focus-visible {
  outline: none;
}

.el-button.is-text {
  color: #fff;
}

.el-button--primary,
.el-button--default {
  background: none;
}

.el-button:focus,
.el-button:hover {
  // color: #fff;
  // background: url('@/assets/images/button-bg.png') no-repeat 0 0 / 100% 100% !important;
  // border: 0 !important;
}

.el-button.is-plain:focus,
.el-button.is-plain:hover {
  background: url('@/assets/images/button-bg.png') no-repeat 0 0 / 100% 100% !important;
  border: 0 !important;
}

/* -----------------------------el-radion样式设置 ------------------------- */
.el-radio__inner {
  background: var(--border-color);
  border: none !important;
}

.el-radio__input.is-checked + .el-radio__label {
  color: var(--border-color);
}

.el-radio__input.is-checked .el-radio__inner {
  background-color: var(--border-color);
}

.el-radio {
  color: var(--border-color);
}

/* -----------------------------el-from样式设置 ------------------------- */
.el-form-item {
  margin: 5px 0;
}

.el-form-item__content {
  display: flex;
  line-height: 20px;
}

.el-form-item__label {
  font-size: 15px;
  line-height: 30px;
  color: #fff;
}

.el-form-item__error {
  display: none;
}

/* -----------------------------el-picker日期时间选择器样式 ------------------------- */
.el-date-editor .el-range__icon,
.el-range__close-icon {
  line-height: 25px !important;
}

.el-date-editor.el-input__wrapper {
  box-shadow: none;
  border: var(--border);
}

.el-date-editor .el-range-input {
  color: #fff;
}

.el-date-table td.in-range .el-date-table-cell {
  background-color: var(--title-bg-color);
}

.el-picker-panel {
  color: #fff;
  background-color: var(--panel-bg-color);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(6px);
}

.el-date-table th {
  color: var(--border-color);
}

.el-picker-panel .el-date-table th {
  border-bottom: 1px solid var(--border-color);
}

.el-time-spinner__item :hover {
  background-color: var(--title-bg-color) !important;
}

.el-date-table td.in-range div,
.el-date-table td.in-range div:hover,
.el-date-table.is-week-mode .el-date-table__row.current div,
.el-date-table.is-week-mode .el-date-table__row:hover div {
  background-color: var(--border-color);
}

.el-date-picker__header-label {
  color: var(--border-color) !important;
}

.el-picker-panel__icon-btn {
  color: #fff;
}

.el-picker-panel__footer {
  background-color: transparent;
  border-top: 1px solid var(--border-color);
}

.el-picker-panel__footer .is-plain,
.el-picker-panel__footer .is-text {
  color: #fff;
  background-color: transparent;
  border: var(--border);
}

.el-time-panel {
  background-color: var(--panel-bg-color);
}

.el-time-spinner__item.active:not(.disabled) {
  color: #fff;
}

/* 没选中的小时，分钟的hover效果 */
.el-time-spinner__item:hover:not(.disabled, .active) {
  background-color: var(--title-bg-color) !important;
}

.el-time-panel__btn {
  color: #fff;
}

/* -----------------------------el-tree样式 ------------------------- */
#app .el-tree {
  margin-top: 5px;
  font-size: 16px;
  color: var(--font-color);
  background: none;
}

.el-tree-node__content:hover {
  background-color: #005d93;
}

.el-tree-node:focus > .el-tree-node__content {
  background-color: var(--entity-active-bg-color);
}

.tree-icon {
  padding: 0 5px;
}

.el-tree.is-dragging.is-drop-not-allow .el-tree-node__content {
  cursor: pointer !important;
}

/* -----------------------------el-switch样式 ------------------------- */
.el-switch__core {
  background-color: #0c578393;
  border-color: var(--border-color);
}

.el-switch__label {
  font-family: Medium;
  color: #fff;
}

.el-switch__label.is-active {
  font-size: 15px;
  color: #21aeff;
}

.el-switch__core .el-switch__action {
  background-color: #21aeff;
}

.el-switch.is-checked .el-switch__core {
  border-color: var(--border-color);
  background-color: #0c578393;
}

/* --------------------------el-tab样式设置------------------------ */
.el-tabs {
  --el-tabs-header-height: 30px;
}

.el-tabs--card .el-tab-pane {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
}

.el-tabs--card .el-tab-pane::-webkit-scrollbar {
  width: 5px;
  height: 1px;
}

.el-tabs--card .el-tab-pane::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 10px;
  box-shadow: inset 0 0 5px var(--border-color);
}

.el-tabs--card .el-tab-pane::-webkit-scrollbar-track {
  background: rgb(255 255 255 / 30%);
  border-radius: 10px;
  box-shadow: none;
}

.el-tabs--card {
  height: 100%;
}

.el-tabs--card .el-tabs__header {
  margin: 0;
  border: none;
}

.el-tabs__nav-next,
.el-tabs__nav-prev {
  margin: 0 3px;
  line-height: 30px;
  color: #fff;
}

.el-tabs--card .el-tabs__nav {
  display: flex;
  height: 26px;
  line-height: 26px;
  border: transparent !important;
}

.el-tabs__nav.is-top {
  border-bottom: 1px solid var(--title-bg-color) !important;
}

.el-tabs--card .el-tabs__item {
  height: 26px;
  line-height: 26px;
  color: #fff !important;
  flex: 1;
}

.el-tabs--card .el-tabs__item:nth-child(2) {
  border-left: none !important;
}

.el-tabs--card .el-tabs__item.is-active {
  // color: var(--border-color) !important;
  // background: linear-gradient(to top, rgb(13 145 233 / 50%), transparent);
  border: none !important;
}

.el-tabs--card .el-tabs__content {
  height: 100%;
}

.el-tabs__item:nth-child(-n + 2) .el-icon-close {
  display: none;
}

.el-tabs__item {
  font-size: 16px;
  color: #fff;
  border: none !important;
}

.el-tabs__active-bar {
  background-color: transparent !important;
}

.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background-color: transparent;
}

.el-table .hidden {
  display: none !important;
}

.el-table thead.is-group th {
  padding: 0;
  background: none;
}

.el-table thead.is-group tr:first-of-type th:first-of-type {
  border-bottom: none;
}

.el-table thead.is-group tr:first-of-type th:first-of-type div.cell {
  text-align: right;
}

.el-table thead.is-group tr:last-of-type th:first-of-type div.cell {
  text-align: left;
}

.el-table thead.is-group tr:first-of-type th:first-of-type::before {
  position: absolute;
  left: 0;
  display: block;
  width: 1px;
  height: 80px;
  background-color: #08528f;
  content: '';
  opacity: 0.2;
  transform: rotate(287deg);
  transform-origin: top;
}

.el-table thead.is-group tr:last-of-type th:first-of-type::before {
  position: absolute;
  top: -62px;
  right: 0;
  display: block;
  width: 1px;
  height: 85px;
  background-color: #08528f;
  content: '';
  opacity: 0.2;
  transform: rotate(287deg);
  transform-origin: bottom;
}

.el-cascader__suggestion-item:focus,
.el-cascader__suggestion-item:hover {
  background: #324a59;
}

.el-textarea__inner {
  color: var(--title-color);
  background-image: linear-gradient(0deg, #073b5a 0%, #022f49 100%);
  border: var(--border) !important;
  box-shadow: none;
  border-radius: 2px;
}

.el-textarea__inner:hover {
  border: 0 !important;
  box-shadow: 0 0 0 1px #02e0ff inset;
}

.el-drawer {
  background-color: #0d3370;

  .el-drawer__title {
    color: #ccc;
  }
}

.el-collapse {
  --el-collapse-header-bg-color: transparent;
  --el-collapse-border-color: transparent;

  border: none;
}

.el-collapse-item__header {
  padding-left: 8px;
  color: #fff;
}

.el-collapse-item__wrap {
  background-color: var(--panel-bg-color);
  border: var(--border);
}

.el-color-dropdown__btns {
  display: flex !important;
  color: #fff;
}

.el-color-dropdown__value {
  flex: 1;
  margin-right: 10px;
}

.el-color-dropdown__btns .el-color-dropdown__link-btn {
  display: none;
}
