<!--
 * @Description: 新建模型/新建单元组件
 * @Author: tzs
 * @Date: 2022-06-08 13:57:54
 * @LastEditors: tzs
 * @LastEditTime: 2024-03-12 13:29:55
-->
<script setup>
import {getPlatformTypeTreeAPI, getModelAPI} from '@/api/index';
import {editModelList} from '@/utils/data_process';
import {ref, computed, watch, reactive, onMounted} from 'vue';
const emit = defineEmits(['createModel', 'editAddUnit']);
const props = defineProps({
  unitData: {
    type: [Object, Array],
    default: () => [],
  },
  unitName: {
    type: String,
    default: '',
  },
  dialogType: {
    type: String,
    default: '新建模型',
  },
});
const modelCascader = ref(null);
const state = reactive({
  createModel: true, //是否为新建模型
  modelList: [],
  dialogShow: false, //新建模型/单元弹窗的显隐
  selectModel: '', //选择的模型
  formData: {}, // 选择的模型的参数
  unitForm: {
    name: '',
  },
  cascaderProps: {
    emitPath: false,
    value: 'id',
    children: 'children',
    label: 'name',
    disabled: 'disabled',
  },
  cascaderModelList: [],
});
watch(
  () => state.dialogShow,
  newVal => {
    if (props.dialogType === '编辑单元') state.unitForm.name = props.unitName;
    if (!newVal) clearCascader();
  }
);
//获取模型资源树
const getPlatformTypeTree = async (name = '') => {
  const {
    data: {data: resData},
  } = await getPlatformTypeTreeAPI(name);
  if (resData) {
    editModelList(resData);
    state.cascaderModelList = resData;
  }
};
//级联选择器的选择
const handleChange = async () => {
  console.log(state.selectModel);
  // const selectId = state.selectModel[state.selectModel.length - 1];
};
//确定添加按钮
const createData = async () => {
  // 判断是添加单元还是模型
  if (state.createModel && props.dialogType === '新建模型') {
    const {data} = await getModelAPI(state.selectModel);
    console.log(data);
    emit('createModel', {
      platformTypeId: state.selectModel,
      name: data.name,
      categoryId: data.categoryId,
    });
  } else {
    emit('editAddUnit', state.unitForm.name);
  }
  state.formData = {};
  state.unitForm.name = '';
  state.selectModel = '';
  state.dialogShow = false;
};
//清除复选选择
const clearCascader = () => {
  modelCascader.value.dropDownVisible = false;
  state.selectModel = '';
};
onMounted(() => {
  getPlatformTypeTree();
});
defineExpose({state});
</script>

<template>
  <div class="create-model-collection">
    <el-dialog v-model="state.dialogShow" class="create-dialog" title="单元操作" width="580px" center :modal="false">
      <!-- 切换按钮 -->
      <div class="switch-create-type">
        <el-switch v-model="state.createModel" active-color="#13ce66" inactive-color="#02e0ff" @change="clearCascader"> </el-switch>
      </div>
      <div class="form-content">
        <!-- 新建模型 -->
        <div v-show="state.createModel && dialogType == '新建模型'" class="add-model">
          <span>实体 </span>
          <el-cascader ref="modelCascader" v-model="state.selectModel" :props="state.cascaderProps" :options="state.cascaderModeliLst" @change="handleChange"></el-cascader>
        </div>

        <!-- 新建单元 -->
        <div v-show="!state.createModel || dialogType !== '新建模型'" class="add-unit">
          <el-form :model="state.unitForm" @submit.prevent>
            <el-form-item label="名称">
              <el-input v-model.trim="state.unitForm.name" autocomplete="off" @keyup.enter="createData"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 底部确定取消部分 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="(state.dialogShow = false), (state.formData = {})">取 消</el-button>
          <el-button type="primary" @click="createData">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.switch-create-type {
  position: absolute;
  top: 20px;
  left: 200px;
}

.form-content {
  height: 100px;
}

.add-model {
  color: #c3f8ff;
}
</style>
