/*
 * <AUTHOR> laofan
 * @Date         : 2023-02-24 09:53:51
 * @LastEditors: 老范
 * @LastEditTime: 2024-12-24 16:44:31
 * @Description  : 请填写简介
 */
import {defineConfig} from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import commonjs from 'vite-plugin-commonjs';

export default defineConfig({
  plugins: [vue(), commonjs()],
  build: {
    commonjsOptions: {
      transformMixedEsModules: true,
      ignoreTryCatch: false,
    },
  },
  optimizeDeps: {
    esbuildOptions: {
      define: {
        global: 'globalThis',
      },
    },
  },
  server: {
    hmr: true,
    host: '0.0.0.0',
    port: 8686, //这里自行更改
    open: false, //是否打开浏览器
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
