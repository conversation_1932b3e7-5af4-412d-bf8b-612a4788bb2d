<!--
 * @Description: 全局脚本配置
 * @Author: tzs
 * @Date: 2024-05-24 15:55:22
 * @LastEditors: tzs
 * @LastEditTime: 2024-08-27 14:41:56
-->
<script setup>
import {ref} from 'vue';
import {getGlobalScriptAPI} from '@/api/index';
import {useStateStore} from '@/pinia/state/index';

const stateControler = useStateStore();
const globalScriptData = ref([]);

(async () => {
  const {data} = await getGlobalScriptAPI();
  globalScriptData.value = data;
})();
</script>

<template>
  <el-dialog v-model="stateControler.globalScriptVisble" :close-on-click-modal="false" draggable title="脚本配置" center width="388px">
    <div class="global-script">
      <div class="script-box">
        <p>公共脚本：</p>
        <el-select v-model="stateControler.globalScript" suffix-icon="CaretBottom" multiple clearable collapse-tags>
          <el-option v-for="(item, index) in globalScriptData" :key="index" :title="item.displayName" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </div>
      <div class="script-box">
        <p>本地脚本：</p>
        <el-input v-model="stateControler.localScript" class="local-area" resize="none" type="textarea" />
      </div>
    </div>
  </el-dialog>
</template>

<style lang="less" scoped>
.global-script {
  width: 100%;
  height: 175px;
  padding: 5px;
  font-size: 16px;
  color: #fff;

  .script-box {
    display: flex;
    margin-bottom: 15px;

    p {
      width: 110px;
      margin-top: 8px;
    }

    :deep(.el-textarea__inner) {
      height: 112px;
    }
  }

  .el-select {
    margin-top: 5px;
  }
}
</style>
