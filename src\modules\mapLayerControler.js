/*
 * @Description: 添加地图layer和source
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-05-20 15:51:23
 * @LastEditors: 老范
 * @LastEditTime: 2024-09-23 09:37:44
 *
 */
//! 自定义sector的参数介绍
// 	RANGE        = "rg" //range
// 	ANGLE        = "ag" //angle
// 	YAW          = "yw" //yaw

export const addMapLayer = (map, pulsingDot) => {
  // 想定航路的数据源
  map.getSource('lineTrack')
    ? ''
    : map.addSource('lineTrack', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: [],
        },
      });
  // 想定预演位置的数据源
  map.getSource('previewSource')
    ? ''
    : map.addSource('previewSource', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: [],
        },
      });
  // 卫星轨道source
  map.getSource('satellite-track')
    ? ''
    : map.addSource('satellite-track', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: [],
        },
      });
  // 卫星轨道layer
  map.getLayer('satellite-track-line')
    ? ''
    : map.addLayer({
        id: 'satellite-track-line',
        type: 'line',
        source: 'satellite-track',
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
        },
        paint: {
          'line-color': '#FF0000',
          'line-width': 1,
        },
      });
  if (pulsingDot) {
    map.hasImage('highLight') ? '' : map.addImage('highLight', pulsingDot, {pixelRatio: 3});
  }
  //todo实体图层
  map.getLayer('places')
    ? ''
    : map.addLayer({
        id: 'places',
        type: 'symbol',
        source: 'places',
        layout: {
          'icon-image': '{iconId}',
          'icon-allow-overlap': true,
          'icon-rotate': ['get', 'yaw'],
          'icon-rotation-alignment': 'map',
          'icon-ignore-placement': true,
          'icon-size': 1,
          'text-field': ['get', 'entityDisplayName'],
          'text-font': ['NotoSansSC-Regular'],
          'text-offset': [0, 1.25],
          'text-anchor': 'top',
          'text-allow-overlap': true,
        },
        paint: {
          'icon-color': ['get', 'color'],
          'text-color': '#4d7085',
          'text-halo-color': '#fff',
          'text-halo-width': 0.3,
        },
        filter: ['in', '$type', 'Point'],
      });
  //todo 高亮框图层
  map.getLayer('points')
    ? ''
    : map.addLayer({
        id: 'points',
        type: 'symbol',
        source: 'points',
        layout: {
          'icon-image': 'highLight',
          'icon-allow-overlap': true,
        },
      });
  //todo  想定航路
  map.getLayer('scenario-route')
    ? ''
    : map.addLayer({
        id: 'scenario-route',
        type: 'line',
        source: 'lineTrack',
        layout: {
          'line-join': 'round',
          'line-cap': 'round',
        },
        paint: {
          'line-color': '#ccc',
          // 'line-color': ['case', ['any', ['==', ['get', 'fs'], 'red'], ['==', ['get', 'ForceSideID'], 'red']], '#FF3737', ['any', ['==', ['get', 'fs'], 'blue'], ['==', ['get', 'ForceSideID'], 'blue']], '#00B1FF', '#ffffff'],
          'line-opacity': 0.8,
          'line-width': 2,
        },
      });
  // todo预演实体图标
  map.getLayer('places-entity')
    ? ''
    : map.addLayer({
        id: 'places-entity',
        type: 'symbol',
        source: 'previewSource',
        layout: {
          'icon-image': ['get', 'icon'],
          // 'icon-image': '{ModelType}',
          'icon-allow-overlap': true,
          'icon-rotate': ['get', 'heading'],
          'icon-rotation-alignment': 'map',
          'icon-ignore-placement': true,
          'icon-size': 1,
          // 'text-field': '1',
          'text-field': ['get', 'name'],
          'text-font': ['NotoSansSC-Regular'],
          'text-offset': [0, 1.25],
          'text-anchor': 'top',
          'text-allow-overlap': true, //能否遮挡住其他图标(false会导致闪烁)
          visibility: 'visible',
        },
        paint: {
          'text-color': '#4d7085',
          'text-halo-color': '#fff',
          'text-halo-width': 0.3,
          'icon-color': ['get', 'side'],
        },
      });

  //todo探测特效
  /*
   * @description: 探测特效
   * @param {"sector-stroke-opacity":0.1圆形扫描线, 0.2干扰线, 0.3扇形扫描线,0.4圆形范围} //sector-stroke-opacity 参数为设置特效线的类型
   */
  map.getLayer('disturb-line')
    ? ''
    : map.addLayer({
        id: 'disturb-line',
        type: 'sector',
        source: 'places',
        layout: {
          visibility: 'visible',
        },
        paint: {
          'sector-radius': ['get', 'range'],
          'sector-color': ['get', 'color'],
          'sector-orient': ['get', 'yaw'],
          'sector-open': ['get', 'angle'],
          'sector-opacity': 0.1,
          'sector-stroke-width': 100,
          'sector-stroke-color': '#2eb3a5',
          'sector-stroke-opacity': 0.4,
          'sector-pitch-scale': 'map',
          'sector-pitch-alignment': 'map',
        },
        // filter: [
        //   'all',
        //   ['all', ['==', ['get', 'lt'], 'interferenceEffect']],
        //   ['==', ['get', 'sh'], true],
        // ],
      });

  //todo圆形zone区域
  // {"sector-stroke-opacity":0.1圆形扫描线, 0.2干扰线, 0.3扇形扫描线,0.4圆形范围}
  map.getLayer('zone')
    ? ''
    : map.addLayer({
        id: 'zone',
        type: 'sector',
        source: 'places',
        layout: {
          visibility: 'visible',
        },
        paint: {
          'sector-radius': ['get', 'maximumRadius'],
          'sector-color': ['get', 'color'],
          'sector-orient': ['get', 'heading'],
          'sector-open': ['get', 'angle'],
          'sector-opacity': 0.5,
          'sector-stroke-width': 100,
          'sector-stroke-color': ['get', 'color'],
          'sector-stroke-opacity': 0.4,
          'sector-pitch-scale': 'map',
          'sector-pitch-alignment': 'map',
        },
      });

  //todo 添加路网区域
  map.getLayer('road-network')
    ? ''
    : map.addLayer({
        id: 'road-network',
        type: 'line',
        source: 'places',
        paint: {
          'line-width': 2,
          'line-opacity': 1,
          'line-color': '#f00',
        },
      });
};
