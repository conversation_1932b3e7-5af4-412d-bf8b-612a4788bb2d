:root {
  --primary-color: #2563eb;
  --secondary-color: #3b82f6;
  --background-light: #f9fafb;
  --background-dark: #1e293b;
  --text-light: #1f2937;
  --text-dark: #f8fafc;
  --success-color: #10b981;
  --error-color: #ef4444;
  --code-bg-light: #f8f8f8;
  --code-bg-dark: #2d2d2d;
}

.dark-mode {
  --background-light: #1e293b;
  --background-dark: #022e48;
  --text-light: #f8fafc;
  --text-dark: #cbd5e1;
  --code-bg-light: #2d2d2d;
  --code-bg-dark: #1a1a1a;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', system-ui, sans-serif;
}

.iframe-container {
  background-color: var(--background-light);
  color: var(--text-light);
  line-height: 1.6;
  transition: all 0.3s ease;
  min-height: 80vh;
}

.iframe-container .dark-mode {
  background-color: var(--background-dark);
  color: var(--text-dark);
}

.container {
  max-width: 100vw;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  min-height: 100vh;
}

.iframe-container .dark-mode header {
  background: linear-gradient(135deg, #05507b, #295167);

  color: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  position: relative;
}
header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.main-content {
  width: 90vw;
  display: flex;
  align-items: flex-start;
  gap: 30px;
  flex: 1;
}

.chat-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 2;
  order: 1;
  box-shadow: 0px 0px 3px 3px rgba(0, 0, 0, 0.1);
}
.iframe-container .dark-mode .settings-panel {
  background-color: #05507b;
  flex: 1;
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 14px 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  /* border-top: 2px solid rgba(37, 99, 235, 0.3); */
  order: 2;
  flex-shrink: 0;
  z-index: 10;
  align-self: center;
}
.settings-panel {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 14px 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  /* border-top: 2px solid rgba(37, 99, 235, 0.3); */
  order: 2;
  flex-shrink: 0;
  z-index: 10;
  align-self: center;
}

.input-group {
  flex: 2;
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.input-textarea {
  width: 100%;
  padding: 15px;
  padding-right: 80px; /* 为按钮留出空间 */
  border: 2px solid #e2e8f0;
  border-radius: 18px;
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  transition: border-color 0.3s, box-shadow 0.3s;
  resize: none;
  /* line-height: 1.5em; */
  /* max-height: calc(1.5em * 9); */
  overflow-y: auto;
  box-sizing: border-box;
}

.iframe-container .dark-mode .input-textarea {
  border: 2px solid #476081;
  background-color: #05507b;
  color: var(--text-light);
}

@media (max-width: 780px) {
  .input-textarea {
    width: 100vw !important;
    padding: 5px;
    max-height: calc(1em * 9);
    max-width: 100vw !important;
    min-width: 0 !important;
    box-sizing: border-box;
    padding-right: 80px;
  }
}

textarea:focus {
  outline: none;
  /* border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2); */
}

button {
  /* padding: 12px 24px; */
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

button:disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
  transform: none;
}

.stop-btn {
  background-color: var(--error-color);
}

.control-group {
  flex: 1;
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.answer-container {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 25px;
  height: 66vh;
  overflow-y: auto;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  font-size: 16px;
  line-height: 1.8;
  position: relative;
  /* flex: 1; */
  scroll-behavior: smooth;
}

/* 自定义滚动条样式 */
.answer-container::-webkit-scrollbar {
  width: 8px;
}

.iframe-container .dark-mode .answer-container::-webkit-scrollbar-track {
  background: #05507b;
  border-radius: 4px;
}
.answer-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.answer-container::-webkit-scrollbar-thumb {
  background: rgba(37, 99, 235, 0.5);
  border-radius: 4px;
}

.answer-container::-webkit-scrollbar-thumb:hover {
  background: rgba(37, 99, 235, 0.7);
}

.answer-container.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  animation: loading 1.5s infinite;
  border-radius: 3px;
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.iframe-container .dark-mode .response-stats {
  display: flex;
  justify-content: space-between;
  color: #ffffff;
  font-size: 14px;
  margin-top: 10px;
}
.response-stats {
  display: flex;
  justify-content: space-between;
  color: #64748b;
  font-size: 14px;
  margin-top: 10px;
}

.model-info {
  padding: 10px;
  background-color: rgba(37, 99, 235, 0.1);
  border-radius: 8px;
  text-align: center;
  margin-top: 20px;
}

.security-note {
  background-color: rgba(239, 68, 68, 0.1);
  border-left: 4px solid var(--error-color);
  padding: 10px;
  margin: 15px 0;
  border-radius: 0 4px 4px 0;
}

.reference {
  background-color: rgba(156, 163, 175, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  vertical-align: super;
  cursor: pointer;
}

pre {
  background-color: var(--code-bg-light);
  padding: 15px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 15px 0;
  position: relative;
}

.iframe-container .dark-mode pre {
  background-color: var(--code-bg-dark);
}

.code-block {
  position: relative;
}

.copy-btn {
  position: absolute;
  top: 30px;
  right: 30px;
  padding: 5px 10px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-light);
}

.iframe-container .dark-mode .copy-btn {
  color: var(--text-dark);
}

.api-status {
  display: flex;
  align-items: center;
  margin-top: 15px;
  padding: 8px;
  border-radius: 6px;
  background-color: rgba(16, 185, 129, 0.1);
}

.api-status .indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--success-color);
  margin-right: 10px;
}

.api-status .text {
  font-size: 14px;
}

.security-badge {
  display: inline-block;
  background: var(--success-color);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  margin-left: 10px;
}

/* Markdown样式增强 */
.answer-container h1,
.answer-container h2,
.answer-container h3 {
  margin: 1.5em 0 0.8em 0;
  padding-bottom: 0.3em;
  border-bottom: 1px solid rgba(156, 163, 175, 0.3);
}

.answer-container h1 {
  font-size: 1.8em;
}

.answer-container h2 {
  font-size: 1.5em;
}

.answer-container h3 {
  font-size: 1.3em;
}

.answer-container p {
  margin: 1em 0;
}

.answer-container ul,
.answer-container ol {
  margin: 1em 0;
  padding-left: 2em;
}

.answer-container li {
  margin: 0.5em 0;
}

.answer-container blockquote {
  border-left: 4px solid var(--primary-color);
  padding: 0.5em 1em;
  margin: 1em 0;
  background-color: rgba(37, 99, 235, 0.05);
  border-radius: 0 4px 4px 0;
}

.answer-container table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
}

.answer-container th,
.answer-container td {
  border: 1px solid rgba(156, 163, 175, 0.3);
  padding: 0.5em 1em;
  text-align: left;
}

.answer-container th {
  background-color: rgba(37, 99, 235, 0.1);
  font-weight: 600;
}

.answer-container code:not(pre code) {
  background-color: rgba(156, 163, 175, 0.2);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-family: monospace;
}

.answer-container a {
  color: var(--primary-color);
  text-decoration: none;
}

.answer-container a:hover {
  text-decoration: underline;
}

footer {
  text-align: center;
  padding: 5px 20px;
  color: #64748b;
  font-size: 14px;
  margin-top: auto;
}

/* Element Plus 暗色主题适配 */
.iframe-container .dark-mode .el-select {
  --el-text-color-primary: #f8fafc;
  --el-text-color-regular: #cbd5e1;
  --el-border-color: #475569;
  --el-border-color-hover: #64748b;
  --el-fill-color-blank: #334155;
  --el-bg-color: #1e293b;
  --el-bg-color-overlay: #0f172a;
}

.iframe-container .dark-mode .el-select .el-input__wrapper {
  background-color: #05507b;
  border-color: #475569;
  box-shadow: 0 0 0 1px #475569 inset;
}

.iframe-container .dark-mode .el-select .el-input__wrapper:hover {
  border-color: #64748b;
  box-shadow: 0 0 0 1px #64748b inset;
}

.iframe-container .dark-mode .el-select .el-input__wrapper.is-focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

.iframe-container .dark-mode .el-select .el-input__inner {
  color: #f8fafc;
  background-color: transparent;
}

.iframe-container .dark-mode .el-select-dropdown {
  background-color: #1e293b;
  border-color: #475569;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

.iframe-container .dark-mode .el-select-dropdown__item {
  color: #cbd5e1;
}

.iframe-container .dark-mode .el-select-dropdown__item:hover {
  background-color: #334155;
}

.iframe-container .dark-mode .el-select-dropdown__item.selected {
  background-color: rgba(37, 99, 235, 0.2);
  color: #f8fafc;
}

.iframe-container .dark-mode .el-popper.is-dark {
  background-color: #1e293b;
  border-color: #475569;
}

.iframe-container .dark-mode .el-popper.is-dark .el-popper__arrow::before {
  background-color: #1e293b;
  border-color: #475569;
}

/* Element Plus 图标暗色主题 */
.iframe-container .dark-mode .el-select .el-select__caret {
  color: #cbd5e1;
}

.iframe-container .dark-mode .el-select .el-select__caret:hover {
  color: #f8fafc;
}

/* Element Plus 占位符文本暗色主题 */
.iframe-container .dark-mode .el-select .el-input__inner::placeholder {
  color: #64748b;
}
.iframe-container .dark-mode .el-select .el-select__wrapper {
  box-shadow: 0 0 0 1px #1c8fd0 inset;
  background-image: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%);
  border-radius: 0;
}
.iframe-container .dark-mode .el-select .el-select__selected-item span {
  line-height: 2;
  color: var(--font-color-white);
}
.iframe-container .dark-mode .el-select .el-select__selected-item .el-tag {
  background: #0d3a63;
}
.iframe-container .dark-mode .el-select .el-select__selected-item .el-tag {
  background: #0d3a63;
}
.iframe-container .dark-mode .el-select .el-select__selected-item .el-tag__content .el-select__tags-text {
  color: #00e0ff;
}

.iframe-container .dark-mode .el-select .el-select__selection {
  .el-tag {
    background: #1287cc;
  }
}

.iframe-container .dark-mode .el-select .el-select__wrapper.is-hovering:not(.is-focused) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

.iframe-container .dark-mode .el-scrollbar {
  --el-scrollbar-opacity: 1;
  --el-scrollbar-bg-color: #097cbc;
  --el-scrollbar-hover-opacity: 1;
  --el-scrollbar-hover-bg-color: #097cbc;
}
/* .iframe-container .dark-mode .el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: var(--select-hover-color);
} */
.el-select-dropdown__item.is-hovering {
  background-color: transparent;
}
.iframe-container .dark-mode .el-select-dropdown__item.is-hovering {
  background-color: #0a79b8;
}
/* 响应式设计优化 */
@media (max-width: 768px) {
  .container {
    padding: 15px;
    gap: 20px;
  }

  .main-content {
    gap: 20px;
    align-items: flex-start;
  }

  .iframe-chat-section .answer-container {
    min-height: 250px;
    max-height: calc(100vh - 300px);
    padding: 20px;
  }

  .settings-panel {
    padding: 15px;
    align-self: center;
  }

  .input-group {
    flex-direction: column;
  }

  .control-group {
    flex-wrap: wrap;
    gap: 8px;
  }

  button {
    padding: 10px 16px;
    font-size: 14px;
  }

  .action-btn {
    width: 60px !important;
  }
}
