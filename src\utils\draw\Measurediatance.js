/*
 * @Author: 老范
 * @Date: 2024-06-25 11:10:26
 * @LastEditors: 老范
 * @LastEditTime: 2024-06-25 11:10:29
 * @Description: 请填写简介
 */
// const turfLength = require('@turf/length').default;
const numeral = require('numeral');
// const turfDistance = require('@turf/distance').default;
const turf = require('@turf/turf');
function getDisplayMeasurements(feature) {
  // should log both metric and standard display strings for the current drawn feature

  // metric calculation
  // 计算总的长度
  const drawnLength = turf.length(feature) * 1000; // meters
  let metricUnits = 'm';
  let metricFormat = '0,0';
  let metricMeasurement;
  metricMeasurement = drawnLength;
  if (drawnLength >= 1000) {
    // if over 1000 meters, upgrade metric
    metricMeasurement = drawnLength / 1000;
    metricUnits = 'km';
    metricFormat = '0.00';
  }
  // 计算当前线段的长度
  let singlemetricUnits = 'm';
  let singlemetricFormat = '0,0';
  let singleLength;
  const length = feature.geometry.coordinates.length;
  const nextPoint = feature.geometry.coordinates[length - 1]; //倒数第一个点
  let prePoint = feature.geometry.coordinates[length - 2]; //倒数第二个点
  let isClick = false;
  if (nextPoint[0] == prePoint[0] && nextPoint[1] == prePoint[1] && length > 2) {
    prePoint = feature.geometry.coordinates[length - 3];
    isClick = true;
  }
  const from = turf.point(prePoint);
  const to = turf.point(nextPoint);

  singleLength = turf.distance(from, to) * 1000;
  if (singleLength >= 1000) {
    // if over 1000 meters, upgrade metric
    singleLength = singleLength / 1000;
    singlemetricUnits = 'km';
    singlemetricFormat = '0.00';
  }
  // 计算两点间与正北方向的角度
  const bearing = turf.bearing(from, to);

  // 计算两条线之间的角度
  // 能进入这个函数的肯定是大于两个点的
  let segAngle = 0;
  if (length > 2) {
    let pre = turf.point(feature.geometry.coordinates[length - 3]);
    if (isClick && length > 3) {
      pre = turf.point(feature.geometry.coordinates[length - 4]);
    }
    const preBearing = turf.bearing(from, pre);
    // 如果符号不一样 说明在正北的两侧
    if ((bearing > 0 && preBearing < 0) || (bearing < 0 && preBearing > 0)) {
      segAngle = Math.abs(bearing) + Math.abs(preBearing);
      if (segAngle > 180) segAngle = 360 - segAngle;
    } else {
      segAngle = Math.abs(preBearing - bearing);
    }
  }

  // 计算每一段线段的长度
  const lines = [];
  if (length > 2) {
    let everymetricUnits = 'm';
    let everyemetricFormat = '0,0';
    for (let i = 0; i <= feature.geometry.coordinates.length - 3; i++) {
      everymetricUnits = 'm';
      everyemetricFormat = '0,0';
      const from = turf.point(feature.geometry.coordinates[i]);
      const to = turf.point(feature.geometry.coordinates[i + 1]);
      const next = turf.point(feature.geometry.coordinates[i + 2]);
      const midpoint = turf.midpoint(from, to);
      let length = turf.distance(from, to) * 1000;
      const bearing = turf.bearing(to, next);
      const preBearing = turf.bearing(to, from);
      let yaw = 0;
      if ((bearing > 0 && preBearing < 0) || (bearing < 0 && preBearing > 0)) {
        yaw = Math.abs(bearing) + Math.abs(preBearing);
        if (yaw > 180) yaw = 360 - yaw;
      } else {
        yaw = Math.abs(preBearing - bearing);
      }
      if (length >= 1000) {
        length = length / 1000;
        everymetricUnits = 'km';
        everyemetricFormat = '0.00';
      }
      const tempObj = {
        length: `${numeral(length).format(everyemetricFormat)} ${everymetricUnits}`,
        coordinates: midpoint.geometry.coordinates,
        yaw: `${numeral(yaw).format(0.0)}°`,
        yawPoint: feature.geometry.coordinates[i + 1],
      };
      lines.push(tempObj);
    }
  }

  const displayMeasurements = {
    metric: `${numeral(metricMeasurement).format(metricFormat)} ${metricUnits}`,
    single: `${numeral(singleLength).format(singlemetricFormat)} ${singlemetricUnits}`,
    angle: `${numeral(bearing).format(0.0)}°`,
    segAngle: `${numeral(segAngle).format(0.0)}°`,
    lines: lines,
  };
  return displayMeasurements;
}

module.exports = {
  getDisplayMeasurements,
};
