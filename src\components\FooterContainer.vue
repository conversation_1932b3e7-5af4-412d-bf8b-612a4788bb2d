<!--
 * @Description: 底部
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-15 18:50:43
-->
<script setup lang="ts">
import {ref} from 'vue';
import {useMapStore} from '@/pinia/map/index';
import {useStateStore} from '@/pinia/state/index';

import {storeToRefs} from 'pinia';
import bus from '@/utils/bus';

const {mapInstance} = storeToRefs(useMapStore());
const stateControler = useStateStore();
const useLngLat = () => {
  const longitude = ref('');
  const latitude = ref('');
  bus.on('mapLoaded', () => {
    mapInstance.value.getLngLat((data: {lng: number; lat: number}) => {
      const {lng, lat} = data;
      longitude.value = lng.toFixed(6);
      latitude.value = lat.toFixed(6);
    });
  });

  return {longitude, latitude};
};
const draw_measure = () => {
  mapInstance.value.draw_measure();
  mapInstance.value.mapDrawState = `测距`;
  const drawLineBtn = document.querySelector('.mapbox-gl-draw_line');
  const drawPointBtn = document.querySelector('.mapbox-gl-draw_point');
  drawLineBtn!.classList.remove('drawActive');
  drawPointBtn!.classList.remove('drawActive');
};
const {longitude, latitude} = useLngLat();

const drawCircle = () => {
  mapInstance.value.drawInstance.changeMode('drag_circle');
  // mapInstance.value.drawInstance.changeMode('draw_circle');
  mapInstance.value.mapDrawState = '绘制圆';
};
</script>
<template>
  <div class="footer">
    <!--绘制完成提示内容-->
    <div v-show="stateControler.drawTipsShow" class="draw-tips">
      <div id="calculatedArea" v-html="stateControler.shapeInfo"></div>
    </div>

    <!-- 工具栏 -->
    <div class="openControls">
      <div @click="stateControler.controlModel = !stateControler.controlModel">
        <el-icon> <View v-if="!stateControler.controlModel" /><Hide v-else /> </el-icon>
        <span>资源库</span>
      </div>
      <div @click="stateControler.controlEntity = !stateControler.controlEntity">
        <el-icon><View v-if="!stateControler.controlEntity" /><Hide v-else /></el-icon>
        <span>兵力部署</span>
      </div>
      <!-- <div @click="stateControler.groupVisible = !stateControler.groupVisible">
        <el-icon><View v-if="!stateControler.groupVisible" /><Hide v-else /></el-icon>
        <span>编组设计</span>
      </div> -->
    </div>
    <div class="configControl">
      <div @click="stateControler.controlLayer = !stateControler.controlLayer">
        <i class="icon map"></i>
        <span>地图图源</span>
      </div>
      <div @click="stateControler.globalScriptVisble = !stateControler.globalScriptVisble">
        <i class="icon script-config"></i>
        <span>脚本配置</span>
      </div>
      <div>
        <i class="icon area"></i>
        <span @click="stateControler.graphicManagerVisible = !stateControler.graphicManagerVisible">区域管理</span>
      </div>
    </div>
    <div :class="[mapInstance.mapDrawState === '测距' ? 'drawActive' : '', 'rule']" @click="draw_measure"></div>
    <!-- <div :class="[mapInstance.mapDrawState === '绘制圆' ? 'drawActive' : '', 'draw-circle']" @click="drawCircle"></div> -->
    <div v-if="mapInstance.mapDrawState" class="draw-state">当前地图状态：{{ mapInstance.mapDrawState }}</div>

    <!-- 比例尺经纬度 -->
    <div class="map-info">
      <div class="scale">比例：</div>
      <div class="coordinates">
        <div>
          <span>经度:</span>
          <span id="lng">{{ longitude }}</span>
        </div>

        <div>
          <span>纬度:</span>
          <span id="lat"> {{ latitude }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.footer {
  position: fixed;
  bottom: 1px;
  z-index: 1;
  display: flex;
  width: calc(100% - 2px);
  height: 32px;
  font-size: 15px;
  background-color: var(--panel-bg-color);
  border-top: var(--border);
  align-items: center;

  .draw-tips {
    position: absolute;
    right: 456px;
    line-height: 30px;
  }

  .openControls {
    position: relative;
    left: var(--nav-left-distance);
    display: flex;
    width: 245px;
    height: 25px;
    padding: 0;
    align-items: center;
    flex-direction: row;
    border-right: 2px solid var(--split-line-color);

    > div {
      display: flex;
      align-items: center;
      margin: 0 5px;
    }

    span {
      cursor: pointer;
    }

    span:hover {
      color: #339af0;
    }
  }

  .draw-state {
    position: absolute;
    bottom: 4px;
    left: 970px;
    color: red;
  }

  .configControl {
    position: relative;
    left: 0;
    display: flex;
    height: 25px;
    padding: 0;
    align-items: center;
    flex-direction: row;
    border-right: 2px solid var(--split-line-color);

    > div {
      display: flex;
      align-items: center;
      margin: 0 15px;
      cursor: pointer;

      .icon {
        display: inline-block;
        width: 15px;
        height: 15px;
        background-size: contain !important;
        margin: 0 5px;
      }

      .map {
        background: url('@/assets/images/map.png') no-repeat;
      }

      .script-config {
        background: url('@/assets/images/script-config.png') no-repeat;
      }

      .area {
        background: url('@/assets/images/area.png') no-repeat;
      }
    }
  }

  .rule {
    position: absolute;
    bottom: 4px;
    left: 660px;
    width: 29px;
    height: 22px;
    background: url('@/assets/images/rule.png') no-repeat center;
    cursor: pointer;
    background-size: contain;
  }

  .draw-circle {
    position: absolute;
    bottom: 4px;
    left: 700px;
    width: 29px;
    height: 22px;
    text-align: center;
    cursor: pointer;
  }

  .draw-circle::after {
    position: relative;
    left: 3px;
    display: block;
    width: 18px;
    height: 18px;
    border: 1.9px solid #fff;
    border-radius: 50%;
    content: ' ';
  }

  .map-info {
    position: absolute;
    right: 0;
    bottom: 4px;
    display: flex;
    align-items: center;
    align-content: center;

    .scale {
      position: absolute;
      right: 395px;
      width: 55px;
      height: 25px;
      line-height: 25px;
      border-left: 2px solid var(--split-line-color);
    }

    .coordinates {
      display: flex;
      width: 300px;
      align-items: center;
      justify-content: space-around;

      div {
        width: 150px;
        text-align: left;
      }

      div span {
        display: inline-block;
      }

      div span:nth-child(1) {
        width: 40px;
      }

      div span:nth-child(2) {
        width: 40px;
      }
    }
  }

  .el-drawer {
    background-color: #172740b3;
  }
}
</style>
