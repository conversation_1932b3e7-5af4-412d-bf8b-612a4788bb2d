/*
 * @Author: 老范
 * @Date: 2024-06-25 11:10:37
 * @LastEditors: 老范
 * @LastEditTime: 2025-03-21 14:36:51
 * @Description: 请填写简介
 */
// this mode extends the build-in linestring tool, displaying the current length
// of the line as the user draws using a point feature and a symbol layer
// 该功能是通参考的是geojson.io的源码实现的
// 主要功能是实现绘制线的时候实时显示总长度和当前线段的长度以及与正北方向的夹角
const MapboxDraw = require('@mapbox/mapbox-gl-draw');
const {getDisplayMeasurements} = require('./Measurediatance.js');

const ExtendedLineStringMode = {
  ...MapboxDraw.modes.draw_line_string,

  toDisplayFeatures: function (state, geojson, display) {
    // console.log(display, geojson, 'display');

    const isActiveLine = geojson.properties.id === state.line.id;
    geojson.properties.active = isActiveLine ? 'true' : 'false';
    if (!isActiveLine) return display(geojson);
    // Only render the line if it has at least one real coordinate
    if (geojson.geometry.coordinates.length < 2) return;
    geojson.properties.meta = 'feature';
    display({
      type: 'Feature',
      properties: {
        meta: 'vertex',
        parent: state.line.id,
        coord_path: `${state.direction === 'forward' ? geojson.geometry.coordinates.length - 2 : 1}`,
        active: 'false',
      },
      geometry: {
        type: 'Point',
        coordinates: geojson.geometry.coordinates[state.direction === 'forward' ? geojson.geometry.coordinates.length - 2 : 1],
      },
    });

    display(geojson);

    const displayMeasurements = getDisplayMeasurements(geojson);
    // console.log(displayMeasurements, 'displayMeasurements');
    // create custom feature for the current pointer position
    const currentVertex = {
      type: 'Feature',
      properties: {
        meta: 'currentPosition',
        radius: `距离:${displayMeasurements.single}\n角度:${displayMeasurements.segAngle}`,
        parent: state.line.id,
      },
      geometry: {
        type: 'Point',
        coordinates: geojson.geometry.coordinates[geojson.geometry.coordinates.length - 1],
      },
    };
    display(currentVertex);
    if (displayMeasurements.lines.length) {
      displayMeasurements.lines.forEach(line => {
        const feature = {
          type: 'Feature',
          properties: {
            meta: 'currentPosition',
            radius: `距离:${line.length}`,
            parent: state.line.id,
          },
          geometry: {
            type: 'Point',
            coordinates: line.coordinates,
          },
        };
        display(feature);
        const pointfeature = {
          type: 'Feature',
          properties: {
            meta: 'currentPosition',
            radius: `夹角:${line.yaw}`,
            parent: state.line.id,
          },
          geometry: {
            type: 'Point',
            coordinates: line.yawPoint,
          },
        };
        display(pointfeature);
      });
    }
  },
};

module.exports = ExtendedLineStringMode;
