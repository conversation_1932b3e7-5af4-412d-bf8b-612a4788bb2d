<!--
 * @Description: 请填写简介
 * @Author: 老范
 * @Date: 2024-08-28 16:52:22
 * @LastEditors: tzs
 * @LastEditTime: 2024-09-23 17:06:36
-->
<script setup lang="ts">
import {ref} from 'vue';
const dialogVisible = ref(false);
const title = ref('');
const emits = defineEmits(['saveTemplate', 'save', 'notSave']);
const show = (data: {title: string}) => {
  title.value = data.title;
  dialogVisible.value = true;
};
const props = defineProps({
  unCommandEntityList: {
    type: Array,
    default: () => [{label: '123'}, {label: '234'}, {label: '234'}, {label: '234'}, {label: '234'}],
  },
  unNetworkEntityList: {
    type: Array,
    default: () => [{label: '123'}, {label: '234'}, {label: '234'}, {label: '234'}, {label: '234'}],
  },
  noPlanEntities: {
    type: Array,
    default: () => [{label: '123'}, {label: '234'}, {label: '234'}, {label: '234'}, {label: '234'}],
  },
  overlapArea: {
    type: Array,
    default: () => [['1111', '222']],
  },
  noWeaponList: {
    type: Array,
    default: () => ['1111', '222'],
  },
});
const close = () => {
  dialogVisible.value = false;
};
// 保存
const handleSave = () => {
  emits('save');
};
// 不保存
const handlenotSave = () => {
  emits('notSave');
};
defineExpose({
  show,
  close,
});
</script>
<template>
  <el-dialog v-model="dialogVisible" title="校验结果" align-center width="680" show-close class="doubt-dialog">
    <!-- <el-row class="align-items flex-dir-column doubt-box">
      <img src="@/assets/images/warning_img.png" alt="" />
    </el-row> -->
    <el-row class="align-items flex-dir-column doubt-box">
      <div class="lineItem">
        <div class="title">指挥关系</div>
        <div class="valueBox">
          <div class="label">以下实体未出现在指挥链中：</div>
          <div class="data">
            <el-tag v-for="(item, index) in props.unCommandEntityList" :key="index" type="primary" class="dataItem">{{ item.label }}</el-tag>
          </div>
        </div>
      </div>
    </el-row>
    <el-row class="align-items flex-dir-column doubt-box">
      <div class="lineItem">
        <div class="title">通信关系</div>
        <div class="valueBox">
          <div class="label">以下实体未出现在通信关系中：</div>
          <div class="data">
            <el-tag v-for="(item, index) in props.unNetworkEntityList" :key="index" type="primary" class="dataItem">{{ item.label }}</el-tag>
          </div>
        </div>
      </div>
    </el-row>
    <el-row class="align-items flex-dir-column doubt-box">
      <div class="lineItem">
        <div class="title">任务分配情况</div>
        <div class="valueBox">
          <div class="label">以下实体未进行任务分配：将不进行任务分配!</div>
          <div class="data">
            <el-tag v-for="(item, index) in props.noPlanEntities" :key="index" type="primary" class="dataItem">{{ item }}</el-tag>
          </div>
        </div>
      </div>
    </el-row>
    <el-row v-show="overlapArea.length > 0" class="align-items flex-dir-column doubt-box">
      <div class="lineItem">
        <div class="title">安全冲突</div>
        <div class="valueBox">
          <div class="label">以下区域重合，可能存在安全冲突!</div>
          <div class="data">
            <el-tag v-for="(item, index) in overlapArea" :key="index" type="primary" class="dataItem">{{ item[0] + '和' + item[1] }}</el-tag>
          </div>
        </div>
      </div>
    </el-row>
    <el-row class="align-items flex-dir-column doubt-box">
      <div class="lineItem">
        <div class="title">武器装备状态</div>
        <div class="valueBox">
          <div class="label">以下实体未进行武器配置或未打开weapon</div>
          <div class="data">
            <el-tag v-for="(item, index) in noWeaponList" :key="index" type="primary" class="dataItem">{{ item }}</el-tag>
          </div>
        </div>
      </div>
    </el-row>
    <!-- <el-row class="align-items flex-dir-column doubt-box">
      <el-button @click="dialogVisible = false">想定脚本文件校验</el-button>
    </el-row> -->
    <el-row class="align-items flex-dir-column doubt-box">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </el-row>
  </el-dialog>
</template>
<style lang="less" scoped>
.doubt-dialog {
  .doubt-box {
    padding: 10px 5px 15px;
    color: #fff;
    background: var(--panel-bg-color);

    .lineItem {
      display: flex;
      width: 80%;
      //   height: 50px;
      //   line-height: 50px;
      min-height: 30px;
      padding: 10px;
      border: 1px dashed #484747;

      .title {
        width: 100px;
        text-align-last: left;
        margin-left: 20px;
      }

      .valueBox {
        display: flex;
        width: 20.8333vw;
        flex-direction: column;
        align-items: flex-start;

        .label {
          font-size: 12px;
          color: antiquewhite;
        }

        .data {
          display: flex;
          max-height: 120px;
          overflow: scroll;
          flex-wrap: wrap;

          .dataItem {
            margin: 5px;
          }
        }
      }
    }

    .doubt-text {
      margin: 20px 0;
      /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
      font-family: Regular;
      line-height: 1.2;
      text-align: center;
    }
  }
}
</style>
