<!--
 * @Author: 老范
 * @Date: 2025-06-09 15:12:48
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-02 14:56:57
 * @Description: 请填写简介
-->
<template>
  <section class="settings-panel">
    <!-- 文件列表，宽度为输入框的1/4，显示在输入框上方 -->
    <div v-if="selectedFiles.length" class="file-list">
      <div v-for="(file, idx) in selectedFiles" :key="file.name + file.size" class="file-item">
        <span class="file-name" :title="file.name">{{ file.name }}</span>
        <button class="remove-file-btn" title="移除" @click="removeFile(idx)">×</button>
      </div>
    </div>
    <!-- 输入框及操作按钮 -->
    <div class="input-container">
      <textarea ref="textareaRef" v-model="chatStore.question" :disabled="chatStore.isLoading" placeholder="请输入你的问题..." class="input-textarea" :rows="textareaRows" @keydown="handleKeydown"></textarea>
      <div class="input-actions-row">
        <button class="action-btn upload-btn" title="上传附件" @click="triggerFileInput">
          <i class="iconfont iconupload"></i>
        </button>
        <input ref="fileInput" type="file" multiple style="display: none" accept=".md,.txt,.doc,.docx,text/markdown,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document" @change="handleFileChange" />
        <button
          class="action-btn send-btn"
          :class="{
            disabled: !chatStore.question.trim() && !selectedFiles.length,
            active: chatStore.question.trim() || selectedFiles.length,
            stop: chatStore.isLoading,
          }"
          :disabled="!chatStore.question.trim() && !selectedFiles.length"
          @click="handleSendClick"
        >
          <span v-if="!chatStore.isLoading">发送</span>
          <span v-else>停止</span>
        </button>
      </div>
    </div>
  </section>
</template>

<script setup>
import {useChatStore} from '@/pinia/chat';
import {useChat} from '@/composables/useChat';
import {ref, watch, nextTick, onMounted, onBeforeUnmount} from 'vue';

const chatStore = useChatStore();
const {askQuestion, stopResponse} = useChat();

const fileInput = ref(null);
const selectedFiles = ref([]); // 支持多个文件
const textareaRef = ref(null);
const textareaRows = ref(29); // 减少行数以适配 iframe

const triggerFileInput = () => {
  fileInput.value && fileInput.value.click();
};

const ALLOWED_EXTS = ['md', 'txt', 'doc', 'docx'];
const ALLOWED_MIME = ['text/markdown', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

const handleFileChange = event => {
  const files = Array.from(event.target.files);
  files.forEach(file => {
    const ext = file.name.split('.').pop().toLowerCase();
    if (ALLOWED_EXTS.includes(ext) || ALLOWED_MIME.includes(file.type)) {
      if (!selectedFiles.value.some(f => f.name === file.name && f.size === file.size)) {
        selectedFiles.value.push(file);
      }
    } else {
      alert(`文件 ${file.name} 格式不支持，仅支持md、txt、doc、docx`);
    }
  });
  event.target.value = ''; // 允许重复选择同一文件
};

const removeFile = idx => {
  selectedFiles.value.splice(idx, 1);
};

const handleSendClick = async () => {
  console.log('🚀 ~ handleSendClick ~ chatStore.isLoading:', chatStore.isLoading);
  if (chatStore.isLoading) {
    stopResponse();
    return;
  }
  if (!chatStore.question.trim()) return;

  try {
    await askQuestion(selectedFiles.value);
  } catch (err) {
    alert('发送失败: ' + err.message);
    return;
  } finally {
    selectedFiles.value = [];
    fileInput.value.value = '';
  }
};

const adjustTextareaHeight = () => {
  const el = textareaRef.value;
  if (!el) return;
  el.style.height = 'auto';
  // 计算最大高度（7行）
  const lineHeight = parseFloat(getComputedStyle(el).lineHeight) || 29;
  const maxHeight = lineHeight * 29;
  // el.style.height = Math.min(el.scrollHeight, maxHeight) + 'px';
};

watch(
  () => chatStore.question,
  () => nextTick(adjustTextareaHeight)
);

const handleKeydown = event => {
  // 如果按下回车键且没有按住Shift键，并且有内容且不在加载状态
  if (event.key === 'Enter' && !event.shiftKey && !chatStore.isLoading) {
    // 检查是否有内容（文本或文件）
    if (chatStore.question.trim() || selectedFiles.value.length > 0) {
      event.preventDefault(); // 阻止默认的换行行为
      handleSendClick();
    }
  }
};

function updateRows() {
  // 检测父容器 iframe-container 的宽度
  const iframeContainer = document.querySelector('.iframe-container');
  if (iframeContainer) {
    const containerWidth = iframeContainer.clientWidth;
    textareaRows.value = containerWidth < 780 ? 8 : 29;
  } else {
    // 如果没有找到 iframe-container，使用 window 宽度作为后备
    textareaRows.value = window.innerWidth < 780 ? 8 : 29;
  }
}

let resizeObserver = null;

onMounted(() => {
  updateRows();
  window.addEventListener('resize', updateRows);

  // 监听 iframe-container 尺寸变化
  const iframeContainer = document.querySelector('.iframe-container');
  if (iframeContainer && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateRows();
    });
    resizeObserver.observe(iframeContainer);
  }
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateRows);
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});
</script>

<style scoped>
.settings-panel {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

/* iframe 内的设置面板样式 */
.iframe-main-content .settings-panel {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  align-self: flex-start;
  max-height: 100%;
  overflow-y: auto;
}

/* iframe 内的输入框样式调整 */
.iframe-main-content .input-textarea {
  font-size: 14px;
  padding: 10px;
  border-radius: 12px 12px 0 0;
}

.iframe-main-content .input-actions-row {
  padding: 5px 10px 8px 10px;
  border-radius: 0 0 12px 12px;
}

.input-container {
  width: 100%;
}

.input-textarea {
  width: 100%;
  padding: 15px 15px 0px 15px;
  border: 2px solid #e2e8f0;
  border-bottom: none;
  border-radius: 18px 18px 0 0;
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.7);
  color: var(--text-light);
  transition: border-color 0.3s, box-shadow 0.3s;
  resize: none;
  box-sizing: border-box;
}
body.dark-mode .input-textarea {
  border: 2px solid #476081;
  border-bottom: none;
  background-color: #05507b;
  color: var(--text-light);
}
.input-actions-row {
  display: flex;
  margin-top: -8px;
  align-items: center;
  gap: 8px;
  width: 100%;
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 18px 18px;
  padding: 0px 15px 10px 15px;
  box-sizing: border-box;
  justify-content: flex-end;
}
body.dark-mode .input-actions-row {
  background: #05507b;
  border: 2px solid #476081;
  border-top: none;
}
.input-actions {
  display: none;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  /* border-radius: 50%; */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

body.dark-mode .upload-btn {
  color: #e9edf3;
  background-color: #05507b;
}
.upload-btn {
  color: #64748b;
  background-color: rgba(255, 255, 255, 0.1);
}

.upload-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.send-btn {
  color: #fff;
  background-color: #2563eb;
  transition: background 0.2s;
}

.send-btn:hover:not(.disabled) {
  background-color: #1749b1;
}

.send-btn.disabled {
  background-color: #cbd5e1;
  color: #fff;
  opacity: 0.7;
}

.send-btn.active {
  background-color: #2563eb;
}

.send-btn.stop {
  background-color: #ef4444;
}

.send-btn.stop:hover {
  background-color: #dc2626;
}

.setting-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  margin: 10px 0;
}

.select-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.clear-btn-container {
  display: flex;
  justify-content: flex-end;
}

.clear-btn {
  padding: 8px 16px;
  background-color: transparent;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}
body.dark-mode .clear-btn {
  padding: 8px 16px;
  background-color: transparent;
  color: #ffffff;
  border: 1px solid #2563eb;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}
.clear-btn:hover {
  background-color: #f8fafc;
  color: #475569;
}
body.dark-mode .clear-btn:hover {
  background-color: #1b7fe2;
  color: #c3c9d1;
}

.file-list {
  width: 100%;
  min-width: 120px;
  background: #f4f8fb; /* 柔和的浅蓝灰色 */
  border-radius: 8px;
  padding: 6px 8px;
  box-sizing: border-box;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

body.dark-mode .file-list {
  background: rgba(255, 255, 255, 0.05);
}

.file-item {
  display: flex;
  min-width: 120px;
  max-width: 200px;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  background: #e8eef6; /* 亮色主题下更柔和的蓝灰色 */
  border-radius: 4px;
  padding: 2px 6px;
  word-break: break-all;
}

body.dark-mode .file-item {
  background: #1e293b;
  color: #e2e8f0;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-file-btn {
  background: none;
  border: none;
  color: #ef4444;
  font-size: 16px;
  cursor: pointer;
  margin-left: 6px;
  padding: 0 4px;
  border-radius: 50%;
  transition: background 0.2s;
}
.remove-file-btn:hover {
  background: #fee2e2;
}

body.dark-mode .remove-file-btn:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* 基于容器宽度的响应式样式 */
.container-medium .iframe-main-content .settings-panel {
  width: 100% !important;
  min-width: 0 !important;
  padding: 8px !important;
}

.container-medium .iframe-main-content .input-textarea {
  font-size: 13px !important;
  padding: 8px !important;
  min-height: 80px !important;
}

.container-medium .iframe-main-content .input-actions-row {
  padding: 4px 8px 6px 8px !important;
  flex-wrap: wrap !important;
  gap: 5px !important;
}

.container-medium .iframe-main-content .control-group {
  flex-wrap: wrap !important;
  gap: 5px !important;
}

.container-medium .iframe-main-content button {
  padding: 6px 10px !important;
  font-size: 12px !important;
}

.container-small .iframe-main-content .settings-panel {
  padding: 5px !important;
}

.container-small .iframe-main-content .input-textarea {
  font-size: 12px !important;
  padding: 6px !important;
  min-height: 60px !important;
}

.container-small .iframe-main-content .input-actions-row {
  padding: 3px 6px 4px 6px !important;
}

.container-small .iframe-main-content button {
  padding: 4px 8px !important;
  font-size: 11px !important;
}
</style>
