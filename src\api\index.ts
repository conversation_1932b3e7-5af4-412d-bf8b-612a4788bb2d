import http from '@/utils/http';
//!------------------------------platformType-----------------------------------------
// 模型集列表
export const getModelCollectionAPI = () => {
  return http.get(`/api/v1/platform-type-collection?pageIndex=-1&&pageSize=-1`);
};
//获得单个模型信息
export const getModelAPI = (id: Number) => {
  return http.get(`/api/v1/platform-type/${id}/instance`);
  // return http.get(`/api/v1/platform-type/${id}?editable=true`);
};
//获取全局脚本（全局脚本录入）
export const getGlobalScriptAPI = () => {
  return http.get(`/api/v1/scenario/global-script-selections`);
};

//获取link
export const getLinkTargetAPI = path => {
  return http.get(`/api/v1/template-type/link-targets?linkPath=${path}&showCurrent=false`);
};
//获取平台组件中 的参数
export const getPlatFormTypeTaskAPI = (moduleId: Number) => {
  return http.get(`/api/v1/platform-type-module?platformTypeId=${moduleId}&templateTypeId=764`);
};
// 获取实体列表数据
export const getPlatformTreeAPI = () => {
  return http.get(`/api/v1/platform/tree`);
};

// 模型资源树获取
export const getPlatformTypeTreeAPI = (name = '', collectionId = '') => {
  return http.get(`/api/v1/platform-type/tree?s=${name}&&collectionId=${collectionId}`);
};
//获得单个模型platform-type信息
export const getPlateformTypeAPI = (id: Number) => {
  return http.get(`/api/v1/platform-type/${id}`);
};
//获得单个模型platform-type信息
export const checkPlateformHasCom1 = (id: Number) => {
  return http.get(`/api/v1/platform-type/${id}/has-comm-component`);
};
//!------------------------------platform------------------------------------------
//获得单个实体信息
export const getEntityAPI = (id: Number) => {
  return http.get(`/api/v1/platform/${id}`);
};

//获得实体通讯组件参数
export const getEntityCommAPI = (ids: []) => {
  return http.post(`/api/v1/platform-type/comm-components`, {platformTypeIds: ids});
};

//!-------------------------------------想定-----------------------------------------
// 本地运行
export const localRunAPI = (params: Object) => {
  return http.post(`/api/v1/script-debugger/debug`, params, 'exe');
};
// 想定列表
export const getScenarioListAPI = (params: Object) => {
  return http.get(`/api/v1/scenario`, params);
};
// 新增想定
export const createScenarioAPI = (params: Object) => {
  return http.post(`/api/v1/scenario`, params);
};

//获取单个想定
export const getScenarioInfoAPI = (id: string | number) => {
  return http.get(`/api/v1/scenario/${id}`);
};

//修改想定
export const editScenarioAPI = (id: string | number, data: Object) => {
  return http.put(`/api/v1/scenario/${id}`, data);
};
//复制想定
export const copyScenarioAPI = (id: string | number) => {
  return http.post(`/api/v1/scenario/${id}/copy`);
};
//删除想定
export const deletScenarioAPI = (id: string | number) => {
  return http.delete(`/api/v1/scenario/${id}`);
};

// 获取想定下的模型数据
export const getScenarioModelAPI = (scenarioId: string | number) => {
  return http.get(`/api/v1/scenario/${scenarioId}`);
};
//想定脚本生成
export const exportScenarioScriptAPI = (id: string | number) => {
  return http.get(`/api/v1/script/download?id=${id}&type=4`, undefined, 'download');
};
//想定脚本校验
export const checkScriptAPI = (id: string | number) => {
  return http.get(`/api/v1/script/verify?id=${id}&type=4`);
};
//想定武器数量及开关校验
export const checkWeaponAPI = (id: string | number) => {
  return http.get(`/api/v1/scenario/${id}/verify/result`);
};
//想定预演初始化
export const scenarioPreviewInitAPI = (id: string | number) => {
  return http.get(`/api/v1/data-preview/${id}/init`, undefined, 'control');
};
//想定预演控制
export const scenarioPreviewControlAPI = (params: Object) => {
  return http.post(`/api/v1/data-preview/control`, params, 'control');
};

//想定预演socket
export const scenarioPreviewSocket = () => {
  // return `${globalConfig.BaseWSUrl}/api/v1/data-preview/getModelData`;
  return `${globalConfig.ReviewBaseWSUrl}/api/v1/data-socket/getModelData?pushType=preview`;
};

//想定预演进度socket
export const previewProgressState = () => {
  return http.get(`/api/v1/data-preview/getSysInfo`, undefined, 'control');
};
// 上传脚本文件
export const uploadLocalFileApi = (params: object | undefined) => {
  return http.post(`/api/v1/virtual-file`, params);
};
// 获取脚本内容
export const getLocalFileApi = (id: string) => {
  return http.get(`/api/v1/virtual-file/${id}/json`);
};

//!---------------------------------------------单元-----------------------------------------------------
/**
 * 获取单元的树结构数据
 */
export const getUnitTree = () => {
  return http.get(`/api/v1/unit-resources-category/tree`);
};

/**
 * 添加模型
 * @param  {
 * "entityName": "string",
 * "groupId": 0,
 * "modelDisplayName": "string",
 * "platformTypeId": 0,
 * "parameters": "string"
 * } params
 */
export const addModelApi = (params: Object) => {
  return http.post(`/api/v1/unit-resources`, params);
};

/**
 * 获取单个模型信息
 * @param {id} id
 */
export const getModelDataApi = (id: Number) => {
  return http.get(`/api/v1/unit-resources/${id}`);
};

/**
 * 删除模型
 * @param {id} id
 */
export const deleteModelApi = (id: Number) => {
  return http.delete(`/api/v1/unit-resources`, {ids: [id]});
};

//获取单元列表
export const getUnitListApi = (params: Object) => {
  return http.post(`/api/v1//modelEntityGroup/search`, params);
};

//添加单元
export const addUnitApi = (params: Object) => {
  return http.post(`/api/v1/unit-resources-category`, params);
};

//添加单元编辑单元名称
export const editUnit = (id: Number, params: Object) => {
  return http.put(`/api/v1/unit-resources-category/${id}`, params);
};

/**
 * 获取单个单元数据
 * @param {id} id
 */
export const getUnitDataApi = (id: Number) => {
  return http.get(`/api/v1/unit-resources-category/id/${id}`);
};
/**
 * 删除单元
 * @param {id} id
 */
export const deleteUnitApi = (id: Number) => {
  return http.delete(`/api/v1/unit-resources-category`, {ids: [id]});
};

/**
 * 编辑模型数据
 * @param {id} //单元的id
 * @param {
 *    {
 *      "entityName": "string",
 *       "groupId": 0,
 *      "id":1,          模型的id
 *      "modelDisplayName": "string",
 *      "platformTypeId": 0,
 *      "parameters": "string"
 *    }
 * } params
 */
export const editModelApi = (id: Number, params: Object) => {
  return http.put(`/api/v1/modelEntityGroup/entities/${id}`, params);
};

export const editModelDataApi = (id: Number, params: Object) => {
  return http.put(`/api/v1/modelEntity/${id}`, params);
};
//获取单元下的模型数据
export const getUnitModel = (id: Number) => {
  return http.put(`/api/v1/modelEntityGroup/${id}`);
};

/**
 * 获取模型列表
 * @param  {
 * "groupId": [],
 * "pageNum": -1,
 * "pageSize": -1 } params
 */
export const getUnitModelList = (params: Object) => {
  return http.post(`/api/v1/modelEntity/search`, params);
};

//依据icon名称获取图标数据
export const getIconId = (name: String) => {
  return http.get(`/api/v1/icon-category/?name=${name}`);
};
//获取想定编辑可编辑的参数集合
export const getEditParams = () => {
  return http.get(`/api/v1/scenario-edit-params/`);
};
// 获取icon数据
export const getIconApi = (type = 2) => {
  return http.get(`/api/v1/icon-category/tree`, {type});
};

//获取模型数据
export const getParamDataApi = (id: Number) => {
  return http.get(`/api/v1/model-data/${id}`);
};
//获取模型数据参数
export const getModelDataParamApi = (id: Number) => {
  return http.get(`/api/v1/model-data/${id}`);
};
// 获取资源树
// export const getDataResouce=() =>{
//   return http.get('/api/v1/template-category/tree/typeList');
// }

// 获取航路等数据
export const getDataResouce = (id: Number) => {
  return http.get(`/api/v1/template-category/tree/typeModelData?category_id=${id}`);
};
// 获取航路列表
export const getRouteDataApi = (id: Number) => {
  return http.get(`/api/v1/scenario/${id}/route-selections`);
};
// 获取航路列表
export const getZoneDataApi = (id: Number) => {
  return http.get(`/api/v1/scenario/${id}/zone-selections`);
};
// 获取数据模板
export const getDataDetailApi = (id: Number) => {
  return http.get(`/api/v1/model-data?name=&templateId=${id}`);
};

// 获取任务规划数据
export const getMissionPlanListApi = (scenarioId: Number) => {
  return http.get(`/api/v1/mission-plan/${scenarioId}`);
};

// 获取任务事件数据
export const getTaskEventListApi = () => {
  return http.get(`/api/v1/event-data`);
};

// 创建矩阵
export const createMissionPlanApi = (params: Object) => {
  return http.post('/api/v1/mission-plan', params);
};
// 修改矩阵
export const updateMissionPlanApi = (id: Number, params: Object) => {
  return http.put(`/api/v1/mission-plan/${id}`, params);
};
// 删除矩阵
export const deleteMissionPlanApi = (id: Number) => {
  return http.delete(`/api/v1/mission-plan`, {ids: [id]});
};

// 获取阶段列表
export const getMissionPlanStageListApi = () => {
  return http.get('/api/v1/mission-plan-stage');
};
export const getMissionPlanStageApi = (id: Number) => {
  return http.get(`/api/v1/mission-plan-stage/${id}`);
};
// 创建阶段
export const createMissionPlanStageApi = (params: Object) => {
  return http.post('/api/v1/mission-plan-stage', params);
};
// 修改阶段
export const updateMissionPlanStageApi = (id: Number, params: Object) => {
  return http.put(`/api/v1/mission-plan-stage/${id}`, params);
};
// 删除阶段
export const deleteMissionStagePlanApi = (id: Number) => {
  return http.delete(`/api/v1/mission-plan-stage`, {ids: [id]});
};
//获取组件树
export const getComponentTreeApi = () => {
  return http.get(`/api/v1/component/tree`);
};
//想定运行初始化
export const scenarioRunInitApi = (param: Object) => {
  return http.post(`/api/v1/data-server/init`, param, 'control3d');
};
//想定运行初始化
export const getUnitConfigApi = (type: string | number) => {
  if (typeof type === 'string') {
    return http.get(`/api/v1/unit-config/units?configName=${type}`);
  } else {
    return http.get(`/api/v1/unit-config/units?configId=${type}`);
  }
};
//获取环境配置列表
export const getEnvList = (param: {templateTypeId: number}) => {
  return http.get(`/api/v1/model-data?templateTypeId=${param.templateTypeId}`);
};
// //获取路网配置列表
// export const getRouteList = (param: {templateTypeId: number}) => {
//   return http.get(`/api/v1/model-data?templateTypeId=${param.templateTypeId}`);
// };
//获取地形配置列表
export const getTerrainList = () => {
  return http.get(`/api/v1/scenario/0/terrain-selections`);
};

// 获取单位列表
export const getUnitsListApi = (params: object | undefined) => {
  return http.get(`/api/v1/unit-config`, params);
};

// 单位转换
export const unitConvertApi = (sourceUnit: string, targetUnit: string, value: string) => {
  return http.get(`/api/v1/unit-config/unit/convert?sourceUnit=${sourceUnit}&targetUnit=${targetUnit}&value=${value}`);
};
//获取战场环境模版 typeId: 821
export const getEnvListApi = (params: object | undefined) => {
  return http.get(`/api/v1/template-type-params`, params);
};

// 编组相关
export const getGroupListApi = () => {
  return http.get(`/api/v1/model-group`);
};
export const getGroupByIdApi = (id: number | string) => {
  return http.get(`/api/v1/model-group/${id}`);
};
export const addGroupApi = (params: object) => {
  return http.post(`/api/v1/model-group`, params);
};
export const updataGroupNameApi = (params: object, id: string) => {
  return http.put(`/api/v1/model-group/${id}/basic`, params);
};
export const updateGroupApi = (params: object, id: number | string) => {
  return http.put(`/api/v1/model-group/${id}`, params);
};
export const deleteGroupApi = (id: number | string) => {
  return http.delete(`/api/v1/model-group/${id}`);
};
// 获取网络规划的网络类型
export const getNetworkTypeApi = () => {
  return http.get(`/api/v1/model-data?categoryId=166&isTemplate=true`);
};
// 上传想定
export function addFile3dForm(params) {
  return http.post(`/api/v1/scenario/file`, params, 'upLoad');
}
