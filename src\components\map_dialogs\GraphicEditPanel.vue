<script setup>
import {ElMessage} from 'element-plus';
import {reactive} from 'vue';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
import bus from '@/utils/bus';
import {getDefaultUnit} from '@/utils/data_process';
import {checkNumber} from '@/utils/tools';

const {mapInstance} = storeToRefs(useMapStore());
const stateControler = useStateStore();

const state = reactive({
  // 点线面具体区域类型选择
  selectType: {
    Point: [{label: '特殊点', value: 'Point'}],
    LineString: [{label: '航路', value: 'LineString'}],
  },

  drawForm: {
    properties: {
      tableData: [],
      drawingtype: '', //点,线,面具体类型
    },
    geometry: {
      type: '',
    },
  },
  tableData: [], //经纬参数表格数据
  addWayPoint: false, //实体绑定航路点
  typeName: '', //类型名
  drawName: '', //显示的类型名
});

// draw控件的相关回调注册以及数据获取
const useMapDraw = () => {
  //draw 控件数据操作回调
  const drawEventCallback = (todo, data) => {
    console.log('🚀 ~ drawEventCallback ~ data:', data);
    const currentEditRoute = data?.currentEditRoute;
    let resData = {}; //整理的数据结构
    switch (todo) {
      case 'drawWayVisible':
        stateControler.graphicEditPanelVisible = data;
        break;
      case 'drawTipsShow':
        stateControler.drawTipsShow = data;
        break;
      case 'setShapeInfo':
        stateControler.shapeInfo = data;
        break;
      case 'updateDrawForm':
        resData = data.drawForm;
        resData.side = currentEditRoute.side || currentEditRoute.properties.side;
        resData.displayName = currentEditRoute.displayName || currentEditRoute.properties.displayName;
        resData.globalID = currentEditRoute.globalID;
        resData.properties.tableData = data.lastViapointData;
        resData.properties.drawingtype = currentEditRoute.properties.drawingtype;
        // console.log(data.lastViapointData, 'data.lastViapointData');
        // debugger;
        getDraw(resData);
        break;
      case 'createDrawForm':
        resData = data.drawForm;
        resData.globalID = stateControler.graphicId;
        getDraw(resData);
        break;
      case 'massDeploymentShow':
        stateControler.massDeploymentVisible = true;
        break;
      default:
        break;
    }
  };
  // 注册选择、更新点线面数据事件
  mapInstance.value.registerSelectionAndUpdateEvent(drawEventCallback);
  // 注册创建点线面数据事件
  mapInstance.value.registerCreateEvent(drawEventCallback);
  // 注册删除点线面数据事件
  mapInstance.value.registerDeleteEvent(drawEventCallback);
  //点击地图的时候清除高亮,如果处于编辑状态则退出
  mapInstance.value.registerMapClick(() => {
    if (stateControler.drawTipsShow) stateControler.drawTipsShow = false;
    stateControler.shapeInfo = '';
  });
};
bus.on('mapLoaded', () => {
  useMapDraw();
});
bus.on('eidtGraphical', data => {
  getDraw(data);
});
bus.on('setAddWayPoint', data => {
  state.addWayPoint = data;
});
//todo 获取到geojson转为表格可展示数据
const getDraw = data => {
  if (!data) return;
  //图形类型
  const type = data.geometry.type;
  switch (type) {
    case 'LineString': // 线
      state.tableData = data.geometry.coordinates;
      data.properties?.drawingtype ? '' : (data.properties.drawingtype = 'LineString');
      state.typeName = '航路';
      break;
    case 'Point': // 点
      state.tableData = [data.geometry.coordinates];
      data.properties?.drawingtype ? '' : (data.properties.drawingtype = 'Point');
      state.typeName = '特殊点';
      break;
    default:
      break;
  }
  if (state.drawForm.properties.drawingtype) {
    state.drawName = state.drawForm.properties.drawingtype; //显示的名称
    getDrawName(state.drawName, type);
  }
  if (!data.properties.tableData) {
    // 初始化数据
    data.properties.tableData = [];
    state.tableData.forEach(el => {
      const item = {};
      item.longitude = Number(el[0].toFixed(6));
      item.latitude = Number(el[1].toFixed(6));
      item.altitude = el.altitude ? Number(el.altitude) : 0;
      item.altitudeUnit = getDefaultUnit('length').name;
      if (type === 'LineString') item.speed = el.speed ? Number(el.speed) : 0;
      if (type === 'LineString') item.speedUnit = getDefaultUnit('speed').name;
      // //默认选择第一个
      // if (state.typeData.AIR && type === 'LineString') {
      //   item.type = state.typeData.AIR[0].value;
      // }
      data.properties.tableData.push(item);
    });
  }
  data.side ? '' : (data.side = 'red');
  data.displayName ? '' : (data.displayName = `红方${state.typeName}-${data.globalID}`);
  data.name = data.displayName;

  state.drawForm = JSON.parse(JSON.stringify(data));
  // 更新航路的数据
  mapInstance.value.graphicalGeojson.features.forEach(item => {
    if (item.name === data.name) {
      item.properties = data.properties;
      item.geometry = data.geometry;
    }
  });
};

// 修改阵营表单项
const getDrawName = (name, type) => {
  //传入要查询的value,以及大的分类(点/线/面)
  console.log('🚀 ~ getDrawName ~ state.selectType:', state.selectType);
  state.selectType[name].forEach(item => {
    if (item.value === name) {
      state.typeName = item.label;
    }
  });
};
//修改类型(具体点线面)
const setDrawType = definiteType => {
  getDrawName(definiteType, state.drawForm.geometry.type);
  handleEdit(state.drawForm.side);
};
//修改阵营
const handleEdit = side => {
  // 重新处理名称
  stateControler.entityListData.forEach(item => {
    if (item.side === side) {
      state.drawForm.displayName = `${item.title}${state.typeName}-${state.drawForm.globalID}`;
    }
  });
};
// 确定按钮无绑定时
const saveForm = () => {
  const {displayName, side, properties} = state.drawForm;
  if (!displayName || !side || !properties.drawingtype) {
    ElMessage.error('请查看类型、阵营是否绑定');
    return;
  }
  saveFormData();
  state.typeName = '';
};

// 索引
const indexMethod = index => index + 1;
// 保存表单数据
const saveFormData = () => {
  state.drawForm.properties.tableData.forEach(ele => {
    ele.speed = Number(ele.speed);
    ele.altitude = Number(ele.altitude);
    ele.speedUnit = ele.speedUnit;
    ele.altitudeUnit = ele.altitudeUnit;
    // state.drawForm.properties.altitude.push(parseFloat(ele.altitude));
    // state.drawForm.properties.speed.push(parseFloat(ele.speed));
  });
  state.drawForm.properties.side = state.drawForm.side;
  state.drawForm.properties.displayName = state.drawForm.displayName;
  //绘制点线面组件隐藏
  stateControler.graphicEditPanelVisible = false;
  //绘制操作提示隐藏
  stateControler.drawTipsShow = false;
  mapInstance.value.getDrawData(state.drawForm);
  // 更新geojson的数据
  const index = mapInstance.value.graphicalGeojson.features.findIndex(item => item.uuid === state.drawForm.uuid);
  mapInstance.value.graphicalGeojson.features[index] = JSON.parse(JSON.stringify(state.drawForm));
  mapInstance.value.drawInstance.changeMode('simple_select', {featureIds: [state.drawForm.id]});
  mapInstance.value.drawInstance.changeMode('simple_select', {featureIds: []});
};
const close = () => {
  stateControler.graphicEditPanelVisible = false;
  stateControler.drawTipsShow = false;
  mapInstance.value.drawInstance.changeMode('simple_select', {featureIds: []});
  state.drawForm.properties.side = state.drawForm.side;
  mapInstance.value.getDrawData(state.drawForm, true);
};
</script>

<template>
  <div v-show="stateControler.graphicEditPanelVisible" class="draw-way mask">
    <div class="draw-list glass">
      <div class="panel-header">
        <span>航路、区域信息面板 </span> <el-icon class="close" @click="close"><Close /></el-icon>
      </div>

      <el-form ref="drawForm" :model="state.drawForm">
        <el-row>
          <el-col :span="7">
            <el-form-item label="类型：">
              <el-select v-model="state.drawForm.properties.drawingtype" class="type-select" placeholder="请选择" suffix-icon="CaretBottom" @change="setDrawType(state.drawForm.properties.drawingtype)">
                <el-option v-for="item in state.selectType[state.drawForm.geometry.type]" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="margin-left" label="阵营：">
              <el-select v-model="state.drawForm.side" class="side-select" placeholder="请选择" suffix-icon="CaretBottom" @change="handleEdit(state.drawForm.side)">
                <el-option v-for="item in stateControler.entityListData" :key="item.side" :label="item.title" :value="item.side"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item class="margin-left" label="名称">
              <el-input v-model="state.drawForm.displayName" size="small" placeholder="名称" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-table :data="state.drawForm.properties.tableData" max-height="300" border>
            <el-table-column type="index" :index="indexMethod"> </el-table-column>
            <el-table-column label="经度">
              <template #default="scope">
                <el-input v-model="scope.row.longitude" :title="scope.row.longitude" size="small" placeholder="请输入内容" disabled></el-input>
              </template>
            </el-table-column>
            <el-table-column label="纬度">
              <template #default="scope">
                <el-input v-model="scope.row.latitude" :title="scope.row.latitude" size="small" placeholder="请输入内容" disabled></el-input>
              </template>
            </el-table-column>
            <el-table-column label="高度">
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.altitude" size="small" placeholder="请输入内容" @input="scope.row.altitude = checkNumber(scope.row.altitude)"> </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="高度单位">
              <template #default="scope">
                <el-select v-model="scope.row.altitudeUnit" size="small">
                  <el-option v-for="(e, ix) in stateControler.unitConfig.get(11).params" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column v-if="state.drawForm.geometry.type === 'LineString'" label="速度">
              <template #default="scope">
                <el-form-item>
                  <el-input v-model="scope.row.speed" size="small" type="number" placeholder="请输入内容" @input="scope.row.speed = checkNumber(scope.row.speed)"> </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column v-if="state.drawForm.geometry.type === 'LineString'" label="速度单位">
              <template #default="scope">
                <el-select v-model="scope.row.speedUnit" size="small">
                  <el-option v-for="(e, ix) in stateControler.unitConfig.get(21).params" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="saveForm('drawForm')">确 定</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.draw-list {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 999;
  max-width: 700px;
  padding: 30px 20px 20px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  transform: translate(-50%, -50%);

  .el-select__wrapper {
    min-width: 60px;
  }

  :deep(.el-table .cell) {
    padding: 0 3px;
  }
}

.panel-header {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  width: 100%;
  height: 30px;
  font-size: 16px;
  line-height: 30px;
  color: #fff;
  text-align: center;
  background-color: var(--title-bg-color);
  align-items: center;
  justify-content: space-between;
}

.panel-header span {
  flex: 1;
}

.close {
  font-size: 24px;
  color: var(--icon-color);
}

.draw-list .dialog-footer {
  top: 10px;
}

.margin-left {
  margin-left: 20px;
}

.side-select {
  min-width: 100px;
}

.type-select {
  min-width: 100px;
}
</style>
