<!--
 * @Author: 老范
 * @Date: 2024-05-07 15:21:41
 * @LastEditors: 老范
 * @LastEditTime: 2024-10-11 09:59:47
 * @Description: 自然环境配置
-->
<script setup lang="ts">
import {ElMessage, ElMessageBox} from 'element-plus';
import {useStateStore} from '@/pinia/state/index';
import {Ref, onMounted, reactive, ref, watch} from 'vue';
import envEditDialog from '@/components/entity_panel/envConfig/envEditDIalog.vue';
import {getEnvList, getUnitsListApi, getEnvListApi, getTerrainList} from '@/api/index';
import type {envDataType} from '@/pinia/state/index';
import {getDefaultUnit} from '@/utils/data_process';

onMounted(() => {
  getEnvParams();
  getEnvTempList();
});
const stateControler = useStateStore();
watch(
  () => stateControler.weatherCheckList,
  val => {
    if (val.length === 0) {
      getEnvTempList();
    } else {
      getUnitList(JSON.parse(JSON.stringify(val)));
    }
  }
);
const envList = ref();
const routeList = ref();
const terrainList = ref();
const unitList = ref(); // 单位列表
const dialogVisible = ref(false); // 编辑弹框
const envTemplateList: Ref<envDataType[]> = ref([]);
const getEnvParams = async () => {
  // 环境
  const envRes = await getEnvList({templateTypeId: 821});
  envList.value = envRes.data.list;
  // 路网
  const routeRes = await getEnvList({templateTypeId: 754});
  routeList.value = routeRes.data.list;
  // 地形
  const terrainRes = await getTerrainList();
  terrainList.value = terrainRes.data;
};
// 获取单位列表
const getUnitList = async (val: undefined) => {
  const res = await getUnitsListApi({});
  unitList.value = res.data.list;
  unitList.value.forEach(i => stateControler.unitConfig.set(i.id, i));
  if (val) {
    dataConvertShow(val);
  }
};
const getEnvTempList = async () => {
  const res = await getEnvListApi({typeId: 821});
  await getUnitList();
  dataConvertShow(res.data.list);
};

const dataConvertShow = (sourceData: any[]) => {
  envTemplateList.value.length = 0;
  sourceData.forEach(item => {
    paramsMap.forEach((paramsItem: {value: string | any[]; label: any}) => {
      // 分三组
      if (paramsItem.value.includes(item.groupId)) {
        const params: envDataType = {
          type: paramsItem.label,
          sortNum: paramsItem.sortNum,
          displayName: item.displayName,
          name: item.name,
          defaultType: item.defaultType,
          unitId: item.unitId,
          defaultValue: item.defaultValue,
          value: item.value,
          templateParamId: item.templateParamId || item.id,
          groupId: item.groupId,
        };
        // 值赋值
        if (item.value) {
          params.value = item.value;
        } else {
          params.value = item.defaultValue;
        }

        // 单位赋值
        if (item.unitType) {
          params.unitType = item.unitType;
        } else {
          unitList.value.forEach((unitItem: {id: any; params: (string | undefined)[]}) => {
            if (item.unitId === unitItem.id) {
              const defaultUnit = getDefaultUnit(item.unitId);
              params.unitType = defaultUnit ? defaultUnit.name : unitItem.params[0].name;
            }
          });
        }
        if (item.unitObj) {
          params.unitObj = item.unitObj;
        } else {
          params.unitObj = getUnitObjByName(params.unitType, item.unitId);
        }
        if (item.defaultType === 'struct') {
          params.configureJson = item.configureJson;
          // 已配置值
          if (item.value) {
            if (typeof item.value === 'string') {
              params.value = JSON.parse(item.value);
            } else {
              params.value = item.value;
            }
          } else {
            // 初次配置
            const defaultAry = JSON.parse(item.configureJson);
            params.value = defaultAry.map((configItem: any) => {
              return {...configItem, value: 0, unitType: firstGetUnitById(configItem.unitId)};
            });
          }
        }
        envTemplateList.value.push(params);
      }
    });
  });
  envTemplateList.value.sort((a: envDataType, b: envDataType) => a.sortNum - b.sortNum);
};
const setConfigData = (val: envDataType[]) => {
  console.log('🚀 ~ setConfigData ~ val:', val);
  stateControler.weatherCheckList = val;
};
const getParamsLabel = (val: {name: string | number; value: any}) => {
  let resultLabel = '';
  if (envValueMap[val.name]) {
    envValueMap[val.name].forEach((item: {value: any; label: string}) => {
      if (item.value === val.value) {
        resultLabel = item.label;
      }
    });
  }
  return resultLabel;
};
// 通过name获取label
const getUnitByName = (datas, index) => {
  let resStr = '';
  unitList.value.forEach((item: any) => {
    if (item.id === datas.value[index].unitId) {
      item.params.forEach(params => {
        if (params.name === datas.value[index].unitType) {
          resStr = params.displayName;
        }
      });
    }
  });
  return resStr;
};
// 通过name获取label
const getUnitObjByName = (name, unitId) => {
  let result = {};
  unitList.value.forEach((item: any) => {
    if (item.id === unitId) {
      item.params.forEach(params => {
        if (params.name === name) {
          return (result = params);
        }
      });
    }
  });
  return result;
};
const firstGetUnitById = unitId => {
  let resStr = '';
  unitList.value.forEach((item: any) => {
    if (item.id === unitId) {
      const defaultUnit = getDefaultUnit(unitId);
      resStr = defaultUnit ? defaultUnit.name : item.params[0].name;
    }
  });
  return resStr;
};
const closeVisible = () => {
  dialogVisible.value = false;
};
const openConfigTable = () => {
  dialogVisible.value = true;
};
const resetDefault = () => {
  getEnvTempList();
};
</script>

<template>
  <div class="env-content">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>气象</span>
          <div class="split-line"></div>
        </div>
      </template>
      <!-- <el-checkbox-group v-model="stateControler.weatherCheckList">
        <el-checkbox v-for="item in envList" :key="item.id" :label="item.displayName" :value="item.id" />
      </el-checkbox-group> -->
      <div v-for="(item, index) in envTemplateList" v-show="item.sortNum == 0" :key="index">
        <div v-if="item.defaultType == 'struct'" class="envItem structHeight">
          <div class="envLabel-unit">{{ item.displayName }}</div>
          <div class="line"></div>
          <div class="structValue">
            <span class="structItem">
              <div class="structValue">{{ item.value[0].value }}</div>
              <span class="structUnit" :title="getUnitByName(item, 0)">{{ getUnitByName(item, 0) }}</span>
            </span>

            <span class="structItem">
              <div class="structValue">{{ item.value[1].value }}</div>
              <span class="structUnit" :title="getUnitByName(item, 1)">{{ getUnitByName(item, 1) }}</span></span
            >
          </div>
        </div>
        <div v-else class="envItem">
          <div class="envLabel-unit">{{ item.displayName }}</div>
          <div class="line"></div>
          <div class="envValue">
            <div class="envDatas">{{ item.value }}</div>
            <div class="envUnit" :title="item?.unitObj?.displayName">{{ item?.unitObj?.displayName }}</div>
          </div>
        </div>
      </div>
    </el-card>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>地表</span>
          <div class="split-line"></div>
        </div>
      </template>
      <div v-for="(item, index) in envTemplateList" :key="index">
        <div v-if="item.sortNum == 1" class="envItem">
          <div class="envLabel">{{ item.displayName }}</div>
          <div class="line"></div>
          <div class="envValue">
            <div class="envSurfaceData">{{ getParamsLabel(item) }}</div>
          </div>
          <!-- <div class="envUnit" :title="item?.unitType">{{ item?.unitType }}</div> -->
        </div>
      </div>
    </el-card>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>水文</span>
          <div class="split-line"></div>
        </div>
      </template>

      <div v-for="(item, index) in envTemplateList" :key="index">
        <div v-if="item.sortNum == 2" class="envItem">
          <div class="envLabel">{{ item.displayName }}</div>
          <div class="line"></div>
          <div class="envValue" :title="getParamsLabel(item)">
            <div class="envDatas">{{ getParamsLabel(item) }}</div>
          </div>
          <div class="envUnit" :title="item?.unitType">{{ item?.unitType }}</div>
        </div>
      </div>
    </el-card>
    <envEditDialog :visible="dialogVisible" :show-close="false" :table-data="envTemplateList" :unit-list="unitList" @set-config-data="setConfigData" @close-visible="closeVisible" @reset="resetDefault"></envEditDialog>
    <el-button @click="openConfigTable">编辑</el-button>
    <el-button @click="resetDefault">重置</el-button>
    <el-card class="mar_t_25">
      <template #header>
        <div class="card-header">
          <span>路网</span>
          <div class="split-line"></div>
        </div>
      </template>
      <el-checkbox-group v-model="stateControler.routesNetworks">
        <el-checkbox v-for="item in routeList" :key="item.id" :label="item.displayName" :value="item.id" />
      </el-checkbox-group>
    </el-card>
    <el-card class="mar_t_25">
      <template #header>
        <div class="card-header">
          <span>地形</span>
          <div class="split-line"></div>
        </div>
      </template>
      <el-checkbox-group v-model="stateControler.terrain" max="1">
        <el-checkbox v-for="item in terrainList" :key="item.id" :label="item.displayName" :value="item.id" />
      </el-checkbox-group>
    </el-card>
  </div>
</template>

<style lang="less" scoped>
.env-content {
  width: 100%;
  height: calc(100vh - 190px);
  overflow-x: hidden;
  overflow-y: scroll;
  color: #fff;
  border-radius: 5px;
  box-sizing: border-box;

  // .el-collapse-item__content :deep(.el-card) {
  //   padding: 0 0 20px 5px !important;
  // }

  .el-card {
    color: #fff;
    background-color: transparent;
    border: 0;

    --el-card-padding: 0 20px 10px;

    :deep(.el-checkbox__label) {
      color: #fff;
    }

    :deep(.el-checkbox__inner) {
      background-image: url('@/assets/images/check.png') !important;
      background-size: contain;
      background-color: transparent;
      border: 0;
    }

    :deep(.is-checked .el-checkbox__inner) {
      background-image: url('@/assets/images/checked.png') !important;
    }

    :deep(.el-checkbox__inner::after) {
      border-color: transparent;
    }

    :deep(.el-card__header) {
      padding: 10px 20px 0 !important;
      text-align: left;
      border: 0;
    }

    .card-header {
      display: flex;
      font-size: 16px;
      font-weight: bold;
      color: #5cc9ff;
      align-items: center;

      .split-line {
        height: 10px;
        margin-left: 10px;
        background: url('@/assets/images/br.png') no-repeat;
        flex: 1;
      }
    }

    .envItem {
      display: flex;
      width: 250px;
      align-items: center;
      padding: 5px 0;

      .envLabel-unit {
        width: 75px;
        text-align-last: center;
      }

      .envLabel {
        width: 115px;
        text-align-last: center;
      }

      .line {
        width: 13px;
        height: 0;
        color: #5cc9ff;
        border-bottom: 1px solid #5cc9ff;
      }

      .envValue {
        display: flex;
        width: 135px;
        height: 20px;
        margin-left: 5px;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // align-items: center;
        // justify-content: center;

        .envDatas {
          width: 35px;
          text-align: right;
        }

        .envSurfaceData {
          width: 65px;
          padding-left: 10px;
          text-align: left;
        }

        .envUnit {
          // display: none;
          max-width: 90px;
          margin-left: 10px;
          text-align: left;

          // overflow: hidden;
          // text-overflow: ellipsis;
          // white-space: nowrap;
        }
      }

      .structValue {
        display: flex;
        width: 135px;
        margin-left: 2px;
        flex-direction: column;
        text-overflow: ellipsis;
        white-space: nowrap;

        .structItem {
          display: flex;
          width: 135px;
          height: 20px;

          .structValue {
            width: 35px;
            text-align: right;
          }

          .structUnit {
            // display: none;
            width: 70px;
            margin-left: 10px;
            overflow: hidden;
            text-align: left;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align-last: left;
          }
        }
      }
    }

    // .envItem:hover .envUnit {
    //   display: block;
    //   width: 30px;
    //   margin-left: 10px;
    //   overflow: hidden;
    //   text-overflow: ellipsis;
    //   white-space: nowrap;
    //   text-align-last: left;
    // }

    // .envItem:hover .structItem .structUnit {
    //   display: inline-block;
    //   width: 30px;
    //   margin-left: 10px;
    //   overflow: hidden;
    //   text-overflow: ellipsis;
    //   white-space: nowrap;
    //   text-align-last: left;
    // }

    .structHeight {
      height: 70px;
    }
  }
}
</style>
