<!--
 * @Description: 兵力部署面板
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-17 09:18:37
 * @LastEditors: 老范
 * @LastEditTime: 2024-06-19 10:58:46
-->
<script setup>
import {move} from '@/utils/tools';
import EntityList from '@/components/entity_panel/entity_list/EntityList.vue';
import CommandChain from '@/components/entity_panel/command_chain/CommandChain.vue';
import EnvConfigPanel from '@/components/entity_panel/envConfig/envConfigPanel.vue';
import {onMounted, ref, watch} from 'vue';
import {useStateStore} from '@/pinia/state/index';
onMounted(() => move(entityPanel.value));

const stateControler = useStateStore();

const activeName = ref('1');
const entityPanel = ref('');

watch(activeName, (newVal, oldVal) => {
  if (!newVal) activeName.value = String(oldVal - 1) === '0' ? '3' : String(oldVal - 1); //禁止全部收起
});
</script>
<template>
  <div v-show="stateControler.controlEntity" ref="entityPanel" class="entity-panel">
    <!-- <div class="entity-panel-title">兵力部署</div> -->

    <el-collapse v-model="activeName" accordion>
      <el-collapse-item name="1">
        <template #title>
          <div class="title-content">
            <span> 实体列表</span>
            <el-icon class="header-icon">
              <CaretRight />
            </el-icon>
          </div>
        </template>
        <EntityList />
      </el-collapse-item>
      <el-collapse-item name="2">
        <template #title>
          <div class="title-content">
            <span> 指挥链</span>
            <el-icon class="header-icon">
              <CaretRight />
            </el-icon>
          </div>
        </template>
        <CommandChain />
      </el-collapse-item>
      <el-collapse-item title="自然环境配置" name="3">
        <template #title>
          <div class="title-content">
            <span> 自然环境配置</span>
            <el-icon class="header-icon">
              <CaretRight />
            </el-icon>
          </div>
        </template>
        <EnvConfigPanel />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style lang="less" scoped>
.entity-panel {
  position: fixed;
  top: 45px;
  right: 0;
  z-index: 1;
  width: 255px;
  height: calc(100% - 82px);
  color: #fff;
  border-radius: 5px;
  box-sizing: border-box;

  :deep(.el-tabs__item) {
    height: 26px;
    font-size: 16px;
    line-height: 26px;
  }

  :deep(.el-tabs__item:first-child) {
    color: #ff9393 !important;
    background-color: #4d3545;
  }

  :deep(.is-active.el-tabs__item:first-child) {
    font-weight: bold;
    border: 1px solid #ff9393 !important;
  }

  :deep(.el-tabs__item:nth-child(2)) {
    background-color: #15628e;
  }

  :deep(.is-active.el-tabs__item:nth-child(2)) {
    font-weight: bold;
    color: #4bbfff !important;
    border: 1px solid #4bbfff !important;
  }

  :deep(.el-collapse-item__arrow) {
    display: none;
  }

  .title-content {
    display: flex;
    width: 100%;
    font-family: Medium;
    font-size: 16px;
    align-items: center;
    justify-content: space-between;
  }

  .header-icon {
    font-size: 16px;
    color: var(--icon-color);
    transform: rotate(0deg);
  }

  :deep(.el-collapse-item__header.is-active .header-icon) {
    transform: rotate(90deg);
    transition: 0.3s ease-in-out;
  }
}

.entity-panel .el-tab-pane {
  height: 100%;
}

.entity-panel-title {
  height: 40px;
  font-size: 15px;
  line-height: 40px;
}

.nav-tabs {
  width: 100%;
  height: 94%;
}

:deep(.el-collapse) {
  height: 100%;
}

:deep(.el-collapse-item__wrap) {
  height: calc(100vh - 190px);
}

:deep(.el-tabs__nav.is-bottom) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 5px;
}

:deep(.el-tabs__item.is-bottom) {
  width: 100%;
  height: 30px;
  padding: 0 !important;
  margin: 5px 0;
  line-height: 30px;
  text-align: center;
  background: var(--panel-bg-color);
  border-radius: 5px;
}

:deep(.nav-tabs .el-tabs__content) {
  height: calc(100% - 80px);
}

:deep(.el-tabs--bottom .el-tabs__header.is-bottom) {
  margin-top: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none !important;
}

:deep(.el-collapse-item__header) {
  width: 255px;
  height: 34px;
  padding-left: 8px;
  margin-bottom: 3px;
  color: #fff;
  background: url('@/assets/images/collapse-bg.png') no-repeat !important;
}
</style>
