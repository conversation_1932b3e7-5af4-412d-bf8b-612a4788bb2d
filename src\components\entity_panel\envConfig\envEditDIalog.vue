<!--
 * @Author: 老范
 * @Date: 2024-05-07 15:21:41
 * @LastEditors: 老范
 * @LastEditTime: 2024-08-06 16:04:35
 * @Description: 自然环境配置
-->
<script setup lang="ts">
import {ElMessage, ElMessageBox, SCOPE} from 'element-plus';
import {isContext} from 'vm';
import {computed, onMounted, reactive, ref, watch} from 'vue';
import {useStateStore} from '@/pinia/state/index';
import {getScenarioInfoAPI} from '@/api';

const stateControler = useStateStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  unitList: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(['setConfigData', 'closeVisible', 'reset']);
const dialogShow = ref(false);
// const dialogShow = computed({
//   get: () => props.visible,
//   set: (val: boolean) => emits('updata:visible', val),
// });
watch(
  () => props.visible,
  val => {
    dialogShow.value = val;
  }
);
watch(props.tableData, val => {
  tableData.value = val;
  rowSort();
  rowCalc();
});
const tableData = ref();
const spanArr = reactive([
  {
    prop: 'type',
    span: [],
  },
]);
const saveConfig = () => {
  emits('setConfigData', tableData.value);
  emits('closeVisible');
};

const arraySpanMethod = ({row, column, rowIndex, columnIndex}) => {
  for (let i = 0; i < spanArr.length; i++) {
    const ele = spanArr[i];
    if (column.property === ele.prop) {
      const _row = ele.span[rowIndex];
      const _col = _row > 0 ? 1 : 0;
      return {
        rowspan: _row,
        colspan: _col,
      };
    }
  }
};
const rowCalc = () => {
  spanArr.forEach((ele, index) => {
    let parent;
    if (index !== 0) parent = spanArr[ele.parent || index - 1].span;
    ele.span = rowSpan(ele.prop, parent);
  });
};
const rowSort = () => {
  spanArr.forEach((ele, index) => {
    const key = ele.prop;
    tableData.value = tableData.value.sort((a: {[x: string]: number}, b: {[x: string]: number}) => {
      let flag = true;
      for (let i = 0; i < index; i++) {
        const prop = spanArr[i].prop;
        flag = flag && a[prop] == b[prop];
      }
      if (flag) {
        if (a[key] < b[key]) {
          return 1;
        } else if (a[key] > b[key]) {
          return 1;
        }
        return 0;
      }
      return 0;
    });
  });
};
const rowSpan = (key: string, parent: never[] | undefined) => {
  const list: number[] = [];
  let position = 0;
  tableData.value.forEach((item: any, index: number) => {
    if (index === 0) {
      list.push(1);
      const position = 0;
    } else {
      if (tableData.value[index][key] === tableData.value[index - 1][key]) {
        if (parent && parent[index] !== 0) {
          list.push(1);
          position = index;
        } else {
          list[position] += 1;
          list.push(0);
        }
      } else {
        list.push(1);
        position = index;
      }
    }
  });
  return list;
};
const getUnitByid = (id: number, data?: object): {id: number; displayName: string; name: string}[] => {
  let resAry: never[] = [];
  props.unitList.forEach((item: any) => {
    if (item.id === id) {
      resAry = item.params;
    }
  });
  // 默认值设置
  if (data) {
    const defaultUnit = resAry.find(i => i.default);
    if (defaultUnit && !data.unitType) data.unitType = defaultUnit.id;
  }
  return resAry;
};
const getSeltctMuntEnum = (id: any) => {
  return envValueMap[id];
};
const changeParamsData = (val: any) => {
  console.log('🚀 ~ changeParamsData ~ val:', val);
};
const changeUnitData = (val: any, datas) => {
  console.log('🚀 ~ changeParamsData ~ val:', val, datas);
  datas.unitObj = getUnitObjByName(val, datas.unitId);
};
const isMenu = (name: string | number) => {
  return envValueMap[name];
};
// 通过name获取label
const getUnitObjByName = (name, unitId) => {
  let result = {};
  props.unitList.forEach((item: any) => {
    if (item.id === unitId) {
      item.params.forEach(params => {
        if (params.name === name) {
          return (result = params);
        }
      });
    }
  });
  return result;
};
const cancelForm = async () => {
  // 已打开想定的状态  重新拿数据并赋值
  if (stateControler.scenarioEditFlag) {
    const res = await getScenarioInfoAPI(stateControler.scenarioId);
    stateControler.weatherCheckList = res.data.environments;
  } else {
    //直接编辑的状态  直接重置
    emits('reset');
  }
  emits('closeVisible');
};
</script>

<template>
  <div class="env-dialog">
    <el-dialog v-model="dialogShow" title="自然环境配置" width="800px" center :before-close="() => emits('closeVisible')">
      <el-table :data="props.tableData" :span-method="arraySpanMethod" border height="500" stripe>
        <el-table-column label="气象类型" prop="type" align="center" width="100"> </el-table-column>
        <el-table-column label="参数" prop="displayName" align="center" width="230"></el-table-column>
        <el-table-column label="值" prop="defaultValue" align="center" width="270">
          <template #default="scope">
            <el-select v-if="isMenu(scope.row.name)" v-model="scope.row.value" @change="changeParamsData">
              <el-option v-for="item in getSeltctMuntEnum(scope.row.name)" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <div v-else-if="scope.row.defaultType === 'struct'" class="structInput">
              <div class="downLine"><el-input-number v-model="scope.row.value[0].value" style="width: 150px;" :min="0" :max="999999" placeholder="请输入下限"> </el-input-number></div>
              <span class="line"></span>
              <div class="topLine"><el-input-number v-model="scope.row.value[1].value" style="width: 150px;" :min="0" :max="999999" placeholder="请输入上限"></el-input-number></div>
            </div>
            <el-input-number v-else v-model="scope.row.value" :min="scope.row.displayName == '风向' ? -180 : 0" :max="scope.row.displayName == '风向' ? 180 : 99999" style="width: 150px;"> </el-input-number>
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" width="188">
          <template #default="scope">
            <!-- {{ scope.row.unitId }} -->
            <el-select
              v-if="scope.row.unitId !== 0"
              v-model="scope.row.unitType"
              @change="
                val => {
                  changeUnitData(val, scope.row);
                }
              "
            >
              <el-option v-for="item in getUnitByid(scope.row.unitId, scope.row)" :key="item.id" :label="item.displayName" :value="item.name" />
            </el-select>
            <div v-else-if="scope.row.defaultType === 'struct'">
              <el-select v-model="scope.row.value[0].unitType" placeholder="请选择下限单位">
                <el-option v-for="item in getUnitByid(scope.row.value[0].unitId, scope.row.value[0])" :key="item.id" :label="item.displayName" :value="item.name" />
              </el-select>
              <span class="line"></span>
              <el-select v-model="scope.row.value[1].unitType" placeholder="请选择上限单位">
                <el-option v-for="item in getUnitByid(scope.row.value[1].unitId, scope.row.value[1])" :key="item.id" :label="item.displayName" :value="item.name" />
              </el-select>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelForm">取 消</el-button>
          <el-button type="primary" @click="saveConfig">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.env-dialog {
  width: 600px;

  --el-fill-color-light: #0e345f;

  .el-table :deep(.cell) {
    line-height: 40px !important;
  }

  .line::before {
    display: inline-block;
    width: 0;
    height: 25px;
    margin: 0 10px;
    color: #5cc9ff;
    vertical-align: middle;
    content: '';
    border-right: 1px solid #5cc9ff;
  }

  .dialog-footer {
    top: 0;
  }

  .structInput {
    display: flex;
    flex-direction: column;
    align-items: center;

    .downLine::before {
      display: inline-block;
      width: 32px;
      height: 25px;
      margin: 0 10px;
      color: #5cc9ff;
      content: '下限:';
    }

    .topLine::before {
      display: inline-block;
      width: 32px;
      height: 25px;
      margin: 0 10px;
      color: #5cc9ff;
      content: '上限:';
    }
  }
}
</style>
