<script setup>
import {onMounted, reactive, ref} from 'vue';
import {addToEntityList, getModelData, editModelList} from '@/utils/data_process';
import {getPlatformTypeTreeAPI} from '@/api/index';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
const stateControler = useStateStore();
const {MapGl, mapInstance} = storeToRefs(useMapStore());
const modeContent = ref(null);
const state = reactive({
  entityResourceData: [], //树节点
  platformTypeId: '', //platformTypeId 模型id
  //模型列表
  defaultProps: {
    children: 'children',
    label: 'name',
    isLeaf: 'leaf',
  },
  modelInfo: [], //模型配置信息
  options: [], //模型集列表
  searchKeywords: '', //搜索模型关键字
});
// 树节点点击事件
const handleNodeClick = (event, node) => {
  // 模型列表图标切换
  if (event.source !== 0) {
    return (stateControler.massDeploymentModelID = node.data.id);
  }
  event.checked = !event.checked;
  const treeNode = document.querySelector('#modelTree .el-tree-node:focus .custom-tree-node');
  const icon = treeNode.querySelector('.icon');
  icon.className = event.checked ? 'icon el-icon-folder' : 'icon el-icon-folder-opened';
};
// 开始拖动
const handleDragStart = node => {
  state.platformTypeId = node.data.id;
  // 监听一次地图鼠标移动事件
  mapInstance.value.map.once('mousemove', addEntity);
};
// 创建实体
const addEntity = async e => {
  const entityId = stateControler.currentEntityId + 1;
  const param = {
    isPlatFormType: true,
    platformTypeId: state.platformTypeId,
    currentModelId: entityId,
    side: stateControler.currentSide,
  };
  if (stateControler.scenarioId) param.scenarioId = stateControler.scenarioId;
  const modelData = await getModelData(param, routeGeojson => {
    const res = routeGeojson;
    if (mapInstance.value.graphicalGeojson.features.some(i => i.displayName === res.displayName)) return;
    mapInstance.value.addGraphical(res);
    //处理航路到选择面板
    mapInstance.value.getDrawData(res);
  });

  modelData.geometry.coordinates = [e.lngLat.lng, e.lngLat.lat];
  modelData.properties.latitude = e.lngLat.lat.toFixed(6);
  modelData.properties.longitude = e.lngLat.lng.toFixed(6);
  // console.log(modelData);
  //!添加实体到地图
  mapInstance.value.addEntity(JSON.parse(JSON.stringify(modelData)));
  mapInstance.value.setHighLight([e.lngLat.lng, e.lngLat.lat]);
  // 添加到实体列表
  // addToEntityList(stateControler.entityListData, modelData.properties);
  stateControler.currentEntityId++;
};
//获取模型资源树
const getPlatformTypeTree = async (name = '') => {
  const {data} = await getPlatformTypeTreeAPI(name);
  if (data) {
    editModelList(data);
    state.entityResourceData = data;
  }
};
</script>

<template>
  <div ref="modeContent" class="entity-resource-content">
    <div class="entity-resource-body">
      <el-tree id="entityResourceTree" ref="entityResourceTree" :props="state.defaultProps" :allow-drop="() => false" :allow-drag="node => node.data.typeFlag !== 0" draggable :data="state.entityResourceData" @node-drag-start="handleDragStart">
        <template #default="{node, data}">
          <span v-if="data.source == 1 && data.data" class="custom-tree-node">
            <span>{{ node.label }}</span>
          </span>

          <span v-else class="custom-tree-node">
            <i :class="data.className"></i>
            <span>{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<style scoped>
/* 模型列表 */
.entity-resource-content {
  height: 100%;
  min-width: 220px;
  border-radius: 5px;
  box-sizing: border-box;
}

.entity-resource-body {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
}

.entity-resource-body .el-select,
.el-input {
  width: 94%;
  margin-bottom: 5px;
  margin-left: 10px;
}
</style>
