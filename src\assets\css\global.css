/* 全局样式设置，element覆盖，公共样式书写 */
:root {
  --selected-font-color: #36b6ff;
  --title-color: #fff;
  --font-color: #92d7ff;
  --icon-color: #5fc5ff;
  --border-color: #097cbc;
  --title-bg-color: #12577f;
  --dialog-bg-color: #022e48;
  --panel-bg-color: #002f49;
  --split-line-color: #3186b3;
  --highlight-color: #006ca9;
  --th-bg-color: #14628f;
  --mask-bg: rgb(0 0 0 / 60%);
  --model-list-left-distance: 70px;
  --nav-left-distance: 0;
  --draw-left-distance: 465px;
  --border: 1px solid var(--border-color);
  --el-border-color: #415c9b !important;
}
@font-face {
  font-family: Medium;
  src: url('../font/medium.OTF');
}
@font-face {
  font-family: Regular;
  src: url('../font/regular.OTF');
}

* {
  box-sizing: border-box;
  margin: 0;
}

#app {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-family: Regular;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  text-align: center;
  background: #002941;
  box-sizing: border-box;
  border: var(--border);
}

body {
  padding: 0;
  margin: 0;
  user-select: none;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 10px;
  box-shadow: inset 0 0 5px var(--border-color);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  appearance: none;
}

a {
  color: #fff !important;
  text-decoration: none !important;
}

ul {
  padding: 0;
  margin: 0;
}

ul li {
  list-style: none;
  cursor: pointer;
}

textarea {
  font-family: 'Helvetica Neue', sans-serif;
}

button {
  -webkit-app-region: no-drag;
}

/* 毛玻璃效果 */
.glass::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  background: var(--dialog-bg-color);
  border: var(--border);
  border-radius: 5px;
  content: '';
  backdrop-filter: blur(5px);
}

.clearfix {
  height: 24px;
  padding-left: 14px;
  line-height: 24px;
  border-left: 4px solid var(--border-color);
}

.clearfix::before,
.clearfix::after {
  display: table;
  content: '';
}

.clearfix::after {
  clear: both;
}

/* 遮罩样式 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  background: var(--mask-bg);
}

/* 左浮动 */
.float-left {
  float: left;
}

.jitter {
  animation: jitter 0.1s 4 linear;
}

/* 抖动动画 */
@keyframes jitter {
  0% {
    transform: translate(0, 0);
  }

  25% {
    transform: translate(10px, 0);
  }

  50% {
    transform: translate(10px, 10px);
  }

  75% {
    transform: translate(0, 10px);
  }

  100% {
    transform: translate(0, 0);
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 项目图标 */
.icon-title {
  display: inline-block;
  width: 50px;
  height: 40px;
  vertical-align: middle;
  background: url('@/assets/images/publish-icon.png') no-repeat;
}

.paramters-table {
  margin-bottom: 10px;
  overflow: auto;
}

/* 模型参数表格的样式 */
.paramters-table .el-form-item__content {
  margin: 5px 10px !important;
  flex-direction: column;
}

.paramters-table .el-input__inner {
  height: 25px;
}

.paramters-table .el-cascader,
.paramters-table .el-select {
  width: 100%;
}

.paramters-table .el-form-item {
  display: flex;
  height: 100%;
  align-items: center;
  margin: 0;
}

/* 模型参数配置中的图片 */
.model-img {
  width: 90px;
  height: 90px;
  margin: 15px;
}

/* 当img标签src为空时、没有src属性时 设置透明解决 */
img[src=''],
img:not([src]) {
  opacity: 0;
}

/* 分页器的盒子样式 */
.pagination-box {
  display: flex;
  justify-content: center;
}

.pagination-box .comfirm {
  height: 32px;
  margin-left: 20px;
  line-height: 32px;
  color: #fff;
}

.margin-bottom {
  margin-bottom: 6px;
}

.maplibregl-ctrl-group {
  position: fixed;
  bottom: 4px;
  left: 700px;
  display: flex;
  height: 25px;
  margin: 0 !important;
  background: transparent !important;
  border-radius: 0 !important;
  border-right: 2px solid var(--split-line-color);
}

.maplibregl-ctrl-group button {
  position: relative;
  bottom: -2px;
  height: 22px !important;
  margin: 0 15px;
}

.maplibregl-ctrl-group button + button:not(.drawActive) {
  border-color: transparent;
}

.maplibregl-ctrl-group .mapbox-gl-draw_polygon {
  display: none;
}

.maplibregl-ctrl-group:not(:empty) {
  box-shadow: none;
}

.maplibregl-ctrl-scale {
  position: fixed;
  right: 320px;
  bottom: 10px;
  display: flex;
  height: 8px;
  margin: 0 !important;
  font-size: 12px !important;
  line-height: 2px;
  color: #fff !important;
  background-color: transparent !important;
  border: 2px solid #ccc !important;
  border-top: 0 !important;
  cursor: default;
}

.maplibregl-popup,
.maplibregl-popup-content {
  /* width: 600px !important; */
  max-width: 600px !important;
  padding: 0 !important;
}

.maplibregl-popup-content {
  background: var(--panel-bg-color) !important;
  border: 1.5px solid var(--title-bg-color);
}

.maplibregl-popup-tip {
  border-top-color: var(--title-bg-color) !important;
}

.maplibregl-popup-close-button {
  display: none;
}

.maplibregl-popup-anchor-top .maplibregl-popup-tip {
  border-bottom-color: var(--border-color) !important;
}

.icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-size: contain !important;
  margin: 2px;
  cursor: pointer;
}

.edit-icon {
  background: url('@/assets/images/edit.png') no-repeat;
}

.export-icon {
  background: url('@/assets/images/export.png') no-repeat;
}

.copy-icon {
  background: url('@/assets/images/copy.png') no-repeat;
}

.delete-icon {
  background: url('@/assets/images/delete.png') no-repeat;
}

.add-icon {
  background: url('@/assets/images/add_icon.png') no-repeat;
}

.border-delete-icon {
  background: url('@/assets/images/delete_icon.png') no-repeat;
}

.network-icon {
  background: url('/images/network.png') no-repeat;
}

.drawActive {
  border: 1px solid #36b6ff !important;
  border-radius: 2px !important;
  border-color: #36b6ff !important;
}

.maplibregl-ctrl-group .maplibregl-ctrl .drawActive {
  border-radius: 2px;
  border-color: #36b6ff !important;
}

/* .maplibregl-canvas-container.maplibregl-interactive,
.maplibregl-ctrl-group button.maplibregl-ctrl-compass {
  cursor: move !important;
} */
