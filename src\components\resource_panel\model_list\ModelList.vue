<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue';
import {move} from '@/utils/tools';
import {addToEntityList, getModelData, editModelList} from '@/utils/data_process';
import {getPlatformTypeTreeAPI, getModelCollectionAPI} from '@/api/index';
import MassDeployment from '@/components/resource_panel/model_list/dialogs/MassDeployment.vue';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
const stateControler = useStateStore();
const {MapGl, mapInstance} = storeToRefs(useMapStore());
import type Node from 'element-plus/es/components/tree/src/model/node';

onMounted(async () => {
  await getPlatformTypeTree();
  const {data} = await getModelCollectionAPI();
  state.collectionList = data.list;
  state.collectionId = state.collectionList[0].id;
  move(modeContent.value);
});

interface Tree {
  [key: string]: any;
}
const modelTree = ref(null);
const modeContent = ref(null);
// 中文名称
const ZN = ref(true);
const state = reactive({
  collectionList: [],
  collectionId: 0,
  modelListData: [], //树节点
  platformTypeId: '', //platformTypeId 模型id
  //模型列表
  defaultProps: {
    children: 'children',
    label: 'name',
    isLeaf: 'leaf',
  },
  modelInfo: [], //模型配置信息
  options: [], //模型集列表
  selectedModeViewId: [], //选择的模型集
  searchKeywords: '', //搜索模型关键字
});
const dragEntity = (node: Node) => node.data.nodeType === 3;
// 树节点点击事件
const handleNodeClick = (node: Node) => {
  // 点击的是平台类型,进行批量部署
  if (node.nodeType! === 3) {
    stateControler.massDeploymentModelID = node.id;
  }
};
// 开始拖动
const handleDragStart = (node: Node) => {
  state.platformTypeId = node.data.id;
  // 监听一次地图鼠标移动事件
  mapInstance.value.map.once('mousemove', addEntity);
};
// 创建实体
const addEntity = async (e: any) => {
  const entityId = stateControler.currentEntityId + 1;
  const param = {
    isPlatFormType: true,
    platformTypeId: state.platformTypeId,
    currentModelId: entityId,
    side: stateControler.currentSide,
  };
  const modelData = await getModelData(param, (routeGeojson: any) => {
    const res = routeGeojson;
    if (mapInstance.value.graphicalGeojson.features.some((i: any) => i.displayName === res.displayName)) return;
    mapInstance.value.addGraphical(res);
    //处理航路到选择面板
    mapInstance.value.getDrawData(res);
  });
  console.log(modelData);
  if (!modelData) return;

  modelData.geometry.coordinates = [e.lngLat.lng, e.lngLat.lat];
  modelData.properties.latitude = e.lngLat.lat.toFixed(6);
  modelData.properties.longitude = e.lngLat.lng.toFixed(6);
  // console.log(modelData);
  //!添加实体到地图
  mapInstance.value.addEntity(JSON.parse(JSON.stringify(modelData)));
  mapInstance.value.setHighLight([e.lngLat.lng, e.lngLat.lat]);
  mapInstance.value.selectFeatures.push(modelData.properties.id);

  // 添加到实体列表
  // addToEntityList(stateControler.entityListData, modelData.properties);
  stateControler.currentEntityId++;
};
//获取模型资源树
const getPlatformTypeTree = async name => {
  const {data} = await getPlatformTypeTreeAPI(name, state.collectionId);
  if (data) state.modelListData = data;
};

//接收更新数据
window.onmessage = e => {
  getPlatformTypeTree();
};

defineExpose({getPlatformTypeTree, ZN});
</script>

<template>
  <div ref="modeContent" class="mode-content">
    <div class="mode-body">
      <el-input v-model="state.searchKeywords" placeholder="请输入搜索关键字" @keyup.enter="getPlatformTypeTree(state.searchKeywords)"></el-input>
      <el-select v-model="state.collectionId" class="type-select" placeholder="请选择" clearable suffix-icon="CaretBottom" @change="getPlatformTypeTree(state.searchKeywords)">
        <el-option v-for="item in state.collectionList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
      </el-select>
      <el-tree id="modelTree" ref="modelTree" default-expand-all draggable :data="state.modelListData" :props="state.defaultProps" node-key="id" :allow-drop="() => false" :allow-drag="dragEntity" @node-drag-start="handleDragStart" @node-click="handleNodeClick">
        <template #default="{data}">
          <span class="custom-tree-node">
            <i v-if="data.nodeType !== 3" class="custom-tree-icon">
              <img v-if="data.iconString" :src="data.iconString" />
            </i>
            <i v-else class="point"></i>
            <span v-if="ZN" :title="data.name" :class="[data.nodeType === 3 ? 'tree-leaf' : '', 'text-ellipsis']">{{ data.displayName }}</span>
            <span v-else :title="data.displayName" class="text-ellipsis">{{ data.name }}</span>
          </span>
        </template>
      </el-tree>
    </div>
    <MassDeployment />
  </div>
</template>

<style lang="less" scoped>
/* 模型列表 */
.mode-content {
  height: calc(100% - 40px);
  min-width: 220px;
  border-radius: 5px;
  box-sizing: border-box;

  .mode-body {
    width: 100%;
    height: 100%;
    box-sizing: border-box;

    .el-select,
    .el-input {
      width: 94%;
      margin: 5px;
    }

    .el-tree {
      height: calc(100% - 80px);
      overflow: auto;
      font-family: Regular !important;
    }
  }

  .custom-tree-node {
    display: flex;
    width: 170px;
    font-size: 15px;
    align-items: center;
    color: #fff;

    span {
      max-width: 170px;
    }

    img {
      margin-right: 12px;
    }
  }
}

:deep(.el-tree > .el-tree-node > .el-tree-node__content .custom-tree-node) {
  padding-left: 10px;
  color: var(--font-color);
}

:deep(.el-tree > .el-tree-node > .el-tree-node__content .el-tree-node__expand-icon) {
  position: absolute;
  right: 0;
  font-size: 20px;
}

:deep(.el-tree-node__expand-icon) {
  font-size: 18px;
  color: var(--icon-color);
}

:deep(.is-leaf) {
  padding: 0;
  margin: 0;
  color: transparent;
}

:deep(.el-tree) {
  /* ---- ---- ---- ---- ^（节点对齐）---- ---- ---- ---- */
  .el-tree-node {
    position: relative;
    width: auto;
    padding-left: 13px;

    &::before {
      position: absolute;
      right: auto;
      bottom: 0;
      left: 0;
      width: 1px;
      height: 100%;
      content: '';
      border-width: 1px;
      border-left: 1px solid #36b6ff;
    }

    &::after {
      position: absolute;
      top: 17px;
      right: auto;
      bottom: auto;
      left: 0;
      z-index: 0;
      width: 13px;
      height: 13px;
      content: '';
      border-width: 1px;
      border-top: 1px solid #36b6ff;
    }

    .el-tree-node__content {
      position: relative;
      z-index: 1;
      height: 35px;
      padding-left: 0 !important;
    }

    .el-tree-node__children {
      padding-left: 20px;
    }

    &:last-child::before {
      top: 0;
      height: 17px;
    }

    /* ^ 叶子节点 */
    i.el-tree-node__expand-icon.is-leaf {
      &::before {
        display: none;
      }
    }
  }

  /* ^ 第一层节点 */
  > .el-tree-node,
  > .el-tree-node > .el-tree-node__children > .el-tree-node {
    padding-left: 0;

    /* stylelint-disable-next-line no-descending-specificity */
    &::before {
      border-left: none;
    }

    &::after {
      border-top: none;
    }
  }

  > .el-tree-node > .el-tree-node__content {
    background-color: #003352;
    border-bottom: solid 1px #115886;
  }
}

.point {
  position: absolute;
  left: 6px;
  width: 6px;
  height: 6px;
  background: #fff;
  border-radius: 50%;
}
</style>
