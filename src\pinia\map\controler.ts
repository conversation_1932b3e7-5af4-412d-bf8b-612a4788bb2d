/*
 * <AUTHOR> laofan
 * @Date         : 2023-03-06 14:17:30
 * @LastEditors: tzs
 * @LastEditTime: 2024-03-11 14:57:17
 * @Description  : 请填写简介
 */
import {defineStore} from 'pinia';
import {reactive, toRefs} from 'vue';

export interface controlerStatusType {
  statusList: Object;
  testStatus: boolean;
  testNumber: number;
}

export const useControler = defineStore('controler', () => {
  const controlerStatus = reactive<controlerStatusType>({
    statusList: {
      // 未初始化
      0: {
        isInit: true,
        isStart: false,
        isPause: false,
        isContinue: false,
        isStop: false,
        isReset: true,
      },
      // 已初始化
      1: {
        isInit: false,
        isStart: true,
        isPause: false,
        isContinue: false,
        isStop: false,
        isReset: true,
      },
      // 已经开始
      2: {
        isInit: false,
        isStart: false,
        isPause: true,
        isContinue: false,
        isStop: true,
        isReset: false,
      },
      // 已暂停状态
      3: {
        isInit: false,
        isStart: false,
        isPause: false,
        isContinue: true,
        isStop: true,
        isReset: true,
      },
      // 已停止状态
      4: {
        isInit: true,
        isStart: false,
        isPause: false,
        isContinue: true,
        isStop: false,
        isReset: true,
      },
    },
    testStatus: false,
    testNumber: 0,
  });
  return toRefs(controlerStatus);
});
