<!--
 * @Author: 老范
 * @Date: 2025-06-27 16:40:16
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-27 16:40:32
 * @Description: 请填写简介
-->
<template>
  <div ref="answerContainer" class="answer-container" :class="{loading: chatStore.isLoading}">
    <div v-if="chatStore.isLoading" class="typing-indicator-container">
      <span>正在分析问题</span>
      <div class="typing-indicator"></div>
      <div class="typing-indicator"></div>
      <div class="typing-indicator"></div>
    </div>

    <div v-html="chatStore.answer"></div>

    <div v-if="!chatStore.isLoading && chatStore.answer" class="security-note">回答已完成</div>
  </div>
</template>

<script setup>
import {useChatStore} from '@/pinia/chat';
import {onMounted, watch, nextTick, ref, onBeforeUnmount} from 'vue';
import hljs from 'highlight.js';

const chatStore = useChatStore();
const answerContainer = ref(null);
const isUserScrolledUp = ref(false);
const lastScrollTop = ref(0);
const isAtBottom = ref(true);

// 检测用户滚动
const handleScroll = () => {
  if (!answerContainer.value) return;

  const container = answerContainer.value;
  const currentScrollTop = container.scrollTop;
  const maxScrollTop = container.scrollHeight - container.clientHeight;

  // 检测是否在底部（允许5px的误差）
  isAtBottom.value = maxScrollTop - currentScrollTop <= 5;

  // 检测滚动方向
  if (currentScrollTop < lastScrollTop.value) {
    // 向上滚动
    isUserScrolledUp.value = true;
  } else if (isAtBottom.value) {
    // 已滚动到底部，重置状态
    isUserScrolledUp.value = false;
  }

  // 更新上次滚动位置
  lastScrollTop.value = currentScrollTop;
};

// 自动滚动到底部
const scrollToBottom = () => {
  if (!answerContainer.value) return;

  // 只有当用户未向上滚动或已经在底部时才自动滚动
  if (!isUserScrolledUp.value || isAtBottom.value) {
    answerContainer.value.scrollTo({
      top: answerContainer.value.scrollHeight,
      behavior: 'smooth',
    });
  }
};

// 高亮代码块
const highlightCodeBlocks = () => {
  document.querySelectorAll('pre code').forEach(block => {
    hljs.highlightElement(block);
  });
};

// 添加复制按钮和折叠功能
const addCopyButtons = () => {
  document.querySelectorAll('pre').forEach(block => {
    if (!block.closest('.code-block')) {
      const wrapper = document.createElement('div');
      wrapper.className = 'code-block';
      block.parentNode.insertBefore(wrapper, block);
      wrapper.appendChild(block);

      // 创建按钮容器
      const btnContainer = document.createElement('div');
      btnContainer.className = 'toggle-btn-container';

      // 创建展开/收起按钮
      const toggleBtn = document.createElement('button');
      toggleBtn.className = 'toggle-btn';
      toggleBtn.innerHTML = '<i class="iconfont iconfold-open"></i>';
      toggleBtn.title = '展开/收起代码';
      btnContainer.appendChild(toggleBtn);

      wrapper.insertBefore(btnContainer, wrapper.firstChild);

      // 创建复制按钮容器
      const copyBtnContainer = document.createElement('div');
      copyBtnContainer.className = 'copy-btn-container';
      copyBtnContainer.style.display = 'none';

      const copyBtn = document.createElement('button');
      copyBtn.className = 'copy-btn';
      copyBtn.textContent = '复制';
      copyBtn.onclick = () => {
        const codeElement = block.querySelector('code');
        const code = codeElement ? codeElement.textContent : block.textContent;
        navigator.clipboard.writeText(code);
        copyBtn.textContent = '已复制!';
        setTimeout(() => (copyBtn.textContent = '复制'), 2000);
      };

      copyBtnContainer.appendChild(copyBtn);
      block.appendChild(copyBtnContainer);

      // 默认隐藏状态
      let isCollapsed = true;
      block.style.maxHeight = '0';
      block.style.padding = '0 1em';
      block.style.overflow = 'hidden';
      copyBtnContainer.style.display = 'none';

      // 切换展开/收起
      toggleBtn.onclick = () => {
        isCollapsed = !isCollapsed;
        if (isCollapsed) {
          block.style.maxHeight = '0';
          block.style.padding = '0 1em';
          block.style.overflow = 'hidden';
          copyBtnContainer.style.display = 'none';
          toggleBtn.innerHTML = '<i class="iconfont iconfold-open"></i>';
        } else {
          block.style.maxHeight = 'none';
          block.style.padding = '1em';
          block.style.overflow = 'visible';
          copyBtnContainer.style.display = 'block';
          toggleBtn.innerHTML = '<i class="iconfont iconfold"></i>';

          // 重新高亮代码
          const codeElement = block.querySelector('code');
          if (codeElement) {
            hljs.highlightElement(codeElement);
          }
        }
      };
    }
  });
};

onMounted(() => {
  highlightCodeBlocks();
  addCopyButtons();

  // 添加滚动事件监听
  if (answerContainer.value) {
    answerContainer.value.addEventListener('scroll', handleScroll);
  }

  // 模拟真实环境测试 - 添加各种代码块
  //   setTimeout(() => {
  //     const testContent = `
  //       <h3>代码块测试</h3>
  //       <p>以下是各种类型的代码块，默认应该被隐藏：</p>

  //       <h4>1. JSON 代码块</h4>
  //       <pre><code class="language-json">{
  //   "name": "测试用户",
  //   "age": 25,
  //   "email": "<EMAIL>",
  //   "address": {
  //     "street": "测试街道",
  //     "city": "测试城市",
  //     "country": "中国"
  //   },
  //   "hobbies": ["阅读", "编程", "旅行"],
  //   "settings": {
  //     "theme": "dark",
  //     "language": "zh-CN",
  //     "notifications": true
  //   },
  //   "metadata": {
  //     "created": "2024-01-01",
  //     "updated": "2024-01-02",
  //     "version": "1.0.0"
  //   }
  // }</code></pre>

  //       <h4>2. JavaScript 代码块</h4>
  //       <pre><code class="language-javascript">function calculateSum(a, b) {
  //   return a + b;
  // }

  // const result = calculateSum(10, 20);
  // console.log('结果:', result);

  // // 这是一个复杂的JavaScript函数
  // function processData(data) {
  //   return data
  //     .filter(item => item.active)
  //     .map(item => ({
  //       id: item.id,
  //       name: item.name.toUpperCase(),
  //       timestamp: new Date().toISOString()
  //     }))
  //     .sort((a, b) => a.timestamp.localeCompare(b.timestamp));
  // }</code></pre>

  //       <h4>3. Python 代码块</h4>
  //       <pre><code class="language-python">def fibonacci(n):
  //     if n <= 1:
  //         return n
  //     return fibonacci(n-1) + fibonacci(n-2)

  // # 计算斐波那契数列
  // for i in range(10):
  //     print(f"F({i}) = {fibonacci(i)}")

  // # 数据处理示例
  // import pandas as pd
  // import numpy as np

  // data = pd.DataFrame({
  //     'name': ['Alice', 'Bob', 'Charlie'],
  //     'age': [25, 30, 35],
  //     'salary': [50000, 60000, 70000]
  // })

  // print(data.describe())</code></pre>

  //       <h4>4. CSS 代码块</h4>
  //       <pre><code class="language-css">.container {
  //   display: flex;
  //   flex-direction: column;
  //   gap: 20px;
  //   padding: 20px;
  //   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  //   border-radius: 12px;
  //   box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  // }

  // .button {
  //   padding: 12px 24px;
  //   background: #2563eb;
  //   color: white;
  //   border: none;
  //   border-radius: 8px;
  //   cursor: pointer;
  //   transition: all 0.3s ease;
  // }

  // .button:hover {
  //   background: #1d4ed8;
  //   transform: translateY(-2px);
  //   box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
  // }</code></pre>

  //       <h4>5. HTML 代码块</h4>
  //       <pre><code class="language-html">&lt;!DOCTYPE html&gt;
  // &lt;html lang="zh-CN"&gt;
  // &lt;head&gt;
  //     &lt;meta charset="UTF-8"&gt;
  //     &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
  //     &lt;title&gt;测试页面&lt;/title&gt;
  //     &lt;link rel="stylesheet" href="styles.css"&gt;
  // &lt;/head&gt;
  // &lt;body&gt;
  //     &lt;div class="container"&gt;
  //         &lt;header&gt;
  //             &lt;h1&gt;欢迎使用&lt;/h1&gt;
  //             &lt;p&gt;这是一个测试页面&lt;/p&gt;
  //         &lt;/header&gt;
  //         &lt;main&gt;
  //             &lt;section class="content"&gt;
  //                 &lt;h2&gt;主要内容&lt;/h2&gt;
  //                 &lt;p&gt;这里是一些示例内容。&lt;/p&gt;
  //             &lt;/section&gt;
  //         &lt;/main&gt;
  //         &lt;footer&gt;
  //             &lt;p&gt;&copy; 2024 测试网站&lt;/p&gt;
  //         &lt;/footer&gt;
  //     &lt;/div&gt;
  //     &lt;script src="script.js"&gt;&lt;/script&gt;
  // &lt;/body&gt;
  // &lt;/html&gt;</code></pre>

  //       <p>测试完成！所有代码块都使用统一的折叠逻辑，默认隐藏，点击"展开"按钮可以查看完整内容。</p>
  //     `;

  //     // 将测试内容添加到回答容器
  //     const testDiv = document.createElement('div');
  //     testDiv.innerHTML = testContent;
  //     answerContainer.value.appendChild(testDiv);

  //     // 重新执行代码高亮和折叠功能
  //     setTimeout(() => {
  //       highlightCodeBlocks();
  //       addCopyButtons();
  //     }, 200);
  //   }, 1000);
});

onBeforeUnmount(() => {
  // 移除事件监听器
  if (answerContainer.value) {
    answerContainer.value.removeEventListener('scroll', handleScroll);
  }
});

watch(
  () => chatStore.answer,
  () => {
    nextTick(() => {
      highlightCodeBlocks();
      addCopyButtons();
      scrollToBottom();
    });
  }
);

// 监听加载状态变化
watch(
  () => chatStore.isLoading,
  newLoading => {
    if (newLoading) {
      // 开始新的回答，重置滚动状态
      isUserScrolledUp.value = false;
      nextTick(() => {
        scrollToBottom();
      });
    }
  }
);
</script>

<style>
.typing-indicator-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 0;
}

.typing-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: var(--primary-color);
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-indicator:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-8px);
  }
}

/* 代码块样式 */
.code-block {
  position: relative;
  margin: 1em 0;
}

.code-block pre {
  position: relative;
  margin: 0;
  padding: 1em;
  background-color: var(--code-bg-light);
  border-radius: 8px;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

body.dark-mode .code-block pre {
  background-color: var(--code-bg-dark);
}

.code-block pre.is-collapsed {
  max-height: 0;
  padding: 0 1em;
}

.code-block pre:not(.is-collapsed) {
  max-height: none;
}

.code-block code {
  display: block;
  font-family: 'Fira Code', monospace;
  line-height: 1.5;
  tab-size: 2;
}

.toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  padding: 0;
}

.toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.toggle-btn:hover::before {
  left: 100%;
}

.toggle-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.toggle-btn i {
  font-size: 34px;
  line-height: 1;
}

body.dark-mode .toggle-btn {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  box-shadow: 0 4px 12px rgba(76, 81, 191, 0.4);
}

body.dark-mode .toggle-btn:hover {
  background: linear-gradient(135deg, #434190 0%, #4c1d95 100%);
  box-shadow: 0 8px 20px rgba(76, 81, 191, 0.5);
}

body.dark-mode .toggle-btn:active {
  box-shadow: 0 2px 8px rgba(76, 81, 191, 0.4);
}

.copy-btn-container {
  position: absolute;
  top: 6px;
  right: 6px;
  z-index: 10;
}

.copy-btn {
  padding: 3px 6px;
  border: none;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.9);
  color: #2563eb;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  min-width: 40px;
  text-align: center;
}

.copy-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

body.dark-mode .copy-btn {
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.dark-mode .copy-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

/* iframe 内的聊天容器样式 */
.iframe-chat-section .answer-container {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  height: 100%;
  overflow-y: auto;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  font-size: 14px;
  line-height: 1.6;
  position: relative;
  scroll-behavior: smooth;
  box-sizing: border-box;
  word-break: break-word;
  overflow-x: hidden;
}

/* 在 iframe 内时的代码块样式 */
.iframe-chat-section .answer-container pre {
  overflow-x: auto;
  word-break: break-word;
  white-space: pre-wrap;
  max-width: 100%;
  font-size: 12px;
}

/* 通用样式保持不变 */
.chat-container {
  min-width: 0;
  flex: 2 2 0;
  max-width: 70vw;
}

.answer-container {
  width: 100%;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
  word-break: break-all;
  overflow-x: hidden;
}

.answer-container pre {
  overflow-x: auto;
  word-break: break-all;
  white-space: pre;
  max-width: 55vw;
}

.toggle-btn-container {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: 15px;
}

@media (max-width: 780px) {
  html,
  body {
    width: 100vw;
    overflow-x: hidden;
  }
  .main-content {
    flex-direction: column !important;
    align-items: stretch !important;
  }
  .chat-container {
    max-width: 90vw !important;
    width: 90vw !important;
    min-width: 0 !important;
    flex: 1 1 auto !important;
    margin: 0 !important;
  }
  .settings-panel {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    margin: 0 !important;
    border-radius: 0 0 12px 12px !important;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
    order: 2;
  }
  .input-container,
  .settings-panel textarea {
    width: 100% !important;
    max-width: 100vw !important;
    min-width: 0 !important;
    box-sizing: border-box;
  }
}

.main-content {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100vw;
  /* min-height: 100vh; */
}
</style>
