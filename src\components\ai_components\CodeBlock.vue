<!--
 * @Author: <PERSON>
 * @Description: 代码块组件，默认隐藏代码内容
-->
<template>
  <div class="code-block">
    <div class="code-actions">
      <button class="toggle-btn" @click="toggleCollapse">
        <i class="iconfont" :class="isCollapsed ? 'iconfold-open' : 'iconfold'"></i>
        {{ isCollapsed ? '展开' : '收起' }}
      </button>
    </div>
    <pre ref="codeContainer" :class="{'is-collapsed': isCollapsed}">
      <code ref="codeElement"><slot></slot></code>
      <div v-if="!isCollapsed" class="copy-btn-container">
        <button class="copy-btn" @click="copyCode">
          {{ copyStatus }}
        </button>
      </div>
    </pre>
  </div>
</template>

<script setup>
import {ref, onMounted, nextTick} from 'vue';
import hljs from 'highlight.js';

const props = defineProps({
  language: {
    type: String,
    default: '',
  },
});

const isCollapsed = ref(true);
const codeElement = ref(null);
const codeContainer = ref(null);
const copyStatus = ref('复制');

// 切换展开/收起状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;

  if (!isCollapsed.value) {
    nextTick(() => {
      if (codeElement.value) {
        hljs.highlightElement(codeElement.value);
      }
    });
  }
};

// 复制代码内容
const copyCode = async () => {
  if (!codeElement.value) return;

  try {
    await navigator.clipboard.writeText(codeElement.value.textContent);
    copyStatus.value = '已复制!';
    setTimeout(() => {
      copyStatus.value = '复制';
    }, 2000);
  } catch (err) {
    console.error('复制失败:', err);
    copyStatus.value = '复制失败';
    setTimeout(() => {
      copyStatus.value = '复制';
    }, 2000);
  }
};

onMounted(() => {
  if (codeElement.value) {
    // 如果指定了语言，添加对应的类
    if (props.language) {
      codeElement.value.classList.add(`language-${props.language}`);
    }
    // 默认收起状态不执行高亮，等展开时再高亮
    if (!isCollapsed.value) {
      hljs.highlightElement(codeElement.value);
    }
  }
});
</script>

<style scoped>
.code-block {
  position: relative;
  margin: 1em 0;
}

.code-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.toggle-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

body.dark-mode .toggle-btn {
  background: rgba(0, 0, 0, 0.2);
  color: var(--text-dark);
}

body.dark-mode .toggle-btn:hover {
  background: rgba(0, 0, 0, 0.3);
}

.code-block pre {
  position: relative;
  margin: 0;
  padding: 1em;
  background-color: var(--code-bg-light);
  border-radius: 8px;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

body.dark-mode .code-block pre {
  background-color: var(--code-bg-dark);
}

.code-block pre.is-collapsed {
  max-height: 0;
  padding: 0 1em;
}

.code-block pre:not(.is-collapsed) {
  max-height: none;
}

.code-block code {
  display: block;
  font-family: 'Fira Code', monospace;
  line-height: 1.5;
  tab-size: 2;
}

.copy-btn-container {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.copy-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  color: #2563eb;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.copy-btn:hover {
  background: rgba(255, 255, 255, 1);
}

body.dark-mode .copy-btn {
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
}

body.dark-mode .copy-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}
</style>
