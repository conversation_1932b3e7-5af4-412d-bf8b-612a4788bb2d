<!--
 * @Author: 老范
 * @Date: 2024-07-12 16:07:16
 * @LastEditors: 老范
 * @LastEditTime: 2024-09-23 09:31:55
 * @Description: 请填写简介
-->
<script setup lang="ts">
import {onMounted, reactive, ref, Ref, computed, watch} from 'vue';
import {ElMessage} from 'element-plus';
import {getGroupListApi, addGroupApi, deleteGroupApi, updateGroupApi, getGroupByIdApi, updataGroupNameApi} from '@/api/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
import {generateUuid} from '@/utils/tools';
import {entityParamProcess} from '@/utils/data_process';
import {useStateStore} from '@/pinia/state/index';
import {GeoJsonDataType, featureType} from '@/types';

interface chainArrayType {
  name?: string;
  displayName?: string;
  id: string;
  entityId?: string;
  type?: string;
  pid: string | number;
  chainIndex?: number;
  children: chainArrayType[];
}
interface groupType {
  children: any[];
  displayName: string;
  id: string;
  isEdit: boolean;
}

const {mapInstance} = storeToRefs(useMapStore());

const entitys = computed(() => mapInstance.value.geojson.features.map((i: any) => i));
const stateControler = useStateStore();

const dialogVisible = ref(false);
const isEdit = ref(false);
const groupSource: Ref<groupType[]> = ref([]);
const groupForm = reactive({displayName: '', id: ''});
const expandedKeys = ref([]);
let entityTreeRef = ref(null);

watch(
  () => stateControler.entity2GroupKeys,
  val => {
    getEntitiesByCheckdId(entitys.value, val);
  },
  {deep: true}
);

const addGroupVisible = () => {
  if (!isEdit.value) {
    groupForm.displayName = '';
    groupForm.id = '';
  }
  dialogVisible.value = true;
};
const getGroupDatas = async (showCheckbox = false) => {
  if (entityTreeRef.value) {
    const arr = Object.values(entityTreeRef.value.store.nodesMap).filter(e => e.expanded === true);
    expandedKeys.value = arr.map(item => item.data.id);
    console.log(entityTreeRef.value, arr, expandedKeys.value);
  }
  let isEditRember = groupSource.value.find(e => e.isEdit === true);
  console.log(groupSource, isEditRember);

  const res = await getGroupListApi();
  groupSource.value = res.data.list?.map((item: any) => {
    return {
      ...item,
      isEdit: item.id === (isEditRember && isEditRember.id) ? true : false,
    };
  });

  stateControler.showCheckbox = showCheckbox;
};
const changeCheck = (val: any, data: any, node: any) => {
  node.expanded = true;
  groupSource.value.forEach(item => {
    if (item.id !== data.id) {
      item.isEdit = false;
    }
  });
  // return;
  groupForm.displayName = data.displayName;
  groupForm.id = data.id;
  isEdit.value = val;
  stateControler.showCheckbox = val;
};
const creatGroup = async () => {
  if (isEdit.value) {
    const res = await updataGroupNameApi({displayName: groupForm.displayName}, groupForm.id);
    if (res.code === 200) {
      groupForm.displayName = '';
      groupForm.id = '';
      getGroupDatas();
    }
    // 编辑
  } else {
    const res = await addGroupApi({displayName: groupForm.displayName});
    if (res.code === 200) {
      groupForm.displayName = '';
      getGroupDatas();
    }
  }

  dialogVisible.value = false;
};
const removeGroup = async (data: {id: string | number}) => {
  isEdit.value = false;
  const res = await deleteGroupApi(data.id);
  if (res.code === 200) {
    ElMessage.success(res.msg);
    getGroupDatas();
  }
};
const removeGroupItem = async (node: {parent: {data: {id: string | number; displayName: any}}}, data: {id: string}) => {
  const res = await getGroupByIdApi(node.parent.data.id);
  const mapInfo = JSON.parse(res.data.mapInfo);
  const newChildren = res.data.children.filter((item: {id: string}) => {
    return item.id !== data.id;
  });
  const newMapInfo: GeoJsonDataType = {
    type: 'FeatureCollection',
    features: [],
  };
  newMapInfo.features = mapInfo.features.filter((item: {properties: {id: string}}) => {
    return item.properties.id !== data.id;
  });
  let newcmdChain = stateControler.commandChainData.filter((item: {side: string}) => {
    return item.side === stateControler.currentSide;
  });
  if (newcmdChain.length > 0) {
    newcmdChain = newcmdChain[0].content;
  }
  // 只留下勾选进编组内的指挥关系模型
  const newChainTree = delUnCheckedEntityInChain(newcmdChain);
  const updataRes = await updateGroupApi(
    {
      displayName: node.parent.data.displayName,
      mapInfo: JSON.stringify(newMapInfo),
      children: newChildren,
      cmdChain: JSON.stringify(newChainTree),
      id: node.parent.data.id,
    },
    node.parent.data.id
  );
  if (updataRes.code === 200) {
    getGroupDatas(true);
  }
  stateControler.showCheckbox = true;
};
const getEntitiesByCheckdId = (entitys: any[], keys: any[]) => {
  isEdit.value = false;
  groupSource.value.forEach(el => {
    el.isEdit = false;
  });
  const groupMapinfo: GeoJsonDataType = {
    type: 'FeatureCollection',
    features: [],
  };
  let newcmdChain = stateControler.commandChainData.filter((item: {side: string}) => {
    return item.side === stateControler.currentSide;
  });
  if (newcmdChain.length > 0) {
    newcmdChain = newcmdChain[0].content;
  }
  const models: {displayName: string; name: string; platformTypeId: number; modules: any; params: any; id: string | undefined}[] = [];
  entitys.forEach((item: featureType) => {
    if (keys.includes(item.properties.entityId)) {
      const uuid = generateUuid();
      const mapData: any = stateControler.entityModPar.get(item.properties.id);
      let modules = null;
      if (mapData?.modules) {
        modules = mapData?.modules.map((moduleItem: {id: string}) => {
          moduleItem.id = generateUuid();
          return {...moduleItem};
        });
      }
      newcmdChain.forEach(chainItem => {
        chainItem.children.forEach((entityItem: {id: string; displayName: string}) => {
          if (entityItem.id === item.properties.id) {
            entityItem.id = uuid;
            // entityItem.displayName = item.properties.displayName + '_' + item.properties.entityId;
          }
        });
      });
      const entityFeatureInfo = {
        displayName: item.properties.entityDisplayName,
        name: item.properties.entityName,
        platformTypeId: item.properties.platformTypeId,
        modules: modules,
        params: stateControler.entityModPar.get(item.properties.id)?.params || null,
        id: uuid,
      };
      item.properties.id = uuid;
      groupMapinfo.features.push(item);
      models.push(entityFeatureInfo);
    }
  });

  updateGroup(groupMapinfo, models, newcmdChain);
};
const updateGroup = async (mapInfo: GeoJsonDataType, models: any[], cmdChain: any[]) => {
  // 只留下勾选进编组内的指挥关系模型
  const newChainTree = delUnCheckedEntityInChain(cmdChain);
  const res = await updateGroupApi(
    {
      displayName: groupForm.displayName,
      mapInfo: JSON.stringify(mapInfo),
      children: models,
      cmdChain: JSON.stringify(newChainTree),
      id: groupForm.id,
    },
    groupForm.id
  );
  getGroupDatas();
};
const delUnCheckedEntityInChain = (newcmdChain: any) => {
  const chainArr = treeToArray(newcmdChain, 0);
  const checkedChain = chainArr.filter(entity => {
    return stateControler.entity2GroupKeys.includes(entity.entityId) || entity.type !== 'entity';
  });
  return flatArrayToTree(checkedChain);
};

// 扁平转树
const flatArrayToTree = (flatData: chainArrayType[]) => {
  const result: chainArrayType[] = [];
  const map: {[proName: string]: chainArrayType} = {};
  flatData.forEach(item => {
    map[item.id] = {...item, children: []};
  });
  flatData.forEach(item => {
    if (item.pid !== 0) {
      map[item.pid].children.push(map[item.id]);
    } else {
      result.push(map[item.id]);
    }
  });
  return result;
};
const group2Entity = async (data: {id: string | number}) => {
  const sideIndex = stateControler.currentSide === 'red' ? 0 : 1;
  const sidecolor = stateControler.entityListData[sideIndex].color;
  const res = await getGroupByIdApi(data.id);
  const mapInfo = JSON.parse(res.data.mapInfo);
  const cmdChain = JSON.parse(res.data.cmdChain);

  mapInfo.features.forEach((feature: featureType) => {
    const uuid = generateUuid();
    res.data.children.forEach((item: {id: any}) => {
      if (item.id === feature.properties.id) {
        cmdChain.forEach((chainItem: {children: any[]}) => {
          chainItem.children.forEach((item: {id: string; displayName: string}) => {
            if (item.id === feature.properties.id) {
              item.id = uuid;
              item.displayName = feature.properties.displayName + '_' + feature.properties.entityId;
            }
          });
        });
        const data = entityParamProcess(item);
        data.id = uuid;
        feature.properties.id = uuid;
        feature.properties.entityId = stateControler.currentEntityId + 1;
        stateControler.currentEntityId += 1;
        feature.properties.side = stateControler.currentSide;
        feature.properties.color = sidecolor;
        feature.properties.entityName = feature.properties.displayName + '_' + feature.properties.entityId;
        feature.properties.entityDisplayName = feature.properties.displayName + '_' + feature.properties.entityId;
        stateControler.entityModPar.set(uuid, data);
        mapInstance.value.geojson.features.push(feature);
      }
    });
  });
  mergeCmdChain(cmdChain);
  mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
};
const mergeCmdChain = (groupChain: any[]) => {
  // 阵营
  const sideIndex = stateControler.currentSide === 'red' ? 0 : 1;
  groupChain.forEach((item: {name: string; children: any[]}) => {
    if (item.name === 'default') {
      item.children.forEach((ele: any) => {
        stateControler.commandChainData[sideIndex].content[0].children.push(ele);
      });
    } else {
      stateControler.commandChainData[sideIndex].content.push(item);
    }
  });
};
const focusEntity = (e: any) => {};
/**
 * @description: 树结构扁平化
 * */
const treeToArray = (tree: any, pid: number) => {
  let res: any[] = [];
  for (const item of tree) {
    const {children, ...i} = item;
    if (children && children.length) {
      res = res.concat(treeToArray(children, item.id));
    }
    i.pid = pid;
    res.push(i);
  }
  return res;
};
onMounted(() => {
  getGroupDatas();
});
</script>
<template>
  <div v-show="stateControler.groupVisible" class="groupBox">
    <div class="resource-title">
      编组设计
      <div class="ctrl-btn"></div>
    </div>
    <div class="addBtn" @click="addGroupVisible">{{ isEdit ? '编辑名称' : '新建编组' }}</div>
    <!-- show-checkbox -->
    <el-tree id="entity-tree" ref="entityTreeRef" :accordion="true" :data="groupSource" :props="{label: 'displayName'}" node-key="id" :default-expanded-keys="expandedKeys">
      <template #default="{node, data}">
        <span v-if="node.level !== 1" class="custom-tree-node text-ellipsis">
          <span :title="node.label" class="node-label text-ellipsis">
            {{ node.label }}
          </span>
          <span class="node-button">
            <!-- <i title="复制" class="icon copy-icon" @click.stop="copyEntity(data)"> </i>
            <i title="编辑" class="icon edit-icon" @click.stop="editEntity(data)"> </i> -->

            <el-popconfirm title="确定删除吗?" @confirm="removeGroupItem(node, data)">
              <template #reference>
                <i v-show="node.parent.data.isEdit" title="删除" class="icon delete-icon"> </i>
              </template>
            </el-popconfirm>
          </span>
        </span>
        <span v-else class="custom-tree-node text-ellipsis">
          <span :title="node.label" class="pnode-label text-ellipsis"> {{ node.label }}</span>
          <span class="editBtn">
            <el-icon title="部署" @click="group2Entity(data)"><Bottom /></el-icon>
            <el-popconfirm title="确定删除吗?" @confirm="removeGroup(data)">
              <template #reference>
                <i title="删除" class="icon delete-icon"> </i>
              </template>
            </el-popconfirm>
            <el-switch
              v-model="data.isEdit"
              @click.stop
              @change="
                (e: any) => {
                  changeCheck(e, data,node);
                }
              "
            />
          </span>
        </span>
      </template>
    </el-tree>
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑编组' : '创建编组'" width="30%" center :close-on-click-modal="false" :modal-append-to-body="false" :before-close="() => (dialogVisible = false)">
      <el-form ref="form" :model="groupForm" label-width="80px">
        <el-form-item label="编组名称">
          <el-input v-model="groupForm.displayName"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="() => (dialogVisible = false)">取 消</el-button>
          <el-button type="primary" @click="creatGroup">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less">
.groupBox {
  position: fixed;
  top: 45px;
  right: 300px;
  z-index: 99;
  width: 255px;
  height: 700px;
  color: #fff;
  background: var(--panel-bg-color);
  border-radius: 5px;
  box-sizing: border-box;

  .resource-title {
    display: flex;
    height: 40px;
    padding: 15px;
    font-family: Medium;
    font-size: 16px;
    line-height: 40px;
    text-align: left;
    background: url('@/assets/images/title-bg.png');
    align-items: center;
    justify-content: space-between;

    .ctrl-btn {
      display: flex;
      align-items: center;

      .lang {
        margin-left: 8px;
        cursor: pointer;
      }
    }
  }

  #entity-tree {
    height: 90%;
    overflow-y: scroll;
  }

  .custom-tree-node {
    display: flex;
    width: 240px;
    height: 100%;
    padding-right: 10px;
    font-size: 15px;
    text-align: left !important;
    align-items: center;
    justify-content: space-between;

    .editBtn {
      display: flex;
      align-items: center;
    }

    .icon {
      width: 14px;
      height: 14px;
    }
  }

  .addBtn {
    position: relative;
    top: 0;
    left: 170px;
    width: 70px;
    height: 30px;
    margin: 5px 0;
    line-height: 30px;
    text-align: center;
    border: 1px solid #0bb6cd;
    cursor: pointer;
  }

  .dialog-footer {
    position: relative;
    top: 0;
  }
}

.el-button {
  min-width: 30px !important;
}
</style>
