<!--
 * @Description: 用户子网创建
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-11 09:18:09
 * @LastEditors: tzs
 * @LastEditTime: 2025-03-10 09:57:35
-->
<script setup>
import {reactive, computed, ref, watch, nextTick, onMounted} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {getNetworkTypeApi, getModelDataParamApi} from '@/api/index';
import {validateIPv4WithSubnetMask} from '@/utils/tools';

const emit = defineEmits(['update:userSubnetVisible', 'addSubNet', 'editSubNet']);
const props = defineProps({
  userSubnetVisible: {
    type: Boolean,
    default: false,
  },
  networkType: {
    type: Number,
    default: 1,
  },
  currentSide: {
    type: String,
    default: '',
  },
  currentSideEntityList: {
    type: Array,
    default: () => [],
  },
  comms: {
    type: Array,
    default: () => [],
  },
  editData: {
    type: Object,
    default: () => ({}),
  },
  userNetworkDataList: {
    type: Array,
    default: () => [],
  },
});
const dialogVisible = computed(() => props.userSubnetVisible);
const treeRef = ref();
const isEdit = ref(false);
const commList = ref([]);

const form = ref({
  displayName: '',
  name: '',
  networkType: '',
  params: [],
});
// 网络类型列表
const networkTypeList = ref([]);
const netData = ref([]);
const onlySelf = ref(false);

const setDisabled = data => {
  data.forEach(item => {
    item.disabled = props.networkType === 2 ? true : false;
    if (item.children && item.children.length > 0) setDisabled(item.children);
  });
};
// 编辑回显数据
watch(
  () => isEdit.value,
  val => {
    if (!val) return;
    nextTick(() => {
      console.log(props.editData, '编辑的网络数据');
      form.value.name = props.editData.name;
      form.value.displayName = props.editData.displayName;
      form.value.networkType = Number(props.editData.networkType);
      treeRef.value.setCheckedKeys(props.editData.children.map(i => i.uniqueId));
      setType(); //重新设置网络类型的对应的参数
      form.value.params = props.editData.params;
      processData(form.value);
      console.log(form.value);
    });
  }
);
// 弹窗显隐
watch(dialogVisible, value => {
  if (!value) return clearData();
  netData.value = [];
  // 获取并处理成员列表数据
  props.currentSideEntityList.forEach(item => {
    props.comms.get(item.id).comms.forEach(i => (i.platformName = item.entityName));
    netData.value.push({
      id: item.id,
      displayName: item.entityDisplayName,
      name: item.entityName,
      children: props.comms.get(item.id).comms,
    });
  });
  setDisabled(netData.value);
  // 如果是添加网络时，无法添加已经存在于网络的节点
  nextTick(() => {
    if (!isEdit.value) treeRef.value.filter();
  });
  console.log(netData.value, '成员列表数据');
});
// 只显示本网络成员
watch(
  () => onlySelf.value,
  val => {
    if (!val) return treeRef.value.filter([]);
    treeRef.value.filter(props.editData.children.map(i => i.uniqueId));
  }
);

// 当前选择的网络类型如果为默认通用网络，那么其中的链路参数的两个平台不能选中同一个实体
const setLinkFilter = () => {
  if (Number(form.value.networkType) === 10579) {
    const comm_link_list = form.value.params.find(i => i.name === 'comm_link_list');
    // 通信终端链接列表参数的index为0的就是链路参数
    const platform_names = comm_link_list.params[0].configureJson.filter(i => i.name === 'platform_name').map(i => i.defaultValue);
    return platform_names;
  } else {
    return [];
  }
};

// 选择平台
const selectedPlatform = value => {
  const currentEntity = props.currentSideEntityList.find(i => i.entityName === value);
  commList.value = props.comms.get(currentEntity.id).comms;
};

// 设置网络类型对应的参数
const setType = () => {
  const data = networkTypeList.value.find(i => i.id === Number(form.value.networkType));
  console.log(data, '选择的网络类型的参数');
  if (!data) return;

  form.value.params = data?.params ? data.params : [];
};

// 将struct的configjson转为objec,setValue为是否对参数进行赋值（界面使用configureJson渲染所以需要赋值到value）
const processData = (data, setValue = false) => {
  data.params.forEach(item => {
    if (item.defaultType === 'struct' && item.configureJson) {
      item.configureJson = typeof item.configureJson === 'string' ? JSON.parse(item.configureJson) : item.configureJson;
      const data = item.value && typeof item.value === 'string' ? JSON.parse(item.value) : item.value;
      if (!data) return;
      // 如果是设置值，则需要把configureJson的defaultValue填入value
      if (setValue) {
        item.configureJson.forEach((ele, idx) => {
          data[idx].value = ele.defaultValue;
        });
        item.value = JSON.stringify(data);
      } else {
        // 反之，则需要把value 的value填入configureJson的defaultValue（回显）
        data.forEach((ele, idx) => {
          console.log(ele);
          item.configureJson[idx].defaultValue = ele.value;
        });
      }
    }
    if (item.defaultType === 'none' && item.isBlockParam === 2) {
      processData(item, setValue);
    }
  });
};
// 保存用户子网数据
const saveSubNet = () => {
  if (form.value.name === '') return ElMessage.warning('请输入网络名称');
  if (form.value.displayName === '') return ElMessage.warning('请输入显示名称');
  const allSelectedModel = treeRef.value.getCheckedNodes(true);
  if (allSelectedModel.length === 0) return ElMessage.warning('请确保至少有一个成员');

  const networkAddressValue = form.value.params.find(i => i.displayName === '网络地址').value;
  // 验证网络地址的合理性
  const res = validateIPv4WithSubnetMask(networkAddressValue);
  if (!res.valid && networkAddressValue !== '') return ElMessage.warning(res.errorMessage);
  const comm_list = form.value.params.find(i => i.name === 'comm_list');
  processData(form.value, true);
  console.log(allSelectedModel, '选中的实体');
  const commResData = allSelectedModel.map(i => [
    {value: i.platformName, name: 'platform_name'},
    {value: i.name, name: 'comm_name'},
  ]);
  commResData.forEach((item, index) => {
    const data = JSON.parse(JSON.stringify(comm_list.params[0]));
    data.value = JSON.stringify(item);
    comm_list.params[index] = data;
  });
  if (!isEdit.value) {
    const repeat = props.userNetworkDataList.find(i => i.name === form.value.name);
    if (repeat) return ElMessage.warning('用户子网名称重复，请修改');
    emit('addSubNet', {
      side: props.currentSide,
      children: allSelectedModel,
      ...form.value,
    });
  } else {
    emit('editSubNet', {
      side: props.currentSide,
      children: allSelectedModel,
      ...form.value,
    });
  }
  emit('update:userSubnetVisible', false);
};

// 过滤函数，ids为已经添加过的终端id组成的数组
const filterNode = (ids, data) => {
  const filterIds = props.userNetworkDataList
    .map(i => i.children.map(i => i.id))
    .flat(2)
    .concat(ids)
    .filter(i => i);

  // 如果为添加网络,id不传，则过滤掉已经添加过的终端、实体
  if (!ids) return !filterIds.includes(data.uniqueId) && data.uniqueId;
  // 如果为编辑网络id传空数组，显示所有数据
  if (ids.length === 0) return true;
  // 如果为只显示当前网络成员，传递id，只显示传递的
  return filterIds.includes(data.uniqueId);
};

const clearData = () => {
  Object.keys(form.value).forEach(i => (form.value[i] = ''));
  isEdit.value = false;
};
const paramsPlaceholder = paramDisplayName => {
  return paramDisplayName === '网络地址' ? 'xx.xx.xx.xx/yy(未输入时系统自动生成)' : `请输入${paramDisplayName}`;
};
const getDisplayName = key => {
  const nameObj = {
    platform_name: '平台名称',
    comm_name: '终端名称',
    update_rate: '更新速率',
    time: '时间间隔',
  };
  return nameObj[key];
};

onMounted(async () => {
  const {data} = await getNetworkTypeApi();
  for (const item of data.list) {
    const {data: params} = await getModelDataParamApi(item.id);
    item.params = params.params;

    processData(item);
    networkTypeList.value.push(item);
  }
});

defineExpose({isEdit, networkTypeList});
</script>

<template>
  <el-dialog v-model="dialogVisible" draggable :title="`${props.networkType === 1 ? '用户' : '指挥链'}子网`" :close-on-click-modal="false" center :before-close="() => emit('update:userSubnetVisible', false)" width="516px">
    <div class="user-subnet">
      <el-form :model="form">
        <el-form-item label="显示名称">
          <el-input v-model="form.displayName" placeholder="请输入网络显示名称" :disabled="props.networkType === 2"></el-input>
        </el-form-item>
        <el-form-item label="网络名称">
          <el-input v-model="form.name" placeholder="请输入网络名称" :disabled="props.networkType === 2"></el-input>
        </el-form-item>
        <el-form-item v-show="props.networkType !== 2" label="网络类型">
          <el-select v-model="form.networkType" :disabled="props.networkType === 2" @change="setType">
            <el-option v-for="(item, index) in networkTypeList" :key="index" :label="item.displayName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- 网络类型参数渲染 -->
        <template v-for="(item, index) in form.params" :key="index">
          <!-- 成员列表参数 不在这里处理 -->
          <div v-if="item.name !== 'comm_list'" class="type-param-list">
            <span :title="item.displayName" class="param-name text-ellipsis">{{ item.displayName }}</span>
            <!-- 基本类型参数 -->
            <el-input v-if="item.defaultType === 'string' || item.defaultType === 'float'" v-model="item.value" :disabled="props.networkType === 2" :placeholder="paramsPlaceholder(item.displayName)"></el-input>
            <!-- 块级参数处理 -->
            <div v-else-if="item.defaultType === 'none' && item.isBlockParam === 2">
              <i v-if="item.name.trim() == 'comm_update_rates'" class="add-icon icon" @click="item.params.push(item.params[0])"> </i>
              <!-- 遍历 -->
              <div v-for="(ele, idx) in item.params" :key="idx" style="margin-top: 20px;">
                <span class="struct-param">{{ ele.displayName }}：</span>
                <!-- 普通参数 -->
                <el-input v-if="(ele.defaultType === 'string' || item.defaultType === 'float') && ele.name !== 'platform_name' && ele.name !== 'comm_name'" v-model="ele.value"></el-input>

                <div v-if="ele.defaultType === 'struct'">
                  <p v-for="(v, i) in ele.configureJson" :key="i" class="struct-param">
                    <span class="text-ellipsis" :title="v.name">{{ getDisplayName(v.name) }}：</span>

                    <!-- 选平台 -->
                    <el-select v-if="v.name === 'platform_name'" v-model="v.defaultValue" :disabled="props.networkType === 2" @change="selectedPlatform">
                      <el-option v-for="e in props.currentSideEntityList" v-show="!setLinkFilter().includes(e.entityName)" :key="e.id" :label="e.entityDisplayName" :value="e.entityName"></el-option>
                    </el-select>
                    <!-- 选终端 -->
                    <el-select v-if="v.name === 'comm_name'" v-model="v.defaultValue" :disabled="props.networkType === 2">
                      <el-option v-for="e in commList" :key="e.id" :label="e.displayName" :value="e.name"></el-option>
                    </el-select>

                    <el-input v-if="v.name !== 'platform_name' && v.name !== 'comm_name'" v-model="v.defaultValue"></el-input>
                  </p>
                </div>
              </div>
            </div>
            <!-- struct（json）参数处理 -->
            <div v-else-if="item.defaultType === 'struct'">
              <p v-for="(ele, idx) in item.configureJson" :key="idx" class="struct-param">
                <span class="text-ellipsis" :title="ele.name">{{ getDisplayName(ele.name) }}：</span>
                <!-- 选平台 -->
                <el-select v-if="ele.name === 'platform_name'" v-model="ele.defaultValue" :disabled="props.networkType === 2" @change="selectedPlatform">
                  <el-option v-for="e in props.currentSideEntityList" :key="e.id" :label="e.entityDisplayName" :value="e.entityName"></el-option>
                </el-select>
                <!-- 选终端 -->
                <el-select v-if="ele.name === 'comm_name'" v-model="ele.defaultValue" :disabled="props.networkType === 2">
                  <el-option v-for="e in commList" :key="e.id" :label="e.displayName" :value="e.name"></el-option>
                </el-select>

                <el-input v-if="ele.name !== 'platform_name' && ele.name !== 'comm_name'" v-model="ele.defaultValue"></el-input>
              </p>
            </div>
          </div>
        </template>
      </el-form>
      <div class="members-list">
        <div class="title">
          <span>成员列表</span>
          <span v-show="isEdit"><el-checkbox v-model="onlySelf" label="只显示本网络成员"></el-checkbox></span>
        </div>
        <!-- 树节点 -->
        <el-tree ref="treeRef" :filter-node-method="filterNode" node-key="uniqueId" show-checkbox :props="{label: 'displayName'}" :data="netData" :expand-on-click-node="true" default-expand-all> </el-tree>
      </div>
    </div>
    <template #footer>
      <el-button @click="() => emit('update:userSubnetVisible', false)">取消</el-button>
      <el-button @click="saveSubNet">保存</el-button>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.user-subnet {
  height: 390px;

  :deep(.el-form) {
    height: 160px;
    padding: 0 10px;
    overflow: auto !important;
  }

  :deep(.el-form-item__label) {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  .type-param-list {
    display: flex;
    margin: 5px 0;

    > div {
      width: 100%;
    }

    > span {
      display: inline-block;
      width: 85px;
      font-size: 15px;
      color: #fff;
    }
  }

  .struct-param {
    display: flex;
    margin: 5px 0;
    color: #fff;

    > span {
      display: inline-block;
      width: 105px;
    }
  }

  .members-list {
    position: relative;
    height: 240px;
    padding: 0 10px;
    overflow: auto !important;

    .title,
    .el-checkbox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #6ecaff;
    }

    .title::after {
      position: absolute;
      left: 80px;
      display: inline-block;
      width: 55%;
      height: 1px;
      content: ' ';
      background-image: linear-gradient(90deg, #30a8ec 0%, #135f8e00 100%);
    }
  }
}

:deep(.el-checkbox__inner) {
  background-color: transparent !important;
  border: solid 1px #6ecaff !important;
  border-radius: 2px;
}

:deep(.el-tree) {
  height: calc(100% - 80px);

  .custom-tree-node {
    display: flex;
    width: 100%;
    padding-right: 10px;
    align-items: center;
    justify-content: space-between;
  }

  .is-leaf + .el-checkbox {
    padding-left: 10px;
  }

  .el-tree-node__expand-icon {
    font-size: 18px;
    color: var(--icon-color);
  }

  /* ---- ---- ---- ---- ^（节点对齐）---- ---- ---- ---- */
  .el-tree-node {
    position: relative;
    width: auto;
    padding-left: 10px;

    &::before {
      position: absolute;
      right: auto;
      bottom: 0;
      left: 0;
      width: 1px;
      height: 100%;
      content: '';
      border-width: 1px;
      border-left: 1px solid #36b6ff;
    }

    &::after {
      position: absolute;
      top: 17px;
      right: auto;
      bottom: auto;
      left: 0;
      z-index: 0;
      width: 13px;
      height: 13px;
      content: '';
      border-width: 1px;
      border-top: 1px solid #36b6ff;
    }

    .el-tree-node__content {
      position: relative;
      z-index: 1;
      height: 30px;
      padding-left: 0 !important;
    }

    .el-tree-node__children {
      padding-left: 20px;
    }

    &:last-child::before {
      top: 0;
      height: 17px;
    }

    /* ^ 叶子节点 */
    i.el-tree-node__expand-icon.is-leaf {
      display: none;
    }
  }

  /* ^ 第一层节点 */
  > .el-tree-node {
    padding-left: 0;
    /* stylelint-disable-next-line no-descending-specificity */
    &::before {
      border-left: none;
    }

    &::after {
      border-top: none;
    }
  }

  > .el-tree-node > .el-tree-node__content {
    background-color: #145c8985;
  }
}
</style>
