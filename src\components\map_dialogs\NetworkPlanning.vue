<!--
 * @Description: 任务规范
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-11 09:18:09
 * @LastEditors: tzs
 * @LastEditTime: 2025-03-07 15:36:22
-->
<script setup>
import {reactive, computed, ref, watch, nextTick, onMounted} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {useStateStore} from '@/pinia/state/index';
import {useG6} from '@/modules/useG6';
import {getEntityCommAPI} from '@/api/index';
import UserSubnet from '@/components/map_dialogs/network_planning_dialog/UserSubnet.vue';
import TerminalDialog from '@/components/map_dialogs/network_planning_dialog/TerminalDialog.vue';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';

const {mapInstance} = storeToRefs(useMapStore());
const stateControler = useStateStore();

const emit = defineEmits(['close']);
const userSubnetRef = ref();
const termialDailogRef = ref();
const props = defineProps({
  networkPlanningVisible: {
    type: Boolean,
    default: false,
  },
});
const dialogVisible = computed(() => props.networkPlanningVisible);
const entityList = computed(() => mapInstance.value.geojson.features.filter(i => i.properties.isEntity).map(i => i.properties));
const currentSideEntityList = computed(() => entityList.value.filter(i => i.side === currentSide.value));

const comms = ref(new Map()); //通信组件的列表
const currentSide = ref('red'); //当前阵营
const networkType = ref(1); //当前展示网络类型
const networkTypeList = ref([
  {id: 1, label: '用户子网'},
  {id: 2, label: '指挥链子网'},
]);
// 网络数据列表
const netData = computed({
  get() {
    if (networkType.value === 1) {
      return userNetworkDataList.value.filter(i => i.side === currentSide.value);
    } else {
      return commandChainNetworkList.value;
    }
  },
  set(data) {
    if (networkType.value === 1) {
      console.log(data, '.....');
      userNetworkDataList.value = data;
    }
  },
});
// 用户子网数据
const userNetworkDataList = ref([]);
// 指挥链子网数据
const commandChainNetworkList = ref([]);
const G6Instance = ref();
// 选中的网络
const selectedNet = ref();
watch(
  () => dialogVisible.value,
  val => {
    if (!val) return;
    userNetworkDataList.value = [];
    console.log(stateControler.networks, '要进行回显的数据');
    stateControler.networks.forEach(item => {
      const children = [];
      const commList = item?.params?.find(i => i.name === 'comm_list')?.params ?? [];
      // 回显终端
      commList.forEach(element => {
        const valueData = JSON.parse(element.value);
        const entityName = valueData.find(i => i.name === 'platform_name').value;
        const commName = valueData.find(i => i.name === 'comm_name').value;
        const entityData = entityList.value.find(i => i.entityName === entityName);
        if (!entityData) return;
        // 依据实体和终端类型获取终端数据
        const comm = entityData.children.comms.find(i => i.name === commName);
        comm.platformName = entityName;
        children.push(comm);
      });
      // 回显网络
      userNetworkDataList.value.push({
        children: children,
        networkType: item.templateId,
        ...item,
      });
    });
    console.log(userNetworkDataList.value);

    nextTick(() => {
      G6Instance.value = useG6(container.value);
      setCoomData();
      if (userNetworkDataList.value.length > 0) showNetData({data: userNetworkDataList.value[0], level: 1});
    });
  }
);

// 设置通讯组件信息
const setCoomData = async () => {
  commandChainNetworkList.value = [];
  const ids = currentSideEntityList.value.map(i => String(i.platformTypeId));
  console.log(ids);
  const {data} = await getEntityCommAPI(ids);
  if (!data) return;

  currentSideEntityList.value.forEach(entity => {
    // 存储实体的通讯组件
    const currentComm = JSON.parse(JSON.stringify(data.find(i => i.id === entity.platformTypeId)));

    for (const i of currentComm.comms) {
      i.displayName = `${entity.displayName}-${i.displayName}`;
      i.uniqueId = entity.id + '-' + i.id;
      i.id = i.uniqueId;
    }
    entity.children = currentComm;

    comms.value.set(entity.id, currentComm);
  });
  console.log('通讯组件列表', comms.value);

  currentSideEntityList.value.forEach(item => {
    // 设置指挥链子网数据
    commandChainNetworkList.value.push({
      id: item.id,
      displayName: '指挥-' + item.entityDisplayName,
      name: 'master-' + item.name,
      children: JSON.parse(JSON.stringify(comms.value.get(item.id))).comms.map(i => {
        i.name = `${item.name}-${i.name}`;
        i.displayName = `${item.entityDisplayName}-${i.displayName}`;
        return i;
      }),
    });
  });
  console.log('commandChainNetworkList', commandChainNetworkList.value);
};

// 当前编辑网络的index
const currentEditNetIndex = ref(0);
const showNetData = node => {
  console.log('点击节点', node.data);

  const index = userNetworkDataList.value.findIndex(i => i.name === node.data.name);
  currentEditNetIndex.value = index;
  selectedNet.value = node.data;
  if (node.level !== 1) return;
  G6Instance.value.renderTree(JSON.parse(JSON.stringify(node.data)));
};
const deleteData = (node, data) => {
  console.log('删除的数据', node);
  const parent = node.parent;
  const children = parent.data.children || parent.data;
  const index = children.findIndex(i => i.id === data.id);
  children.splice(index, 1);
  netData.value = [...netData.value];
  if (node.level === 1) {
    G6Instance.value.graph.clear();
  } else {
    G6Instance.value.renderTree(JSON.parse(JSON.stringify(node.parent.data)));
  }
};
const addSubNet = data => {
  console.log('添加的用户子网数据', data);
  userNetworkDataList.value.push(data);
};
const editSubNet = data => {
  console.log('编辑的用户子网数据', data);
  userNetworkDataList.value[currentEditNetIndex.value] = data;
  G6Instance.value.graph.clear();
};
const showAddTerminal = node => {
  selectedNet.value = node.data;
  const index = userNetworkDataList.value.findIndex(i => i.name === node.data.name);
  currentEditNetIndex.value = index;

  terminalVisible.value = true;
};
const addTerminal = data => {
  userNetworkDataList.value[currentEditNetIndex.value].children.push(data);
  G6Instance.value.graph.clear();
  G6Instance.value.renderTree(JSON.parse(JSON.stringify(selectedNet.value)));
};
const editTerminal = data => {
  selectedNet.value.children[editTerminalIndex.value] = data;
  G6Instance.value.graph.clear();
  G6Instance.value.renderTree(JSON.parse(JSON.stringify(selectedNet.value)));
};

const container = ref('');
const userSubnetVisible = ref(false);
const terminalVisible = ref(false);

const editData = ref({});
const editTerminalIndex = ref(); //编辑的终端的索引
// G6节点点击事件
window.netNode = data => {
  const nodeData = {};
  data.split(',').reduce((res, item) => {
    res[item.split(':')[0]] = item.split(':')[1];
    return res;
  }, nodeData);

  console.log('点击的G6节点数据', nodeData);
  // 如果是网络则打开网络弹窗
  if (nodeData.level === '0') {
    let findData;
    if (networkType.value === 1) {
      findData = userNetworkDataList.value.find(i => i.name === nodeData.name);
    } else {
      findData = commandChainNetworkList.value.find(i => i.name === nodeData.name);
      console.log(findData);
    }

    editData.value = {
      id: nodeData.id,
      name: nodeData.name,
      networkType: nodeData.networkType,
      displayName: nodeData.displayName,
      children: findData.children,
      params: findData.params,
    };
    console.log(findData, '当前点击的子网数据');
    console.log(editData.value, '编辑的网络');

    userSubnetRef.value.isEdit = true;
    userSubnetVisible.value = true;
  } else {
    // 终端编辑
    if (networkType.value === 1) {
      editTerminalIndex.value = selectedNet.value.children.findIndex(i => i.name === nodeData.name);
      const currentTerminal = selectedNet.value.children[editTerminalIndex.value];
      console.log('用户子网-编辑的终端', currentTerminal);

      editData.value = {
        id: nodeData.id,
        name: nodeData.name,
        type: currentTerminal.templateTypeName,
        displayName: nodeData.displayName,
        platformName: currentTerminal.platformName,
        network: selectedNet.value.displayName,
      };
    } else {
      // 指挥链子网需要处理
      const entityName = nodeData.displayName.split('-')[0];
      const currentNet = commandChainNetworkList.value.find(i => i.displayName === `指挥-${entityName}`);
      const currentTerminal = currentNet.children.find(i => i.name === nodeData.name);
      editData.value = {
        id: nodeData.id,
        name: nodeData.name,
        displayName: nodeData.displayName,
        type: currentTerminal.templateTypeName,
        platformName: entityName,
        network: currentNet.displayName,
      };
      console.log(editData.value, '指挥链终端结果数据');
    }
    terminalVisible.value = true;
    termialDailogRef.value.isEdit = true;
  }
};

// 保存网络规划
const saveNetwork = () => {
  const res = [];
  userNetworkDataList.value.forEach(item => {
    const networkTypeData = userSubnetRef.value.networkTypeList.find(i => item.networkType === i.id);
    res.push({
      side: item.side,
      displayName: item.displayName,
      name: item.name,
      params: item.params,
      templateId: item.networkType,
      templateTypeId: networkTypeData.templateTypeId,
    });
  });
  stateControler.networks = res;
  console.log(res);
  emit('close');
};
defineExpose({data: []});
</script>

<template>
  <el-dialog v-model="dialogVisible" title="网络规划" :close-on-click-modal="false" destroy-on-close center :before-close="() => emit('close')" top="3%" width="1278">
    <div class="network-planning">
      <!-- 用户子网 -->
      <div class="network-list">
        <el-select v-model="networkType" @change="G6Instance.graph.clear()">
          <el-option v-for="item in networkTypeList" :key="item.id" :value="item.id" :label="item.label"></el-option>
        </el-select>
        <div class="network-box">
          <div class="title">{{ networkTypeList[networkType - 1].label }}列表</div>
          <div>
            <el-tabs v-model="currentSide" :stretch="true" type="card" @tab-change="setCoomData">
              <el-tab-pane v-for="item in stateControler.entityListData" :key="item.side" :label="item.title" :name="item.side"> </el-tab-pane>
            </el-tabs>
          </div>
          <el-tree :data="netData" :expand-on-click-node="false" default-expand-all node-key="id">
            <template #default="{node, data}">
              <span class="custom-tree-node" @click="showNetData(node)">
                <span class="text-ellipsis" :title="data.displayName">{{ data.displayName }}</span>
                <div>
                  <i v-if="node.level === 1 && networkType !== 2" class="icon add-icon" @click.stop="showAddTerminal(node)"></i>
                  <i class="icon delete-icon" @click.stop="deleteData(node, data)"></i>
                </div>
              </span>
            </template>
          </el-tree>
          <div v-show="networkType !== 2" class="add-network" @click="userSubnetVisible = true"><i class="icon add-icon"></i><span> 添加网络</span></div>
        </div>
      </div>
      <!-- 网络图表可视化 -->
      <div id="container" ref="container" class="newwork-visual"></div>
    </div>

    <!-- 用户子网 -->
    <UserSubnet
      ref="userSubnetRef"
      v-model:user-subnet-visible="userSubnetVisible"
      :network-type="networkType"
      :current-side="currentSide"
      :comms="comms"
      :edit-data="editData"
      :current-side-entity-list="currentSideEntityList"
      :user-network-data-list="userNetworkDataList"
      @add-sub-net="addSubNet"
      @edit-sub-net="editSubNet"
    />

    <!-- 终端 -->
    <TerminalDialog
      ref="termialDailogRef"
      v-model:terminal-visible="terminalVisible"
      :network-type="networkType"
      :current-side="currentSide"
      :comms="comms"
      :edit-data="editData"
      :current-side-entity-list="currentSideEntityList"
      :selected-net="selectedNet"
      :net-data="netData"
      @add-terminal="addTerminal"
      @edit-terminal="editTerminal"
    />

    <template #footer>
      <el-button @click="emit('close')">取消</el-button>
      <el-button @click="saveNetwork">保存</el-button>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.network-planning {
  display: flex;
  width: 1268px;
  height: 674px;

  .network-list {
    width: 20%;
    margin-right: 5px;
    border: var(--border);

    .el-select {
      width: 98%;
      margin: 2px;
    }

    .network-box {
      height: calc(100% - 30px);

      .title {
        width: 100%;
        height: 36px;
        padding: 0 10px;
        font-size: 16px;
        line-height: 36px;
        color: #fff;
        background: url('@/assets/images/title-bg.png');
      }

      :deep(.el-tree) {
        height: calc(100% - 110px);
        overflow: auto;

        .custom-tree-node {
          display: flex;
          width: 100%;
          padding-right: 10px;
          align-items: center;
          justify-content: space-between;
        }

        .is-leaf + .custom-tree-node span {
          width: 80%;
          padding: 0 10px;
        }

        .el-tree-node__expand-icon {
          font-size: 18px;
          color: var(--icon-color);
        }

        /* ---- ---- ---- ---- ^（节点对齐）---- ---- ---- ---- */
        .el-tree-node {
          position: relative;
          width: auto;
          padding-left: 10px;

          &::before {
            position: absolute;
            right: auto;
            bottom: 0;
            left: 0;
            width: 1px;
            height: 100%;
            content: '';
            border-width: 1px;
            border-left: 1px solid #36b6ff;
          }

          &::after {
            position: absolute;
            top: 17px;
            right: auto;
            bottom: auto;
            left: 0;
            z-index: 0;
            width: 13px;
            height: 13px;
            content: '';
            border-width: 1px;
            border-top: 1px solid #36b6ff;
          }

          .el-tree-node__content {
            position: relative;
            z-index: 1;
            height: 30px;
            padding-left: 0 !important;
          }

          .el-tree-node__children {
            padding-left: 20px;
          }

          &:last-child::before {
            top: 0;
            height: 17px;
          }

          /* ^ 叶子节点 */
          i.el-tree-node__expand-icon.is-leaf {
            display: none;
          }
        }

        /* ^ 第一层节点 */
        > .el-tree-node {
          padding-left: 0;
          /* stylelint-disable-next-line no-descending-specificity */
          &::before {
            border-left: none;
          }

          &::after {
            border-top: none;
          }
        }

        > .el-tree-node > .el-tree-node__content {
          background-color: #145c8985;
        }
      }

      .add-network {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 96%;
        height: 30px;
        margin: 5px;
        font-size: 15px;
        color: #fff;
        border: solid 1px #36b6ff;
        border-radius: 1px;
        cursor: pointer;
        background-image: linear-gradient(0deg, #2374a5 0%, #055285 100%);
      }
    }
  }

  .newwork-visual {
    width: 80%;
    border: var(--border);
  }

  :deep(.el-tabs__item) {
    height: 26px;
    font-size: 16px;
    line-height: 26px;
  }

  :deep(.el-tabs__item:first-child) {
    color: #ff9393 !important;
    background-color: #4d3545;
  }

  :deep(.is-active.el-tabs__item:first-child) {
    font-weight: bold;
    border: 1px solid #ff9393 !important;
  }

  :deep(.el-tabs__item:nth-child(2)) {
    background-color: #15628e;
  }

  :deep(.is-active.el-tabs__item:nth-child(2)) {
    font-weight: bold;
    color: #4bbfff !important;
    border: 1px solid #4bbfff !important;
  }
}
// g6节点的样式
:deep(.node-box) {
  border: solid 1px #36b6ff;
  background-image: linear-gradient(0deg, #014d80 0%, #2372a4 100%);
  border-radius: 2px;
  cursor: pointer;

  p {
    width: 100%;
    font-size: 12px !important;
    color: #72ccff;
    text-align: center;
    background: url('/images/node-bg.png') no-repeat;
    background-size: cover;
  }
}
</style>
