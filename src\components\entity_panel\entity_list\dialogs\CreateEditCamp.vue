<!--
 * @Description: 创建编辑阵营组件
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-12-27 16:06:59
 * @LastEditors: tzs
 * @LastEditTime: 2024-03-12 11:04:06
-->
<script setup>
import {ElMessage, ElMessageBox} from 'element-plus';
import {ref, computed, watch, reactive} from 'vue';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
const {mapInstance} = storeToRefs(useMapStore());
const stateControler = useStateStore();

const emit = defineEmits(['addEntityListData', 'close']);
const props = defineProps({
  createSimVisible: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});
const dialogVisible = computed(() => {
  return props.createCampVisible;
});
const state = reactive({
  tabIndex: 2, //阵营索引
  //创建阵营列表数据
  form: {
    name: '',
    color: null,
  },
});
watch(
  () => stateControler.entityListData.length,
  () => {
    state.tabIndex = Math.max(...stateControler.entityListData.map(i => i.side));
  }
);
//创建，编辑阵营
const createOrEditCamp = () => {
  if (!state.form.color) {
    return ElMessage.warning('请输入颜色');
  }
  ElMessageBox.confirm(`确定${props.isEdit ? '编辑' : '创建'}该阵营？`, '提示', {
    type: 'warning',
    closeOnClickModal: false,
  })
    .then(() => {
      // 阵营索引
      const newSide = ++state.tabIndex + '';
      //创建阵营弹框隐藏
      emit('close');
      // 创建阵营
      const bodyFormData = {
        color: state.form.color,
        name: state.form.name,
        id: state.form.id,
      };
      if (props.isEdit) {
        stateControler.entityListData.forEach(i => {
          if (Number(i.side) === Number(bodyFormData.id)) {
            i.color = bodyFormData.color;
            i.title = bodyFormData.name;
          }
        });
        updateCampColor(bodyFormData.id, bodyFormData.color);
      } else {
        //阵营绑定值
        emit('addEntityListData', {
          newSide,
          newCampData: {
            title: bodyFormData.name,
            side: newSide,
            color: bodyFormData.color,
            content: [],
          },
        });
        ElMessage.success('阵营创建成功');
      }
      state.form = {};
    })
    .catch(() => ElMessage.info('已取消'));
};
//阵营颜色更新后将geojson中的数据更新
const updateCampColor = (currentSide, color) => {
  //遍历所有实体
  mapInstance.value.geojson.features.forEach(item => {
    //找到当前阵营的id
    if (item.properties.side === currentSide) {
      //更新实体图标的颜色
      item.properties.color = color;
    }
  });
  //更新该阵营内图形的颜色
  const drawGeoJson = mapInstance.value.drawInstance.getAll();
  //修改的图形的id数组
  const ids = [];
  drawGeoJson.features.forEach(item => {
    if (item.properties.camp_id === currentSide) {
      ids.push(item.id);
      //设置阵营颜色
      item.properties.color = color;
    }
  });
  mapInstance.value.drawInstance.set(drawGeoJson);
  //绘制完之后切换为simple_select模式,并且选择feature
  mapInstance.value.drawInstance.changeMode('simple_select', {featureIds: ids});
  mapInstance.value.drawInstance.changeMode('simple_select', {featureIds: []});
  // 重新渲染地图数据
  mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
};
</script>

<template>
  <!--创建阵营-->
  <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑阵营' : '创建阵营'" width="30%" center :close-on-click-modal="false" :modal-append-to-body="false" :before-close="() => emit('close')">
    <el-form ref="form" :model="state.form" label-width="80px">
      <el-form-item label="阵营名称">
        <el-input v-model="state.form.name"></el-input>
      </el-form-item>
      <el-form-item label="阵营颜色">
        <el-color-picker v-model="state.form.color" size="small"></el-color-picker>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="emit('close')">取 消</el-button>
        <el-button type="primary" @click="createOrEditCamp">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
