<!--
 * @Description: 保存想定弹窗
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-22 17:28:01
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-09 17:10:55
-->

<script setup>
import {ElMessage, ElMessageBox} from 'element-plus';
import {inputChange, onBlur} from '@/utils/tools.js';
import {useStateStore} from '@/pinia/state/index';
import {storeToRefs} from 'pinia';
import {addFile3dForm} from '@/api/index';
import {ref, computed, watch, reactive} from 'vue';
const stateControler = useStateStore();
const emit = defineEmits(['close', 'editScenario', 'addScenario', 'saveScenarioForm']);
const props = defineProps({
  scenarioType: {
    type: String,
    default: 'eidt', //可选：save(保存想定需区分编辑后和创建后的保存)，create(创建想定)，saveAs（另存想定）
  },
  visible: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});
const dialogVisible = computed(() => props.visible);
const title = computed(() => {
  let resTitle = '';
  switch (props.scenarioType) {
    case 'create':
      resTitle = '创建想定';
      break;
    case 'save':
      resTitle = '保存想定';
      break;
    case 'saveAs':
      resTitle = '另存想定';
      break;
    case 'upLoad':
      resTitle = '上传想定';
      break;
    default:
      break;
  }
  return resTitle;
});
const elform = ref(null);
const fileList = ref([]);
const state = reactive({
  scenarioDataForm: {},
  fileName: 'Setup.txt',
  timeUnits: ['hour', 'minutes', 'seconds'],
  rules: {
    name: [{required: true, message: '请输入想定名称', trigger: 'blur'}],
    // startTime: [{required: true, message: '请输入开始时间', trigger: 'blur'}],
    endTime: [{required: true, message: '请输入结束时间', trigger: 'blur'}],
  },
});

watch(
  () => dialogVisible.value,
  val => {
    if (val) state.scenarioDataForm = JSON.parse(JSON.stringify(props.form));
  }
);

const handleFileChange = (file, fileListNew) => {
  console.log('🚀 ~ handleFileChange ~ fileListNew:', file, fileListNew);
  fileList.value = fileListNew;
};
// 保存
const saveScenario = () => {
  elform.value.validate(valid => {
    if (!valid) return ElMessage.error('请确保数据完整');
    switch (props.scenarioType) {
      case 'create':
        stateControler.scenarioEditFlag = false;
        emit('saveScenarioForm', state.scenarioDataForm);
        break;
      case 'save':
        //true修改 false新建
        if (stateControler.scenarioEditFlag) {
          // 修改想定
          ElMessageBox.confirm('确定保存想定？', {closeOnClickModal: false})
            .then(() => {
              emit('editScenario', state.scenarioDataForm);
              emit('saveScenarioForm', state.scenarioDataForm);
            })
            .catch(() => '');
        } else {
          // 新建想定
          ElMessageBox.confirm('确定创建想定？', {closeOnClickModal: false})
            .then(() => {
              emit('addScenario', state.scenarioDataForm);
              emit('saveScenarioForm', state.scenarioDataForm);
            })
            .catch(() => '');
        }
        break;
      case 'saveAs':
      case 'upLoad':
        ElMessageBox.confirm('确定上传想定？', {closeOnClickModal: false})
          .then(() => {
            const formData = new FormData();
            formData.append('name', state.scenarioDataForm.name);
            formData.append('startTime', stateControler.startTime);
            formData.append('duration', state.scenarioDataForm.endTime + state.scenarioDataForm.unit);
            formData.append('description', state.scenarioDataForm.description);
            formData.append('entryPath', state.fileName);
            formData.append('file', fileList.value[0].raw);
            addFile3dForm(formData).then(res => {
              if (res.code === 200) {
                ElMessage.success('上传成功');
                fileList.value = [];
                emit('close');
              }
            });
          })
          .catch(err => {
            console.log(err);
          });
        break;
      default:
        break;
    }
  });
};
</script>

<template>
  <el-dialog v-model="dialogVisible" :title="title" center :close-on-click-modal="false" :before-close="() => emit('close')" width="30%">
    <div v-show="props.scenarioType !== 'upLoad'" class="form-box">
      <el-form ref="elform" :model="state.scenarioDataForm" label-width="90px" :rules="state.rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="state.scenarioDataForm.name" @input="state.scenarioDataForm.name = state.scenarioDataForm.name.trim()"></el-input>
        </el-form-item>
        <el-form-item label="想定描述">
          <el-input v-model="state.scenarioDataForm.description" type="textarea" :autosize="{minRows: 2, maxRows: 4}" resize="none"></el-input>
        </el-form-item>
        <el-form-item prop="startTime" label="开始时间">
          <el-date-picker v-model="stateControler.startTime" value-format="YYYY-MM-DD HH:mm:ss" type="datetime"></el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime" label="运行时长">
          <div class="duration">
            <el-input v-model="state.scenarioDataForm.endTime" @input="state.scenarioDataForm.endTime = inputChange(state.scenarioDataForm.endTime)" @blur="state.scenarioDataForm.endTime = onBlur(state.scenarioDataForm.endTime)"></el-input>
            <el-select v-model="state.scenarioDataForm.unit" suffix-icon="CaretBottom">
              <el-option v-for="item in state.timeUnits" :key="item" :label="item" :value="item"> </el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="props.scenarioType === 'upLoad'" class="form-box1">
      <el-form ref="elform" :model="state.scenarioDataForm" label-width="90px" :rules="state.rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="state.scenarioDataForm.name" @input="state.scenarioDataForm.name = state.scenarioDataForm.name.trim()"></el-input>
        </el-form-item>
        <el-form-item label="想定描述">
          <el-input v-model="state.scenarioDataForm.description" type="textarea" :autosize="{minRows: 2, maxRows: 4}" resize="none"></el-input>
        </el-form-item>

        <el-form-item label="入口文件">
          <el-input v-model="state.fileName"></el-input>
        </el-form-item>

        <el-form-item label="想定文件">
          <el-upload :auto-upload="false" :limit="1" type="textarea" :file-list="fileList" resize="none" @change="handleFileChange"> <el-button type="primary">选择文件</el-button></el-upload>
        </el-form-item>
        <el-form-item prop="startTime" label="开始时间">
          <el-date-picker v-model="stateControler.startTime" value-format="YYYY-MM-DD HH:mm:ss" type="datetime"></el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime" label="运行时长">
          <div class="duration">
            <el-input v-model="state.scenarioDataForm.endTime" @input="state.scenarioDataForm.endTime = inputChange(state.scenarioDataForm.endTime)" @blur="state.scenarioDataForm.endTime = onBlur(state.scenarioDataForm.endTime)"></el-input>
            <el-select v-model="state.scenarioDataForm.unit" suffix-icon="CaretBottom">
              <el-option v-for="item in state.timeUnits" :key="item" :label="item" :value="item"> </el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="emit('close')">取 消</el-button>
      <el-button type="primary" @click="saveScenario">确 定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.form-box {
  height: 200px;
  margin: 20px 0 0;

  .el-form-item {
    margin-bottom: 10px;
  }

  .duration {
    display: flex;
    width: 100%;

    .el-select {
      margin-left: 5px;
    }
  }
}

.form-box1 {
  height: 290px;
  margin: 20px 0 0;

  .duration {
    display: flex;
    width: 100%;

    .el-select {
      margin-left: 5px;
    }
  }
}

:deep(.el-date-editor) {
  width: 100% !important;
}

.dialog-footer {
  position: relative;
  top: -55px;
}
</style>
