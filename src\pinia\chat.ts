/*
 * @Author: 老范
 * @Date: 2025-06-27 16:46:16
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-02 14:26:53
 * @Description: 请填写简介
 */
import {defineStore} from 'pinia';
import {ref, computed} from 'vue';
import {useStateStore} from '@/pinia/state/index';
const stateControler = useStateStore();

export const useChatStore = defineStore('chat', () => {
  const question = ref('');

  const answer = ref('');
  const isLoading = ref(false);
  const tokenCount = ref(0);
  const responseTime = ref(0);
  const apiStatus = ref('正常');
  const isDarkMode = ref(false); // 默认明亮主题
  const apiType = ref('afsim'); //想定还是任务
  const formattedResponseTime = computed(() => {
    return responseTime.value.toFixed(1) + 's';
  });

  const currentModelInfo = computed(() => {
    if (apiType.value === 'afsim') {
      return 'AFSim-Chat | 上下文: 64K tokens';
    } else {
      return 'DeepSeek-Chat | 上下文: 128K tokens';
    }
  });

  const apiEndpoint = computed(() => {
    if (apiType.value === 'scenario') {
      return `${configUrl.scenarioURL}/generate-scenario`;
    } else {
      return `${configUrl.scenarioTaskURL}/generate-scenario`;
    }
  });

  function clearChat() {
    question.value = '';
    answer.value = '';
    tokenCount.value = 0;
    responseTime.value = 0;
  }

  function toggleTheme() {
    isDarkMode.value = !isDarkMode.value;
    document.body.classList.toggle('dark-mode', isDarkMode.value);
    // 明亮主题时移除dark-mode
    if (!isDarkMode.value) {
      document.body.classList.remove('dark-mode');
    }
  }

  return {
    question,
    answer,
    isLoading,
    tokenCount,
    responseTime,
    apiStatus,
    isDarkMode,
    apiType,
    formattedResponseTime,
    currentModelInfo,
    apiEndpoint,
    clearChat,
    toggleTheme,
  };
});
