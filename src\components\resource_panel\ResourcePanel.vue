<!--
 * @Description: 实体信息面板
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-17 09:18:37
 * @LastEditors: tzs
 * @LastEditTime: 2024-08-05 10:22:07
-->
<script setup lang="ts">
import {onMounted, ref} from 'vue';
import {useStateStore} from '@/pinia/state/index';
const stateControler = useStateStore();
import {move} from '@/utils/tools';
import ModelList from '@/components/resource_panel/model_list/ModelList.vue';
import {ElMessage} from 'element-plus';
// import UnitManage from '@/components/resource_panel/unit_manage/UnitManage.vue';
// import EntityResource from '@/components/resource_panel/entity_resource/EntityResource.vue';
const activeName = ref('modelList');
const reourcePanel = ref('');
const isZN = ref(true);
const modelList = ref();
const refershData = async () => {
  await modelList.value.getPlatformTypeTree();
  ElMessage.success('刷新成功');
};
const switchName = () => {
  modelList.value!.ZN = !modelList.value!.ZN;
  isZN.value = modelList.value!.ZN;
};

onMounted(() => {
  move(reourcePanel.value);
});
</script>
<template>
  <div v-show="stateControler.controlModel" ref="reourcePanel" class="reource-panel">
    <div class="resource-title">
      资源库
      <div class="ctrl-btn">
        <el-icon title="刷新数据" class="header-icon" @click="refershData">
          <RefreshRight />
        </el-icon>
        <p class="lang" :style="{fontSize: '14px'}" @click="switchName">
          <span :style="{color: !isZN ? '#afd6ff' : '#fff'}">英</span>
          <span> / </span>
          <span :style="{color: isZN ? '#afd6ff' : '#fff'}">中</span>
        </p>
      </div>
    </div>
    <ModelList ref="modelList" />
    <!-- 以下未设计好,暂时隐藏 -->
    <el-tabs v-model="activeName" class="nav-tabs" :tab-position="'bottom'">
      <el-tab-pane label="模型资源" type="card" name="modelList">
        <!--模型列表-->
        <!-- <ModelList /> -->
      </el-tab-pane>
      <!-- 实体资源 -->
      <el-tab-pane label="实体资源" name="entityResource">
        <!-- <EntityResource /> -->
      </el-tab-pane>
      <!-- 单元资源 -->
      <el-tab-pane label="单元资源" name="unitManage">
        <!-- <UnitManage /> -->
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="less" scoped>
.reource-panel {
  position: fixed;
  top: 45px;
  left: 0;
  z-index: 1;
  width: 255px;
  height: calc(100% - 82px);
  overflow: hidden;
  color: #fff;
  background: var(--panel-bg-color);
  border: var(--border);
  box-sizing: border-box;

  :deep(.el-tabs__nav.is-bottom) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 5px;
  }

  :deep(.el-tabs__item.is-bottom) {
    width: 100%;
    height: 30px;
    padding: 0 !important;
    margin: 5px 0;
    line-height: 30px;
    text-align: center;
    background: var(--panel-bg-color);
    border-radius: 5px;
  }

  :deep(.nav-tabs .el-tabs__content) {
    height: calc(100% - 40px);
  }

  :deep(.el-tab-pane) {
    height: 100%;
  }

  :deep(.el-tabs--bottom .el-tabs__header.is-bottom) {
    margin-top: 0;
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none !important;
  }
}

.resource-title {
  display: flex;
  height: 40px;
  padding: 15px;
  font-family: Medium;
  font-size: 16px;
  line-height: 40px;
  text-align: left;
  background: url('@/assets/images/title-bg.png');
  align-items: center;
  justify-content: space-between;

  .ctrl-btn {
    display: flex;
    align-items: center;

    .lang {
      margin-left: 8px;
      cursor: pointer;
    }
  }
}

.nav-tabs {
  display: none;
  width: 100%;
  height: 86%;
}
</style>
