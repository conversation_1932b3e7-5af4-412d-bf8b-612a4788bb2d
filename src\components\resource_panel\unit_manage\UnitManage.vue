<!--
 * @Description: 单元列表组件
 * @Author: tzs
 * @Date: 2022-06-07 16:00:26
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-15 17:19:40
-->
<script setup>
import {ElMessage, ElMessageBox} from 'element-plus';
import {onMounted, ref, reactive} from 'vue';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
import UnitModelControl from '@/components/resource_panel/unit_manage/dialogs/UnitModelControl.vue';
import {addUnitApi, deleteUnitApi, editUnit, getUnitTree, deleteModelApi, addModelApi} from '@/api/index';
import {addToEntityList, getModelData} from '@/utils/data_process';

const stateControler = useStateStore();
const {MapGl, mapInstance} = storeToRefs(useMapStore());

const unitModelControl = ref(null);
const tree = ref(null);
const state = reactive({
  unitData: [], //tree单元列表数据
  defaultProps: {
    children: 'children',
    label: 'name',
  },
  dialogType: '', //弹窗的类型
  unitName: '', //当前编辑的单元名称
  unitId: '', //当前编辑的单元的id
  defaultExpandIds: [], // 这里存放要默认展开的节点 id
});
const getClassList = (node, data) => {
  return node.isLeaf && data.typeFlag ? 'collections no-children' : 'collections';
};

//新建单元
const addUnit = () => {
  //新建单元是最外层的数据
  state.unitId = 0;
  state.dialogType = '新建单元';
  unitModelControl.value.state.dialogShow = true;
};
//编辑单元
const editUnitFun = data => {
  unitModelControl.value.state.dialogShow = true;
  state.dialogType = '编辑单元';
  state.unitName = data.name;
  state.unitId = data.id;
};
//el-tree 模型/单元添加事件
const addModelOrUnit = data => {
  //当前要添加到的单元id
  state.unitId = data.id;
  state.dialogType = '新建模型';
  unitModelControl.value.state.dialogShow = true;
};
//删除单元或模型
const deleteUnitOrModel = (node, data) => {
  //如果是单元
  if (!data.typeFlag) {
    //判断是否删除数据
    ElMessageBox.confirm('确定删除该条数据吗?', '提示', {
      type: 'warning',
      closeOnClickModal: false,
    })
      .then(async () => {
        //确认删除数据
        const res = await deleteUnitApi(data.id);
        if (res) {
          ElMessage.success('单元删除成功');
          getUnitListData();
        } else {
          ElMessage.error('单元删除失败,请重试');
        }
      })
      .catch(() => ElMessage.info('已取消'));
  } else {
    //判断是否删除数据
    ElMessageBox.confirm('确定删除该条数据吗?', '提示', {
      type: 'warning',
      closeOnClickModal: false,
    })
      .then(async () => {
        //确认删除数据
        const res = await deleteModelApi(data.id);
        if (res) {
          ElMessage.success('模型删除成功');
          getUnitListData();
        } else {
          ElMessage.error('模型删除失败,请重试');
        }
      })
      .catch(() => ElMessage.info('已取消'));
  }
};

//单元的编辑/添加 弹窗中的名称
const editAddUnit = async name => {
  if (name === '') return;
  console.log(state.dialogType);
  switch (state.dialogType) {
    case '新建单元':
      {
        const res = await addUnitApi({
          displayName: name,
          parentId: state.unitId,
          name: name,
        });
        if (res) {
          ElMessage.success('单元添加成功');
          getUnitListData();
        } else {
          ElMessage.error('单元添加失败,请重试');
        }
      }
      break;
    case '编辑单元':
      {
        const result = await editUnit(state.unitId, {
          displayName: name,
          name: name,
        });
        if (result) {
          ElMessage.success('单元修改成功');
          getUnitListData();
        } else {
          ElMessage.error('单元修改失败,请重试');
        }
      }
      break;
    default:
      {
        const res = await addUnitApi({
          displayName: name,
          parentId: state.unitId,
          name: name,
        });
        if (res) {
          ElMessage.success('单元添加成功');
          getUnitListData();
        } else {
          ElMessage.error('单元添加失败,请重试');
        }
      }
      break;
  }
};
// 编辑/添加模型
const createModel = async modeData => {
  modeData.categoryId = state.unitId;
  const {status} = await addModelApi(modeData);
  if (status === 200) {
    ElMessage.success('新增模型成功');
    getUnitListData();
  }
};

// 开始拖动
const dragStart = async () => {
  //获取所有选中的模型
  const allSelectedModel = tree.value.getCheckedNodes();
  //获取该单元下的模型id
  const allSelectedModelIds = allSelectedModel.filter(i => i.typeFlag).map(i => i.platformTypeId);
  if (allSelectedModelIds.length > 0) {
    const modelList = [];
    //todo 遍历平台类型id获取平台类型数据
    for (const element of allSelectedModelIds) {
      const entityId = stateControler.currentEntityId + 1;
      const param = {
        isPlatFormType: true,
        platformTypeId: element,
        currentModelId: entityId,
        side: stateControler.currentEntityId,
        entityListData: stateControler.entityListData,
      };
      if (stateControler.scenarioId) param.scenarioId = stateControler.scenarioId;
      const entityData = await getModelData(param);
      stateControler.currentEntityId = entityId;
      modelList.push(entityData);
    }
    // 监听一次地图鼠标移动事件,触发创建实体的事件
    mapInstance.value.map.once('mouseover', () => {
      //清除高亮框
      mapInstance.value.clearHighlight();
      //遍历模型数据,添加geoJSON
      modelList.forEach(item => {
        //!添加实体到地图
        mapInstance.value.addEntity(JSON.parse(JSON.stringify(item)));
        // 添加到实体列表
        // addToEntityList(stateControler.entityListData, item.properties);
      });
      //添加单元中的实体到地图
      mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
    });
  } else {
    ElMessage.error('没有实体可进行添加');
  }
};

//获取单元列表数据
const getUnitListData = async () => {
  const {data} = await getUnitTree();
  if (data) {
    //如果没有数据的情况下,后端不返回data字段,会报错
    const datas = data ? data : [];
    // let collectionRes = getTree(data);
    // state.unitData = JSON.parse(JSON.stringify(collectionRes));
    state.unitData = datas;
  }
};
//生成tree
const getTree = list => {
  const result = [];
  getItemData(list, result);
  return result;
};
const getItemData = (data, result) => {
  data.forEach(item => {
    const itemData = {
      id: item.id,
      name: item.name,
      children: [],
    };
    if (item.modelInfos) {
      itemData.typeFlag = 1;
      item.modelInfos.forEach(element => {
        const elementData = {
          id: element.id,
          p_id: element.parentId,
          typeFlag: 2,
          name: element.name,
        };
        itemData.children.push(elementData);
      });
    }
    if (item.children) {
      itemData.name = item.name;
      itemData.typeFlag = 1;
      getItemData(item.children, itemData.children);
    } else {
      itemData.typeFlag = 1;
    }
    result.push(itemData);
  });
};
//判断实体是否被添加过
const isAddEntity = (name, id) => {
  //存储当前地图上所有实体的名称
  const allEntityNames = [];
  stateControler.entityListData.forEach(item => {
    item.content.forEach(element => {
      element.children.forEach(i => {
        allEntityNames.push(i);
      });
    });
  });
  const sameNames = allEntityNames.filter(i => i.label === name && i.id === id);
  // 判断模型名称是否重复
  if (sameNames.length >= 1) {
    return true;
  } else {
    return false;
  }
};
// 树节点展开事件
const handleNodeExpand = data => {
  // 保存当前展开的节点
  let flag = false;
  state.defaultExpandIds.some(item => {
    if (item === data.id) {
      // 判断当前节点是否存在， 存在不做处理
      flag = true;
      return true;
    }
  });
  if (!flag) {
    // 不存在则存到数组里
    state.defaultExpandIds.push(data.id);
  }
};
// 树节点关闭事件
const handleNodeCollapse = data => {
  // 删除当前关闭的节点
  state.defaultExpandIds.some((item, i) => {
    if (item === data.id) {
      state.defaultExpandIds.splice(i, 1);
    }
  });
  removeChildrenIds(data); // 这里主要针对多级树状结构，当关闭父节点时，递归删除父节点下的所有子节点
};
// 删除树子节点
const removeChildrenIds = data => {
  if (data.children) {
    data.children.forEach(item => {
      const index = state.defaultExpandIds.indexOf(item.id);
      if (index > 0) {
        state.defaultExpandIds.splice(index, 1);
      }
      state.removeChildrenIds(item);
    });
  }
};
</script>

<template>
  <div ref="unitManage" class="unit-manage">
    <div class="unit-manage-body">
      <div class="append-collection">
        <el-button type="primary" @click="addUnit"> 新建单元</el-button>
      </div>
      <!-- 单元列表 -->
      <el-tree
        ref="tree"
        draggable
        node-key="id"
        show-checkbox
        :props="state.defaultProps"
        :data="state.unitData"
        :allow-drop="() => false"
        :expand-on-click-node="true"
        :default-expanded-keys="state.defaultExpandIds"
        :allow-drag="node => node.data.typeFlag != '1'"
        @node-drag-start="dragStart"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
      >
        <template #default="{node, data}">
          <span :class="getClassList(node, data)">
            <span class="line-label text-ellipsis" :title="node.label"> {{ node.label }} </span>
            <span class="unit-manage-control">
              <span v-if="!data.typeFlag">
                <el-icon :size="16" title="添加" @click.stop="addModelOrUnit(data)">
                  <Plus />
                </el-icon>
                <el-icon :size="16" class="el-icon-edit" @click.stop="editUnitFun(data)">
                  <Edit />
                </el-icon>
              </span>
              <el-icon :size="16" class="el-icon-delete" @click.stop="deleteUnitOrModel(node, data)">
                <Delete />
              </el-icon>
            </span>
          </span>
        </template>
      </el-tree>
    </div>
    <!-- 模型和单元操作组件 -->
    <UnitModelControl ref="unitModelControl" :unit-data="state.unitData" :unit-name="state.unitName" :dialog-type="state.dialogType" @create-model="createModel" @edit-add-unit="editAddUnit" />
  </div>
</template>

<style scoped>
.unit-manage {
  height: 100%;
  min-width: 220px;
  border-radius: 5px;
  box-sizing: border-box;
}

.unit-manage-body {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
}

.append-collection {
  display: flex;
  justify-content: flex-end;
  padding: 20px 5px 0;
}

:deep(.unit-manage .collections) {
  display: flex;
  width: 86%;
  align-items: center;
  justify-content: space-between;
}

.unit-manage :deep(.no-children) {
  position: relative;
  left: -18px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.unit-manage :deep(.no-children .unit-manage-control) {
  position: relative;
  right: -16px;
}

.unit-manage :deep(.no-children .line-label) {
  flex: 1;
  padding-left: 5px;
}

.unit-manage-body :deep(.no-children .el-icon-caret-right) {
  opacity: 0.65;
}
</style>
