/*
 * @Description: 工具类文件
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-06 16:45:07
 * @LastEditors: 老范
 * @LastEditTime: 2025-03-19 16:41:39
 *
 */
import * as turf from '@turf/turf';
import {v4 as uuidv4} from 'uuid';

/**
 * 右键dom移动
 * @param {*} obj dom元素
 */
export const move = obj => {
  obj.click = e => {
    e.preventDefault();
  };
  let direction = 0;
  let leftRange;
  let rightRange;
  obj.onmousemove = e => {
    const mouseOverX = e.clientX;
    const BoxLeft = obj.offsetLeft;
    const BoxWidth = obj.offsetWidth;
    leftRange = mouseOverX - BoxLeft > -30 && mouseOverX - BoxLeft < 30;
    rightRange = mouseOverX - (BoxLeft + BoxWidth) > -30 && mouseOverX - (BoxLeft + BoxWidth) < 30;

    if (leftRange || rightRange) {
      // obj.style.cursor = "e-resize";
    } else {
      obj.style.cursor = 'default';
    }
  };

  obj.onmousedown = ev => {
    ev.stopPropagation();
    const keySide = ev.button;
    if (keySide === 0) return;
    ev = window.even || ev;
    // ev.clientX:可视窗口 鼠标到body的距离，box.offsetLeft：box的边框到body的距离
    const x = ev.clientX - obj.offsetLeft; //box到鼠标点击位置的水平距离
    const y = ev.clientY - obj.offsetTop;

    const mouseDownX = ev.clientX; //获取鼠标按下时距离浏览器左侧的距离
    const clickBoxLeft = obj.offsetLeft; //获取当前元素距离左侧的距离
    const clickBoxWeight = obj.offsetWidth; //获取当前元素的宽度

    if (mouseDownX < clickBoxLeft + 30) {
      direction = 'left';
    } else if (mouseDownX > clickBoxLeft + clickBoxWeight - 30) {
      direction = 'right';
    } else {
      direction = 0;
    }

    document.onmousemove = ev => {
      ev = window.even || ev;
      let minleft = ev.clientX - x;
      let mintop = ev.clientY - y;
      if (minleft < 0) {
        // 左边边界
        minleft = 0;
      }
      if (minleft > document.documentElement.clientWidth - obj.offsetWidth) {
        // 右边边界
        minleft = document.documentElement.clientWidth - obj.offsetWidth;
      }
      if (mintop < 0) {
        // 上边界
        mintop = 0;
      }
      if (mintop > document.documentElement.clientHeight - obj.offsetHeight) {
        // 下边界
        mintop = document.documentElement.clientHeight - obj.offsetHeight;
      }
      if (keySide === 2 && direction === 0) {
        obj.style.left = minleft + 'px';
        obj.style.top = mintop + 'px';
      } else if (keySide === 2 && direction !== 0) {
        // if (direction === "left") {
        //   obj.style.width = clickBoxWeight + mouseDownX - xx + "px";
        //   if (obj.offsetWidth <= 220) return;
        //   obj.style.left = xx + "px";
        // } else if (direction === "right" && obj.offsetWidth >= 220) {
        //   obj.style.width = clickBoxWeight + xx - mouseDownX + "px";
        // }
      }
    };
    document.onmouseup = e => {
      direction = 0;
      // 停止移动事件
      document.onmousemove = null;
      document.onmouseup = null;
      e.preventDefault();
    };
    return false;
  };
};

//验证经纬度
export const ValidationLonLat = {
  //经度
  lon: (rule, value, callback) => {
    const reg = /^(-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,25})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,15}|180)$/;
    if (!value) {
      callback(new Error('请输入经度'));
    } else {
      if (!reg.test(value)) {
        callback(new Error('经度范围：-180~180'));
      }
      callback();
    }
  },
  //纬度
  lat: (rule, value, callback) => {
    value = value.toString();
    const reg = /^(-|\+)?([0-8]?\d{1}\.\d{0,25}|90\.0{0,15}|[0-8]?\d{1}|90)$/;
    if (!reg.test(value)) {
      callback(new Error('纬度范围：-90~90'));
    } else if (!value) {
      callback(new Error('请输入纬度'));
    } else {
      callback();
    }

    // if (!value) {
    //   callback(new Error('请输入纬度'));
    // } else {
    //   if (!reg.test(value)) {
    //     callback(new Error('纬度范围：-90~90'));
    //   }
    //   callback();
    // }
  },
};
//验证规则
export const ValidationRules = {
  value: [{required: true, message: '请输入参数值', trigger: 'blur'}],
  lon: [
    {
      required: true,
      validator: ValidationLonLat.lon,
      message: '经度范围：-180~180',
      trigger: 'blur',
    },
  ],
  lat: [
    {
      required: true,
      validator: ValidationLonLat.lat,
      message: '纬度范围：-90~90',
      trigger: 'blur',
    },
  ],
};
//判断是否为JSON
export const isJsonString = str => {
  try {
    if (typeof JSON.parse(str) === 'object') {
      return true;
    }
  } catch (e) {
    return false;
  }
};
//毫秒转时分秒
export const convertMsToTime = ms => {
  const seconds = Math.floor(ms / 1000);
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const formattedHours = String(hours).padStart(2, '0');
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(remainingSeconds).padStart(2, '0');

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};
//!------------------------计算表格列宽--------------------------------
export const getStrWidth = table => {
  if (!table) return;
  let str = '';
  let nameWidth;
  const arr = [];
  table.forEach(el => {
    str = el.displayName;
    if (!document.getElementById('strWidth')) {
      document.getElementsByTagName('body')[0].insertAdjacentHTML('beforeend', '<span id="strWidth"></span>');
    }
    document.getElementById('strWidth').innerText = str;
    nameWidth = Math.ceil(document.getElementById('strWidth').getBoundingClientRect().width);
    arr.push(nameWidth);
  });
  let width = '';
  if (Math.max(...arr) > 140) {
    width = 140;
  } else {
    width = Math.max(...arr) < 120 ? 120 : Math.max(...arr);
  }
  return width;
};
//!------------------------依据经纬点获取四边形数据--------------------------------
export const getArea = position => {
  // 提取经纬度数组中的坐标
  const coordinates = position.map(point => [point.longitude, point.latitude]);
  // 使用 Turf.js 创建四边形区域
  const bboxPolygon = turf.bboxPolygon(turf.bbox(turf.multiPoint(coordinates)));
  bboxPolygon.mode = 'route-net';
  return bboxPolygon;
};
//!-----------------------均匀分布坐标点-------------------
export const uniformDistributionPoints = (areaPositionArr, pointNumber) => {
  //去重点位置(这里是矩形的四个角点坐标)
  const uniquePosition = Array.from(new Set(areaPositionArr.map(i => JSON.stringify(i)))).map(i => JSON.parse(i));
  //获取最大最小经纬度
  const maxLon = Math.max(...uniquePosition.map(i => i[0]));
  const maxLat = Math.max(...uniquePosition.map(i => i[1]));
  const minLon = Math.min(...uniquePosition.map(i => i[0]));
  const minLat = Math.min(...uniquePosition.map(i => i[1]));
  //获取几行几列
  const lineNumber = Math.ceil(Math.sqrt(pointNumber));
  //获取步长
  const lonStep = (maxLon - minLon) / lineNumber;
  const latStep = (maxLat - minLat) / lineNumber;
  const resultArr = [];
  const lonArr = [];
  const latArr = [];
  //获取经度数组和纬度数组
  for (let i = 0; i < lineNumber; i++) {
    lonArr.push(minLon + lonStep * i);
    latArr.push(minLat + latStep * i);
  }
  lonArr.forEach(lon => {
    latArr.forEach(lat => {
      resultArr.push([lon, lat]);
    });
  });
  return resultArr;
};

//!-----------------------获取geoJson中心点坐标-------------------
export const getGeoJsonCenter = data => {
  const geoJson = JSON.parse(JSON.stringify(data));
  if (geoJson.features.length === 0) return [0, 0];
  geoJson.features.forEach(i => {
    i.geometry.coordinates = i.geometry.coordinates.map(i => Number(i));
  });
  const center = turf.center(geoJson).geometry.coordinates;
  return center;
};
export const getMaxLong = data => {
  const geoJson = JSON.parse(JSON.stringify(data));
  let maxLineLong = 0;
  if (geoJson.features) {
    geoJson.features.forEach(i => {
      i.geometry.coordinates = i.geometry.coordinates.map(i => Number(i));
    });
  }
  geoJson.features.forEach(i => {
    geoJson.features.forEach(featureItem => {
      const distance = turf.distance(featureItem, i);
      if (maxLineLong < distance) {
        maxLineLong = distance;
      }
    });
  });
  return maxLineLong;
};
// 节流
export const throttle = (fn, delay) => {
  let timer = null;
  return function (...args) {
    if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, args);
        timer = null;
      }, delay);
    }
  };
};
// 转化为入库数据
export const envDataConvert = datas => {
  console.log('🚀 ~ envDataConvert ~ datas:', datas);
  const result = datas.map(item => {
    if (item.defaultType === 'struct') {
      if (typeof item.value === 'string') item.value = JSON.parse(item.value);
      item.value.forEach(ele => {
        ele.value = ele.value.toString();
      });
      return {
        templateParamId: item.templateParamId,
        unitType: item?.unitType?.name || '',
        value: JSON.stringify(item.value),
      };
    } else {
      return {
        templateParamId: item.templateParamId,
        unitType: item?.unitType || '',
        value: item.value.toString(),
      };
    }
  });
  return result;
};

// 非负数输入验证
export const inputChange = val => {
  val = val.toString();
  let num;
  num = val.replace(/[^\d.]|^\./g, '');
  num = num.replace(/^0[0-9]/g, 0);
  num = num.replace(/\.{2,}/g, '.');
  if (val.match(/\./g) && val.match(/\./g).length > 1) {
    let index = val.indexOf('.');
    num = val.slice(0, index + 1);
  }
  return num;
};
// 非负数输入验证
export const onBlur = val => {
  let num = val.replace(/\.$/g, '');
  return num;
};

// input验证负数，小数，整数
export const checkNumber = val => {
  let value = val;
  const t = value.charAt(0);
  value = value.replace(/[^\d.]/g, '');
  value = value.replace(/\.{2,}/g, '.');
  value = value.replace(/^\./g, '');
  value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
  if (t === '-') {
    value = '-' + value;
  }
  return value;
};
export const generateUuid = () => {
  return uuidv4().replace(/-/g, '');
};

// 获取未配置指挥关系的实体
export const getUnCommandEntity = (datas, allChains) => {
  const allEntity = datas.features.filter(feature => {
    return feature.properties.isEntity === true;
  });
  const inChainsEntity = [];
  let noChainEntitys = allEntity.map(item => {
    return {label: item.properties.entityDisplayName};
  });
  const entityInchainsAry = [];
  allChains.forEach(sideItem => {
    sideItem.content.forEach(chainItem => {
      entityInchainsAry.push(flatten(chainItem.children));
    });
  });
  function flatten(sourceAry) {
    return sourceAry.reduce((result, item) => {
      return result.concat(item, Array.isArray(item.children) ? flatten(item.children) : []);
    }, []);
  }
  entityInchainsAry.forEach(item => {
    if (item.length > 0) {
      item.forEach(entity => {
        inChainsEntity.push(entity.displayName);
      });
    }
  });
  noChainEntitys = noChainEntitys.filter(item => {
    if (!inChainsEntity.includes(item.label)) {
      return item;
    }
  });
  return noChainEntitys;
};

// 获取未配置网络通讯关系的实体
export const getUnNetworkEntity = (datas, allChains) => {
  const allEntity = datas.features.filter(feature => {
    return feature.properties.isEntity === true;
  });
  const inChainsEntity = [];
  let noChainEntitys = allEntity.map(item => {
    return {label: item.properties.entityDisplayName};
  });
  const entityInchainsAry = [];
  allChains.forEach(sideItem => {
    sideItem.content.forEach(chainItem => {
      entityInchainsAry.push(flatten(chainItem.children));
    });
  });
  function flatten(sourceAry) {
    return sourceAry.reduce((result, item) => {
      return result.concat(item, Array.isArray(item.children) ? flatten(item.children) : []);
    }, []);
  }
  entityInchainsAry.forEach(item => {
    if (item.length > 0) {
      item.forEach(entity => {
        inChainsEntity.push(entity.displayName);
      });
    }
  });
  noChainEntitys = noChainEntitys.filter(item => {
    if (!inChainsEntity.includes(item.label)) {
      return item;
    }
  });
  const noNetWorkEntity = [];

  // 获取没有进行网络规划的实体
  const getNoNetWorkEntity = () => {
    // todo 这里写筛选没有网络规划的实体的逻辑（暂时为空）
    noChainEntitys.forEach(item => {
      noNetWorkEntity.push(item);
    });
  };
  getNoNetWorkEntity();
  return noNetWorkEntity;
};

// 计算区域重叠
export const getOverlapArea = datas => {
  const inChainsEntity = [];
  const source = datas.features.filter(item => item.properties.zoneType === 'circular');
  source.forEach(item => {
    if (item.properties.zoneType === 'circular') {
      item.geometry = turf.sector(item.geometry.coordinates, item.properties.maximumRadius / 1000, item.properties.startAngle, item.properties.stopAngle).geometry;
    }
  });
  // --------------
  source.forEach(item => {
    source.forEach(item2 => {
      if (item2.properties.displayName === item.properties.displayName) return;
      if (turf.intersect(item, item2)) {
        inChainsEntity.push([item.properties.displayName, item2.properties.displayName]);
      }
    });
  });
  // 去重
  return Array.from(new Set(inChainsEntity.map(subArr => JSON.stringify(subArr.sort())))).map(str => JSON.parse(str));
};

// const ecefCoords 【】
export const geodeticToECEF = (lat, lon, height) => {
  const a = 6378.137; // 地球长半轴
  const e = 0.0818191908426; //地球的离心率
  const latRad = satellite.degressToRadians(lat);
  const lonRad = satellite.degressToRadians(lon);
  const N = a / Math.sqrt(1 - e * e * Math.sin(latRad) * Math.sin(latRad));
  const X = (N + height) * Math.cos(latRad) * Math.cos(lonRad);
  const Y = (N + height) * Math.cos(latRad) * Math.sin(lonRad);
  const Z = (N * (1 - e * e) + height) * Math.sin(latRad);
  return {X, Y, Z};
};
// 计算儒略日
const getJulianDate = date => {
  return date.getTime() / 86400000.0 + 244058735;
};
export const calculateGMST = date => {
  const JD = getJulianDate(date);
  const T = (JD - 2451545.0) / 36525.0;
  const GMST = 280.46061837 + 360.98564736629 * (JD - 2451545.0) + T * T * (0.000387933 - T / 387100003.0);
  return GMST % 360;
  // console.log();
};
// 传入一个时间计算gmst
const gmst = calculateGMST(new Date());

// 通过经纬高加时刻反算出eci坐标
export const ecef2ECI = (X_ecef, Y_ecef, Z_ecef, gmst) => {
  // console.log('🚀 ~ ecef2ECI ~ satellite:', satellite.degreesToRadians);
  // eslint-disable-next-line no-undef
  const gmstRad = satellite.degreesToRadians(gmst);
  const X_eci = X_ecef * Math.cos(gmstRad) - Y_ecef * Math.sin(gmstRad);
  const Y_eci = X_ecef * Math.sin(gmstRad) - Y_ecef * Math.cos(gmstRad);
  const Z_eci = Z_ecef;
  return {X_ecef, Y_ecef, Z_ecef};
};

// 校验网络地址合理性
export function validateIPv4WithSubnetMask(ipWithMask) {
  // 正则表达式匹配IPv4地址
  const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  // 正则表达式匹配子网掩码
  const maskRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  // 将输入字符串按照'/'分割为IP地址和子网掩码
  const parts = ipWithMask.split('/');

  if (parts.length !== 2) {
    return {valid: false, errorItem: '格式', errorMessage: '网络地址与子网掩码必须以"IP/MASK"的格式输入'};
  }

  const [ip, mask] = parts;
  const ipValid = ipRegex.test(ip);
  const maskValid = maskRegex.test(mask) && parseInt(mask, 10) <= 32;

  if (!ipValid) {
    return {valid: false, errorItem: 'IP地址', errorMessage: '无效的网络地址，需输入范围为0~255组成以 . 分隔的4个数字'};
  }

  if (!maskValid) {
    return {valid: false, errorItem: '子网掩码', errorMessage: '无效的子网掩码（必须是0-32之间的整数）'};
  }

  return {valid: true};
}
