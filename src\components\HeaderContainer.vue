<!--
 * @Description: 头部
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-02 14:33:20
-->
<script setup lang="ts">
import {ElMessage, ElMessageBox, ElLoading} from 'element-plus';
import {Base64} from 'js-base64';
import {createScenarioAPI, editScenarioAPI, scenarioRunInitApi, exportScenarioScriptAPI, checkWeaponAPI, localRunAPI, uploadLocalFileApi, getLocalFileApi} from '@/api/index';
import {envDataConvert, generateUuid, getGeoJsonCenter, getMaxLong, getUnCommandEntity, getUnNetworkEntity, getOverlapArea} from '@/utils/tools';
import {onMounted, reactive, nextTick, ref, Ref, watch} from 'vue';
import ScenarioControl from '@/components/map_dialogs/scenario_dialog/ScenarioControl.vue';
import ScenarioList from '@/components/map_dialogs/scenario_dialog/ScenarioList.vue';
import MissionPlanning from '@/components/map_dialogs/MissionPlanningBeta.vue';
import NetworkPlanning from '@/components/map_dialogs/NetworkPlanning.vue';
import ScenarioPreview from '@/components/map_dialogs/NewScenarioPreview.vue';
import WarnigDialog from '@/components/map_dialogs/scenario_dialog/WarnigDialog.vue';
import scriptVerify from '@/components/map_dialogs/scenario_dialog/scriptVerify.vue';
// import ScenarioPreview from '@/components/map_dialogs/ScenarioPreview.vue';
import {useMapStore} from '@/pinia/map/index';
import {useStateStore} from '@/pinia/state/index';
import {storeToRefs} from 'pinia';
import {campList, getMapData, geoJsonEntitySort, getScenarioModelParam, getScenarioRouteData, getScenarioZoneData, convertScenarioRoute, convertScenarioZone, scenarioSaveAsParam} from '@/utils/data_process.js';
import bus from '@/utils/bus';
import type {entityListDataType, scenarioFormType, missionPlanType} from '@/types/index';
import {useChatStore} from '@/pinia/chat';
const chatStore = useChatStore();
const {mapInstance} = storeToRefs(useMapStore());
// const {scenarioId, currentEntityId, entityListData, scenarioEditFlag, graphicId, commandChainData} = storeToRefs(useStateStore());
const stateControler = useStateStore();
const missionPlanning: Ref<any> = ref(null);
const warnigDialogRef = ref();
const unCommandEntitys = ref([]);
const unNetworkEntitys = ref([]);
const noPlanEntities = ref([]);
const overlapArea: Ref<any> = ref([]);
const noWeaponList: Ref<any> = ref([]);
const scriptVerifyDialogRef = ref();
const warnigDialogRefList = ref();
const autoTimer = ref();
const ifAutoSave = ref(false);

const state = reactive({
  networkPlanningVisible: false, //网络规划显隐
  missionPlanningVisible: false, //任务规划显隐
  scenarioPreviewVisible: false, //想定预演 显隐
  manageVisible: false, //想定管理按钮显隐
  scenarioType: 'create', //想定弹窗操作类型，create、save、saveAs
  scenarioFormVisible: false, //想定控制弹窗显隐
  taskListVisible: false, //仿真想定列表组件显隐
  scenarioManagerVisble: false,
  scenarioRunVisble: false,
  form: {
    name: '', //任务/想定名称
    description: '', //想定描述
    // startTime: '',
    // endTime: '',
    unit: 'hour',
  },
  data: {}, //地图数据
  // naturalEnvironmentData: [],
  naturalValue: '',
});

const to3D = async () => {
  if (!stateControler.scenarioId) return ElMessage.warning('请先打开想定');
  const param = {
    id: stateControler.scenarioId,
    mode: 0,
  };
  const loading = ElLoading.service({
    lock: true,
    text: '初始化想定...',
    background: 'rgba(0, 0, 0, 0.7)',
  });
  const res = await scenarioRunInitApi(param);
  if (res) {
    window.top?.postMessage({changePage: 'simulationDeduction'}, '*');
  }
  loading.close();
};
const localRun = async () => {
  if (!stateControler.scenarioId) return ElMessage.warning('请先打开想定');
  await localRunAPI({
    program: 'warlock',
    targetId: stateControler.scenarioId.toString(),
    targetType: 'scenario',
  });
};
//导出脚本
const exportScenario = async () => {
  if (!stateControler.scenarioId) return ElMessage.warning('请先打开想定');
  const res = await exportScenarioScriptAPI(stateControler.scenarioId);
  const blob = new Blob([res], {type: 'application/zip'});
  // eslint-disable-next-line node/no-unsupported-features/node-builtins
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.download = state.form.name + '.zip';
  a.href = url;
  a.click();
  // eslint-disable-next-line node/no-unsupported-features/node-builtins
  URL.revokeObjectURL(url);
};
const checkUnplanEntity = () => {
  // 获取到矩阵用到的所有实体
  const getAllMatrixEntity = () => {
    const allMatrixEntity = stateControler.missionPlanData
      .map(matrix => {
        if (matrix.stages.length > 0) {
          if (typeof matrix.stages[0].entityInfo !== 'string') return matrix.stages[0].entityInfo.flat(2);
          return JSON.parse(matrix.stages[0].entityInfo).flat(2);
        }
      })
      .flat(2)
      .filter(i => i);
    return allMatrixEntity;
  };
  // 检查任务重合(同一个实体是否在多个矩阵中存在)
  const allMatrixEntity = getAllMatrixEntity();
  //检查实体不在任务矩阵中（即没有进行任务规划的实体）
  const entityList = mapInstance.value.geojson.features.filter((i: {properties: {isEntity: any}}) => i.properties.isEntity).map((i: {properties: any}) => i.properties);
  const notInMatrixEntitys = entityList.filter((item: {entityId: any}) => !allMatrixEntity.find(ele => ele.entityId === item.entityId)).map((i: {entityDisplayName: any}) => i.entityDisplayName);
  return notInMatrixEntitys;
};
//脚本校验
const checkScript = async () => {
  noWeaponList.value = [];
  if (!stateControler.scenarioId) return ElMessage.warning('请先打开想定');
  // 计算未配置指挥关系的实体
  unCommandEntitys.value = getUnCommandEntity(mapInstance.value.geojson, stateControler.commandChainData);
  // 计算未配置网络通讯关系的实体
  unNetworkEntitys.value = getUnNetworkEntity(mapInstance.value.geojson, stateControler.commandChainData);
  // 计算重叠区域
  overlapArea.value = getOverlapArea(JSON.parse(JSON.stringify(mapInstance.value.graphicalGeojson)));
  // 计算未进行任务分配的实体
  noPlanEntities.value = checkUnplanEntity();
  // 计算配置了武器组件但武器数量为0或者未打开的情况
  // 调用后端接口
  // return;
  const data = await checkWeaponAPI(stateControler.scenarioId);
  console.log(data);
  if (data.data?.script.success === true) {
    ElMessage.success('校验成功');
  } else {
    ElMessage.warning(data.data?.script.errMsg);
  }
  if (data.data.weapon.turnedOffPLTFs !== null) {
    noWeaponList.value = noWeaponList.value.concat(data.data.weapon.turnedOffPLTFs);
  }
  if (data.data.weapon.withoutQuantityPLTFs !== null) {
    noWeaponList.value = noWeaponList.value.concat(data.data.weapon.withoutQuantityPLTFs);
  }
  scriptVerifyDialogRef.value?.show({title: '提示'});
};
// 清空地图数据、实体列表数据、表单数据等
const clearData = () => {
  mapInstance.value.clearMapData();
  stateControler.currentEntityId = 0;
  stateControler.entityModPar.clear();
  stateControler.localScript = '';
  state.form.name = '';
  state.form.description = '';
  // state.form.startTime = '';
  state.form.endTime = '';
  state.data = {};
  // 清空实体列表阵营数据
  const data = JSON.parse(JSON.stringify(stateControler.entityListData));

  data.forEach((ele: entityListDataType) => {
    ele.content = [];
  });
  stateControler.entityListData = data;
  // graphicId置为0
  stateControler.graphicId = 0;

  stateControler.scenarioEditFlag = false;
  stateControler.scenarioId = null;

  // 重置指挥链
  stateControler.commandChainData = [
    {
      title: '红方',
      side: 'red',
      color: '#F80404',
      content: [{name: 'default', displayName: '默认指挥链', children: [], chainIndex: 0, id: generateUuid()}],
    },
    {
      title: '蓝方',
      side: 'blue',
      color: '#00B1FF',
      content: [{name: 'default', displayName: '默认指挥链', children: [], chainIndex: 0, id: generateUuid()}],
    },
  ];
  stateControler.missionPlanData = [];
  stateControler.weatherCheckList = [];
  stateControler.routesNetworks = [];
  stateControler.terrain = [];
  stateControler.globalScript = [];
  stateControler.networks = [];
};

//保存仿真想定表单
const saveScenarioForm = (form: scenarioFormType) => {
  //清除父组件中存储的数据
  bus.emit('clearFormData', true);
  //关闭创建仿真想定弹框
  state.scenarioFormVisible = false;
  // 更改标题
  state.form.description = form.description ?? '';
  // state.form.startTime = form.startTime ?? new Date();
  state.form.endTime = form.endTime ?? '';
  state.form.unit = form.unit ?? 'hour';
  state.form.name = form.name;
};

// 想定from弹窗
const showScenarioForm = async (type: string) => {
  state.scenarioType = type;
  state.manageVisible = false;
  state.data = await getMapData(JSON.parse(JSON.stringify(mapInstance.value.geojson)));

  if (type === 'create') {
    //判断地图上是否有模型或者形状
    const isHaveModelOrShape = mapInstance.value.geojson.features.length !== 0 || mapInstance.value.drawInstance.getAll().features.length !== 0;
    // 当前添加了实体且没有保存想定，先提示是否保存
    if (isHaveModelOrShape) {
      warnigDialogRef.value?.show({title: '提示', content: '是否保存当前想定?'});
    } else {
      clearData(); // 清空所有地图相关数据
      state.scenarioFormVisible = true; //打开创建仿真想定弹框
    }
  } else {
    state.scenarioFormVisible = true;
  }
};
const openAihelper = () => {
  bus.emit('open-ai-helper');
};

const aleatSave = async () => {
  if (stateControler.scenarioEditFlag) {
    await editScenario(state.form);
  } else if (!stateControler.scenarioEditFlag && state.form.name !== '') {
    await addScenario(state.form);
  } else {
    ElMessage.warning('缺少必要参数，无法保存请直接创建');
  }
  warnigDialogRef.value.close();
  clearData(); // 清空所有地图相关数据
  state.scenarioFormVisible = true; //打开创建仿真想定弹框
};

const aleatListSave = async () => {
  if (stateControler.scenarioEditFlag) {
    await editScenario(state.form);
  } else if (!stateControler.scenarioEditFlag && state.form.name !== '') {
    await addScenario(state.form);
  } else {
    ElMessage.warning('缺少必要参数，无法保存请直接创建');
    warnigDialogRefList.value.close();
    state.scenarioFormVisible = true; //打开创建仿真想定弹框
  }
};
const aleatNotSave = () => {
  warnigDialogRef.value.close();
  clearData(); // 清空所有地图相关数据
  state.scenarioFormVisible = true; //打开创建仿真想定弹框
};
const aleatListNotSave = () => {
  warnigDialogRefList.value.close();
};

//获取添加、编辑想定的参数
const getScenarioApiParam = async (val: scenarioFormType) => {
  // bus.emit('setCommandChain');
  //获取后端需要的model的参数
  const entityData = getScenarioModelParam(state.data, stateControler.scenarioId);

  // 处理任务规划数据
  const plans = JSON.parse(JSON.stringify(stateControler.missionPlanData));
  plans.forEach((item: missionPlanType) => {
    if (!item.stages) return;
    item.stages.forEach((v, i) => {
      v.sort = i + 1;
      if (typeof v.entityInfo !== 'string') v.entityInfo = JSON.stringify(v.entityInfo);
      if (typeof v.eventParamValue !== 'string') v.eventParamValue = JSON.stringify(v.eventParamValue);
    });
  });
  const maxDistance = getMaxLong(mapInstance.value.geojson);
  const center = getGeoJsonCenter(mapInstance.value.geojson);
  const cameraConfig = {
    maxDistance,
    center,
  };
  const environments = envDataConvert(stateControler.weatherCheckList);
  const zones = await getScenarioZoneData(mapInstance.value.graphicalGeojson, mapInstance.value.geojson);

  const mapInfo = geoJsonEntitySort(JSON.parse(JSON.stringify(mapInstance.value.geojson)));

  let customScriptFileId;
  //-------本地脚本------
  if (stateControler.localScript) {
    const fileName = 'data.txt';
    const file = new File([stateControler.localScript], fileName, {type: 'text/plain'});
    const data = new FormData();
    data.append('name', fileName);
    data.append('file', file);
    const res = await uploadLocalFileApi(data);
    if (res.data) customScriptFileId = res.data;
  }

  const param = {
    routes: getScenarioRouteData(mapInstance.value.graphicalGeojson),
    zones: zones,
    cmdChain: JSON.stringify(stateControler.commandChainData),
    mapInfo: JSON.stringify(mapInfo),
    models: entityData,
    ...val,
    cameraConfig,
    startTime: stateControler.startTime,
    globalScripts: stateControler.globalScript,
    plans,
    environments: environments,
    routesNetworks: stateControler.routesNetworks,
    terrain: stateControler.terrain[0],
    networks: stateControler.networks,
  };
  console.log('🚀 ~ getScenarioApiParam ~ param.val:', param.val);
  // 更改时间格式
  param.endTime = param.endTime + ' ' + param.unit;

  customScriptFileId ? (param.customScriptFileId = customScriptFileId) : '';
  return param;
};

// 修改想定请求
const editScenario = async (val: scenarioFormType) => {
  console.log('🚀 ~ editScenario ~ val:', val);
  //获取后端需要的参数
  const param = await getScenarioApiParam(val);
  console.log('🚀 ~ editScenario ~ val:', val);
  const {msg} = await editScenarioAPI(stateControler.scenarioId!, param);
  if (msg === '修改成功') {
    ElMessage.success({duration: 1000, offset: 50, message: '保存成功'});
    //关闭保存想定组件
    state.scenarioFormVisible = false;
  }
};
//创建想定请求
const addScenario = async (val: scenarioFormType) => {
  console.log('🚀 ~ addScenario ~ val:', val);
  console.log('🚀 ~ addScenario ~ val:', val);
  //获取后端需要的参数
  let param = await getScenarioApiParam(val);
  // 如果是另存的话处理参数
  if (state.scenarioType === 'saveAs') {
    param = scenarioSaveAsParam(param);
  }
  const {data} = await createScenarioAPI(param);
  if (data) {
    stateControler.scenarioId = data;
    state.scenarioFormVisible = false;
    stateControler.scenarioEditFlag = true;
    state.form = val;
    ElMessage.success('创建成功');
  }
};
//想定列表
const openScenarioList = () => {
  state.manageVisible = false; //关闭想定管理按钮组件
  state.taskListVisible = true; // 打开仿真想定列表弹框
  // return;
  // const isHaveModelOrShape = mapInstance.value.geojson.features.length !== 0 || mapInstance.value.drawInstance.getAll().features.length !== 0;
  // // 当前添加了实体且没有保存想定，先提示是否保存
  // if (isHaveModelOrShape) {
  //   warnigDialogRefList.value?.show({title: '提示', content: '是否保存当前想定?'});
  // } else {
  // }
};
// 打开想定事件
const openScenario = async (scenarioData: scenarioFormType) => {
  //清空地图数据
  mapInstance.value.clearMapData();
  // 关闭仿真想定列表
  state.taskListVisible = false;
  state.form.name = scenarioData.name;
  state.form.description = scenarioData.description;
  // state.form.startTime = scenarioData.startTime;
  state.form.endTime = scenarioData.endTime.split(' ')[0];
  state.form.unit = scenarioData.endTime.split(' ')[1];
  // endTime.replace(/\d/g, "")
  // 保存想定列表数据处理
  stateControler.scenarioEditFlag = true;

  state.data = JSON.parse(JSON.stringify(scenarioData.geoJson));
  stateControler.scenarioId = scenarioData.scenarioId!;
  //清除多余阵营(只保留红蓝阵营)
  const relevantEntities = stateControler.entityListData.filter(i => ['red', 'blue'].includes(i.side));
  relevantEntities.forEach(item => (item.content = []));
  stateControler.entityListData = relevantEntities;
  //网络规划
  stateControler.networks = scenarioData.networks!;
  stateControler.graphicId =
    scenarioData.routes?.reduce((result, item: {name: String}) => {
      if (result < Number(item.name.split('-')[1])) {
        return Number(item.name.split('-')[1]);
      } else return result;
    }, 0) || 0;
  //全局脚本回显
  if (scenarioData.globalScripts && scenarioData.globalScripts.length > 0) stateControler.globalScript = scenarioData.globalScripts!;
  // 本地脚本回显
  if (scenarioData.customScriptFileId) {
    const {data} = await getLocalFileApi(scenarioData.customScriptFileId);
    stateControler.localScript = Base64.decode(data.data);
  }

  bus.emit('reSetPreview');

  nextTick(async () => {
    //! 处理实体显示
    scenarioData.geoJson!.features.forEach(ele => {
      // 更新实体的ID
      if (ele.properties.entityId! > stateControler.currentEntityId!) {
        stateControler.currentEntityId = ele.properties.entityId!;
      }
      // 判断是实体
      if (ele.properties.isEntity || ele.isEntity) {
        const entityData = ele.properties;
        entityData.isEntity = true; //兼容旧想定

        //实体列表数据
        stateControler.entityListData.forEach(element => {
          if (element.side === entityData.side) {
            element.content = campList(element.content, entityData);
          }
        });
      }
    });

    // 过滤实体数据
    scenarioData.geoJson!.features = scenarioData.geoJson!.features.filter(el => {
      return el.id === 'entity' || el.properties.sourceEntityId!;
    });
    //! 将过滤后的数据添加到places源 (添加地图数据)
    mapInstance.value.map.getSource('places').setData(scenarioData.geoJson);
    mapInstance.value.geojson = JSON.parse(JSON.stringify(scenarioData.geoJson));
    //!处理航路显示
    if (scenarioData.routes) {
      const routes = convertScenarioRoute(scenarioData.routes);
      routes.forEach(item => {
        const {color: color} = stateControler.entityListData.find(entity => entity.side === item.properties.side) || {};
        item.properties.color = color;

        // 将数据添加到draw控件(也就是通过draw控件去显示点线,多边形等数据)
        mapInstance.value.addGraphical(item);
      });
    }
    //!处理zone显示
    if (scenarioData.zones) {
      const zones = await convertScenarioZone(scenarioData.zones);
      zones.forEach(item => {
        mapInstance.value.addGraphical(item);
      });
    }

    if (mapInstance.value.geojson.features.length === 0) return;
    const center = getGeoJsonCenter(mapInstance.value.geojson);
    mapInstance.value.map.flyTo({
      center,
      essential: true,
      speed: 0.9,
      zoom: 2.5,
    });
  });
};
const scenarioPreview = async () => {
  if (!stateControler.scenarioId) return ElMessage.warning('请先打开想定');
  state.scenarioPreviewVisible = !state.scenarioPreviewVisible;
};

document.addEventListener('keydown', e => {
  const ctrlKey = e.ctrlKey || e.metaKey;
  if (ctrlKey && e.key === 'L') {
    openScenarioList();
  }
  if (ctrlKey && e.key === 'S') {
    showScenarioForm('save');
  }
  if (ctrlKey && !mapInstance.value.ctrlKey) {
    // mapInstance.value.ctrlKey = ctrlKey;
    mapInstance.value.setCtrlKey(ctrlKey);
  }
});
document.addEventListener('keyup', e => {
  const ctrlKey = e.ctrlKey || e.metaKey;
  if (!ctrlKey && mapInstance.value.ctrlKey) {
    // mapInstance.value.ctrlKey = ctrlKey;
    mapInstance.value.setCtrlKey(ctrlKey);
  }
});
watch(
  () => state.missionPlanningVisible,
  val => {
    console.log('🚀 ~ watch ~ val:', val);
    if (val) {
      chatStore.apiType = 'task';
    } else {
      chatStore.apiType = 'scenario';
    }
  }
);
const autoSaveScenario = val => {
  if (val) {
    autoTimer.value = setInterval(() => {
      editScenario(state.form);
      // }, 1000);
    }, 30000);
  } else {
    clearInterval(autoTimer.value);
    autoTimer.value = null;
  }
};
// onMounted(() => (state.networkPlanningVisible = true));
</script>
<template>
  <div class="header">
    <div class="left-container">
      <div class="navigation-bar">
        <div class="manager-box" @click="state.scenarioManagerVisble = !state.scenarioManagerVisble">
          <i class="icon"></i>
          <span>想定管理</span>
          <el-icon><CaretBottom /></el-icon>
        </div>
        <div v-show="stateControler.scenarioId" class="btn-list">
          <div @click="state.missionPlanningVisible = !state.missionPlanningVisible"><i class="icon mission"></i> <span>任务规划</span></div>
          <!-- <div @click="state.networkPlanningVisible = !state.networkPlanningVisible"><i class="icon network-icon"></i> <span>网络规划</span></div> -->
          <div v-show="stateControler.scenarioId" @click="scenarioPreview"><i class="icon preview"></i> <span>想定预演</span></div>
          <div v-show="stateControler.scenarioId" @click="exportScenario"><i class="icon export"></i> <span>导出想定</span></div>
          <div v-show="stateControler.scenarioId" @click="state.scenarioRunVisble = !state.scenarioRunVisble"><i class="icon run"></i> <span>运行</span></div>
          <div v-show="stateControler.scenarioId" @click="checkScript"><i class="icon script"></i> <span>脚本校验</span></div>
        </div>
        <div v-show="state.scenarioRunVisble" class="scenario-run glass" @mouseleave="state.scenarioRunVisble = false">
          <p @click="to3D">场景推演</p>
          <p @click="localRun">仿真调试</p>
        </div>
        <div v-show="state.scenarioManagerVisble" class="scenario-manager glass" @mouseleave="state.scenarioManagerVisble = false">
          <p @click="openScenarioList">想定列表</p>
          <p @click="showScenarioForm('create')">创建想定</p>
          <p @click="showScenarioForm('upLoad')">上传想定</p>
          <p @click="showScenarioForm('save')">保存想定</p>
          <p @click="openAihelper">想定助手</p>
          <!-- <p v-show="stateControler.scenarioEditFlag" @click="showScenarioForm('saveAs')">另存想定</p> -->
        </div>
      </div>
      <div v-if="state.data" class="scenario-name">
        <span v-if="state.form.name">
          <b>当前想定：</b><span class="scName" :title="state.form.name">{{ state.form.name }}</span> <b class="desc">描述信息：</b><span class="descContent" :title="state.form.description">{{ state.form.description }}</span>
        </span>
        <span class="fightTime"><b>作战时间：</b> <el-date-picker v-model="stateControler.startTime" value-format="YYYY-MM-DD HH:mm:ss" type="datetime"></el-date-picker></span>
        <!-- <span v-if="state.form.name" class="autoSaveClass">
          <b>自动保存：</b>
          <el-switch v-model="ifAutoSave" size="large" active-text="开启" inactive-text="关闭" @change="autoSaveScenario" />
        </span> -->
      </div>
    </div>
  </div>
  <!-- 想定操作弹窗 -->
  <ScenarioControl :visible="state.scenarioFormVisible" :scenario-type="state.scenarioType" :form="state.form" @save-scenario-form="saveScenarioForm" @add-scenario="addScenario" @edit-scenario="editScenario" @close="state.scenarioFormVisible = false" />
  <!--想定列表-->
  <ScenarioList :task-list-visible="state.taskListVisible" @clear-data="clearData" @open-scenario="openScenario" @close="state.taskListVisible = false" />
  <!-- 网络规划 -->
  <NetworkPlanning ref="networkPlanning" :network-planning-visible="state.networkPlanningVisible" @close="state.networkPlanningVisible = false" />
  <!-- 任务规划 -->
  <MissionPlanning ref="missionPlanning" :mission-planning-visible="state.missionPlanningVisible" @close="state.missionPlanningVisible = false" />
  <!-- 想定预演 -->
  <ScenarioPreview :scenario-id="stateControler.scenarioId" :scenario-preview-visible="state.scenarioPreviewVisible" @close="state.scenarioPreviewVisible = false" />
  <scriptVerify ref="scriptVerifyDialogRef" :un-command-entity-list="unCommandEntitys" :un-network-entity-list="unNetworkEntitys" :no-plan-entities="noPlanEntities" :overlap-area="overlapArea" :no-weapon-list="noWeaponList"></scriptVerify>
  <WarnigDialog ref="warnigDialogRef" @save="aleatSave" @not-save="aleatNotSave" />
  <WarnigDialog ref="warnigDialogRefList" @save="aleatListSave" @not-save="aleatListNotSave" />
</template>
<style lang="less" scoped>
.header {
  display: flex;
  width: 100%;
  height: 40px;
  padding: 0 10px;
  background: url('@/assets/images/instance_edit_header.png') no-repeat 0 0 / 100% 100%;
  align-items: center;
  justify-content: space-between;

  .left-container {
    display: flex;
    align-items: center;

    .navigation-bar {
      display: flex;
      height: 50px;
      align-items: center;

      .manager-box {
        display: flex;
        width: 150px;
        height: 23px;
        margin-right: 20px;
        font-size: 16px;
        align-items: center;
        justify-content: space-around;
        border-right: 2px solid var(--split-line-color);
        cursor: pointer;

        .icon {
          display: inline-block;
          width: 20px;
          height: 20px;
          background: url('@/assets/images/mission.png') no-repeat;
          background-size: contain;
        }

        .el-icon {
          margin-right: 20px;
          color: var(--icon-color);
        }
      }

      .btn-list {
        position: fixed;
        top: 55px;
        left: 277px;
        z-index: 9;
        display: flex;
        font-size: 15px;

        > div {
          display: flex;
          margin: 0 5px;
          line-height: 24px;
          cursor: pointer;
          align-items: center;
        }

        .icon {
          display: inline-block;
          width: 18px;
          height: 18px;
          background-size: cover !important;
          margin: 0 5px;
        }

        .mission {
          background: url('@/assets/images/mission.png') no-repeat;
        }

        .preview {
          background: url('@/assets/images/preview.png') no-repeat;
        }

        .export {
          background: url('@/assets/images/export.png') no-repeat;
        }

        .run {
          background: url('@/assets/images/run.png') no-repeat;
        }

        .script {
          background: url('@/assets/images/run.png') no-repeat;
        }
      }
    }
  }

  .scenario-manager {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 6;
    padding: 10px;
  }

  .scenario-run {
    position: absolute;
    top: 80px;
    left: 580px;
    z-index: 6;
    padding: 10px;
  }

  .scenario-manager p,
  .scenario-run p {
    width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
  }

  .scenario-manager p:hover,
  .scenario-run p:hover {
    color: #409eff;
    cursor: pointer;
  }

  .scenario-name {
    padding: 0 5px;
    font-size: 16px;
    font-weight: bold;

    b {
      color: var(--font-color);
    }

    .scName {
      display: inline-block;
      text-wrap: nowrap;
      max-width: 200px;
      overflow: hidden;
      vertical-align: middle;
    }

    .desc {
      margin-left: 20px;
    }

    .descContent {
      display: inline-block;
      text-wrap: nowrap;
      max-width: 200px;
      overflow: hidden;
      vertical-align: middle;
    }

    .autoSaveClass {
      margin-left: 20px;

      b {
        display: inline-block;
        margin-top: 7px;
      }
    }
  }

  .fightTime {
    margin-left: 30px;
    // position: absolute;
    // top: 5px;
    // left: 900px;
  }

  :deep(.el-button) {
    width: 100px;
  }

  :deep(.el-button :hover) {
    color: #339af0;
  }
}
</style>
