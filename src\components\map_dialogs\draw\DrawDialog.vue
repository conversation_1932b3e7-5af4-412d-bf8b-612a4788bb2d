<!--
 * @Description: 区域绘制组件
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-12-21 09:39:14
 * @LastEditors: 老范
 * @LastEditTime: 2025-03-21 17:04:27
-->
<script setup>
import {ElMessage} from 'element-plus';
import {watch, computed, ref, reactive, nextTick} from 'vue';
import {unitConvertApi} from '@/api/index';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
import {v4 as uuidv4} from 'uuid';

const stateControler = useStateStore();
const {mapInstance, MapGl} = storeToRefs(useMapStore());
const pointsTable = ref('');
const form = reactive({
  displayName: '',
  name: '',
  side: 'red',
  zoneType: 'circular',
  referencePlatform: '',
  latitude: 34,
  longitude: 120,
  minimumAltitude: 0,
  minimumAltitudeUnit: '',
  maximumAltitude: 1000,
  maximumAltitudeUnit: '',
  minimumRadius: 0,
  minimumRadiusUnit: '',
  maximumRadius: 1000,
  maximumRadiusUnit: '',
  startAngle: 0,
  startAngleUnit: '',
  stopAngle: 360,
  stopAngleUnit: '',
  heading: 0,
  headingUnit: '',
  color: '#ff0000',
});
const types = ref([
  {id: 'circular', name: '圆'},
  {id: 'polygonal', name: '多边形'},
  // {id: 'elliptical', name: '椭圆'},
]);
const entityList = computed(() => mapInstance.value.geojson.features.filter(i => i.properties.isEntity).map(i => i.properties));
const unitList = computed(() => id => stateControler.unitConfig.get(id)?.params);

watch(
  () => stateControler.drawDialogVisible,
  () => setDefaultUnit()
);
// 表格滚动
const scroll = () => {
  nextTick(() => {
    const box = pointsTable.value.$refs.bodyWrapper.getElementsByClassName('el-scrollbar__wrap')[0];
    box.scrollTop = box.scrollHeight;
  });
};

const setDefaultUnit = () => {
  if (isEdit.value) return;
  Object.keys(form).forEach(item => {
    if ((item.includes('Unit') && item.includes('Angle')) || item === 'headingUnit') {
      form[item] = stateControler.unitConfig.get(18).params.find(i => i.default).name;
    } else if (item.includes('Unit') && !item.includes('Angle')) {
      form[item] = stateControler.unitConfig.get(11).params.find(i => i.default).name;
    }
  });
};

const useZone = () => {
  const isSelectPoint = ref(false);
  const points = ref([]);
  const isEdit = ref(false);

  const selectPoint = e => {
    const longitude = e.lngLat.lng.toFixed(6);
    const latitude = e.lngLat.lat.toFixed(6);
    if (form.zoneType !== 'circular') {
      const lastLon = points.value[points.value.length - 1]?.longitude;
      const lastLat = points.value[points.value.length - 1]?.latitude;
      if (lastLon == longitude && lastLat == latitude) return;
      points.value.push({longitude, latitude});
      scroll();
    } else {
      form.latitude = latitude;
      form.longitude = longitude;
    }
  };
  watch(
    () => isSelectPoint.value,
    val => {
      if (val) {
        mapInstance.value.map.on('click', selectPoint);
      } else {
        mapInstance.value.map.off('click', selectPoint);
      }
    }
  );

  watch(
    () => stateControler.currentZoneData.id,
    async val => {
      if (!val) return;

      isEdit.value = true;
      const data = stateControler.currentZoneData.properties;
      Object.keys(data).forEach(item => {
        form[item] = data[item];
      });

      if (data.zoneType === 'polygonal') {
        const lonlatArr = stateControler.currentZoneData.geometry.coordinates[0];
        points.value = lonlatArr.slice(0, -1).map(i => ({longitude: i[0], latitude: i[1]}));
      } else if (data.zoneType === 'circular') {
        const res = await unitConvertApi('m', data.maximumRadiusUnit, Number(form.maximumRadius));
        form.maximumRadius = res.data;
        // 回显角度要见回去加的角度
        form.heading = stateControler.currentZoneData.properties.heading - stateControler.currentZoneData.properties.angle / 2 - stateControler.currentZoneData.properties.startAngle;
        // 区域绑定实体处理

        if (form.referencePlatform) {
          console.log(mapInstance.value.geojson.features);
          const isHave = mapInstance.value.geojson.features.find(i => i.properties.id === form.referencePlatform);
          if (!isHave) {
            form.referencePlatform = '';
            form.longitude = stateControler.currentZoneData.position[0].toFixed(6);
            form.latitude = stateControler.currentZoneData.position[1].toFixed(6);
          }
        }
      }

      form.color = data.color;
      // console.log(stateControler.currentZoneData, '编辑的zone数据');
    },
    {deep: true}
  );

  const addZone = async () => {
    if (form.minimumAltitude > form.maximumAltitude) return ElMessage.warning('最小高度不能大于最大高度');

    const zoneGeoJson = {
      type: 'Feature',
      properties: {
        displayName: form.displayName, //模型显示名称(中文)
        name: form.name, //模型名称
        side: form.side, //实体所属阵营
        zoneType: form.zoneType,
        minimumAltitude: Number(form.minimumAltitude),
        maximumAltitude: Number(form.maximumAltitude),
        minimumAltitudeUnit: form.minimumAltitudeUnit,
        maximumAltitudeUnit: form.maximumAltitudeUnit,
        color: form.color,
        heading: Number(form.heading),
        headingUnit: form.headingUnit,
      },
      geometry: {
        type: 'Polygon',
        coordinates: [[]],
      },
      isEntity: false,
    };
    // 多边形添加、编辑
    if (form.zoneType === 'polygonal') {
      if (points.value.length < 3) return ElMessage.warning(`多边形最少有三个点构成`);

      points.value.forEach(item => {
        zoneGeoJson.geometry.coordinates[0].push([Number(item.longitude), Number(item.latitude)]);
      });
      zoneGeoJson.geometry.coordinates[0].push(zoneGeoJson.geometry.coordinates[0][0]);

      for (const item of Object.keys(zoneGeoJson.properties)) {
        if (zoneGeoJson.properties[item] === '') return ElMessage.warning(`${item}不能为空`);
      }
      if (isEdit.value) {
        zoneGeoJson.id = stateControler.currentZoneData.id;
        console.log('编辑的图形', zoneGeoJson);
        mapInstance.value.getDrawData(zoneGeoJson);
        mapInstance.value.drawInstance.changeMode('simple_select', {featureIds: [zoneGeoJson.id]});
        mapInstance.value.drawInstance.changeMode('simple_select', {featureIds: []});
      } else {
        mapInstance.value.addGraphical(zoneGeoJson);
      }
    } else {
      // 圆添加、编辑
      zoneGeoJson.geometry.type = 'Point';
      if (form.referencePlatform) {
        const entity = entityList.value.find(i => i.id === form.referencePlatform);
        zoneGeoJson.geometry.coordinates = [Number(entity.longitude), Number(entity.latitude)];
        zoneGeoJson.properties.referencePlatform = form.referencePlatform;
      } else {
        zoneGeoJson.geometry.coordinates = [Number(form.longitude), Number(form.latitude)];
        if (form.longitude > 180) {
          form.longitude -= 180;
        } else if (form.longitude < -180) {
          form.longitude += 180;
        }
        zoneGeoJson.properties.longitude = Number(Number(form.longitude).toFixed(6));
        zoneGeoJson.properties.latitude = form.latitude;

        // if (Math.abs(form.longitude) > 180) return ElMessage.warning('请输入有效经度');
        if (Math.abs(form.latitude) > 90) return ElMessage.warning('请输入有效纬度');
      }

      zoneGeoJson.properties.minimumRadius = Number(form.minimumRadius);
      zoneGeoJson.properties.minimumRadiusUnit = form.minimumRadiusUnit;
      const res = await unitConvertApi(form.maximumRadiusUnit, 'm', Number(form.maximumRadius));
      zoneGeoJson.properties.maximumRadius = res.data;
      zoneGeoJson.properties.maximumRadiusUnit = form.maximumRadiusUnit;
      zoneGeoJson.properties.startAngle = Number(form.startAngle);
      zoneGeoJson.properties.startAngleUnit = form.startAngleUnit;
      zoneGeoJson.properties.stopAngle = Number(form.stopAngle);
      zoneGeoJson.properties.stopAngleUnit = form.stopAngleUnit;

      if (zoneGeoJson.properties.minimumRadius > zoneGeoJson.properties.maximumRadius) return ElMessage.warning('最小半径不能大于最大半径');
      if (zoneGeoJson.properties.startAngle > zoneGeoJson.properties.stopAngle) return ElMessage.warning('起始角度不能大于终止角度');

      zoneGeoJson.properties.angle = Math.abs(form.stopAngle - form.startAngle);
      // 地图角度对应不上需要加上夹角的一半在加上初始角度
      zoneGeoJson.properties.heading = zoneGeoJson.properties.heading + zoneGeoJson.properties.angle / 2 + zoneGeoJson.properties.startAngle;

      for (const item of Object.keys(zoneGeoJson.properties)) {
        if (zoneGeoJson.properties[item] === '') return ElMessage.warning(`${item}不能为空`);
      }
      // 如果是编辑就删除掉原来的数据
      if (isEdit.value) {
        mapInstance.value.geojson.features = mapInstance.value.geojson.features.filter(i => i.id !== stateControler.currentZoneData.id);
        mapInstance.value.graphicalGeojson.features = mapInstance.value.graphicalGeojson.features.filter(i => i.id !== stateControler.currentZoneData.id);
        zoneGeoJson.id = stateControler.currentZoneData.id;
      } else {
        zoneGeoJson.id = uuidv4().replace(/-/g, '');
      }
      zoneGeoJson.properties.id = zoneGeoJson.id; //property存id用于点击时获取（因地图点击无法拿到外层id）

      mapInstance.value.geojson.features.push(JSON.parse(JSON.stringify(zoneGeoJson))); //添加到geojson中在地图显示
      mapInstance.value.graphicalGeojson.features.push(zoneGeoJson); //添加到图形数据中（管理）
      mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
    }
    close();
  };

  return {isSelectPoint, points, addZone, isEdit};
};

const {isSelectPoint, points, addZone, isEdit} = useZone();
const close = () => {
  stateControler.drawDialogVisible = false;
  isEdit.value = false;
  Object.keys(form).forEach(item => {
    if (!['color', 'side', 'zoneType'].includes(item)) form[item] = '';
    // form
    Object.assign(form, {
      latitude: 34,
      longitude: 120,
      minimumAltitude: 0,
      minimumAltitudeUnit: '',
      maximumAltitude: 1000,
      maximumAltitudeUnit: '',
      minimumRadius: 0,
      minimumRadiusUnit: '',
      maximumRadius: 1000,
      maximumRadiusUnit: '',
      startAngle: 0,
      startAngleUnit: '',
      stopAngle: 360,
      stopAngleUnit: '',
      heading: 0,
      headingUnit: '',
    });
  });
  points.value = [];
  stateControler.currentZoneData = {};
  isSelectPoint.value = false;
};
document.body.oncontextmenu = () => (isSelectPoint.value = false);

defineExpose({isSelectPoint});
</script>

<template>
  <div v-show="stateControler.drawDialogVisible" v-draggable class="drawDialog">
    <div class="header">区域数据</div>
    <div>
      <el-form ref="formRef" :model="form" label-width="80px">
        <el-form-item label="显示名称">
          <el-input v-model="form.displayName"></el-input>
        </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="阵营">
          <el-select v-model="form.side" class="side-select" placeholder="请选择" suffix-icon="CaretBottom">
            <el-option v-for="item in stateControler.entityListData" :key="item.side" :label="item.title" :value="item.side"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="form.zoneType" class="side-select" :disabled="isEdit" placeholder="请选择" suffix-icon="CaretBottom">
            <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.zoneType !== 'polygonal'" label="实体">
          <el-select v-model="form.referencePlatform" clearable class="side-select" placeholder="无" suffix-icon="CaretBottom">
            <el-option v-for="item in entityList" :key="item.id" :label="item.entityDisplayName" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <div v-if="!form.referencePlatform && form.zoneType !== 'polygonal'" class="dis-flex align-items">
          <div class="flex-box">
            <el-form-item label="纬度">
              <el-input v-model="form.latitude" :step="0.000001" type="number"></el-input>
            </el-form-item>
            <el-form-item label="经度">
              <el-input v-model="form.longitude" :step="0.000001" type="number"></el-input>
            </el-form-item>
          </div>
          <div :class="[isSelectPoint ? 'selecting' : '', 'select-position']" @click="isSelectPoint = !isSelectPoint">
            <el-icon class="position-icon" title="地图选点"> <Location /> </el-icon>
            <p>地图选点</p>
          </div>
        </div>
        <el-form-item label="最小高度">
          <div class="value-unit-box">
            <el-input v-model="form.minimumAltitude" type="number"></el-input>
            <el-select v-model="form.minimumAltitudeUnit" size="small">
              <el-option v-for="(e, ix) in unitList(11)" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="最大高度">
          <div class="value-unit-box">
            <el-input v-model="form.maximumAltitude" type="number"></el-input>
            <el-select v-model="form.maximumAltitudeUnit" size="small">
              <el-option v-for="(e, ix) in unitList(11)" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item v-if="form.zoneType === 'circular'" label="最小半径">
          <div class="value-unit-box">
            <el-input v-model="form.minimumRadius" type="number"></el-input>
            <el-select v-model="form.minimumRadiusUnit" size="small">
              <el-option v-for="(e, ix) in unitList(11)" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item v-if="form.zoneType === 'circular'" label="最大半径">
          <div class="value-unit-box">
            <el-input v-model="form.maximumRadius" type="number"></el-input>
            <el-select v-model="form.maximumRadiusUnit" size="small">
              <el-option v-for="(e, ix) in unitList(11)" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item v-if="form.zoneType === 'circular'" label="起始角度">
          <div class="value-unit-box">
            <el-input v-model="form.startAngle" type="number"></el-input>
            <el-select v-model="form.startAngleUnit" size="small">
              <el-option v-for="(e, ix) in unitList(18)" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item v-if="form.zoneType === 'circular'" label="终止角度">
          <div class="value-unit-box">
            <el-input v-model="form.stopAngle" type="number"></el-input>
            <el-select v-model="form.stopAngleUnit" size="small">
              <el-option v-for="(e, ix) in unitList(18)" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="偏航角">
          <div class="value-unit-box">
            <el-input v-model="form.heading" type="number"></el-input>
            <el-select v-model="form.headingUnit" size="small">
              <el-option v-for="(e, ix) in unitList(18)" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="颜色">
          <el-color-picker v-model="form.color"></el-color-picker>
        </el-form-item>

        <div v-if="form.zoneType === 'polygonal'" :class="[isSelectPoint ? 'selecting' : '', 'select-position']" @click="isSelectPoint = !isSelectPoint">
          <el-icon class="position-icon" title="地图选点"> <Location /> </el-icon>
          <p>地图选点</p>
        </div>
      </el-form>
      <el-table v-if="form.zoneType === 'polygonal'" ref="pointsTable" :data="points" :show-header="true" border max-height="160">
        <el-table-column label="经度" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.longitude" type="number" :step="0.000001"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="纬度" align="center">
          <template #default="scope"> <el-input v-model="scope.row.latitude" type="number" :step="0.000001"></el-input> </template
        ></el-table-column>
        <el-table-column label="操作" width="60" align="center">
          <template #default="scope"> <i class="icon delete-icon" @click="points.splice(scope.$index, 1)"></i> </template
        ></el-table-column>
      </el-table>
      <div class="draw-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="addZone">确定</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.drawDialog {
  position: absolute;
  top: 10%;
  left: 35%;
  z-index: 9;
  width: 500px;
  background-color: var(--panel-bg-color);
  border: var(--border);
  box-sizing: border-box;

  .header {
    padding: 5px 0;
    font-size: 17px;
    color: #c3f8ff;
    background: var(--title-bg-color);
  }

  .el-table {
    width: calc(100% - 20px);
    margin: 10px;

    :deep(.el-table__row) {
      height: 35px;
    }
  }

  .value-unit-box {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .el-input {
      width: 70%;
    }

    .el-select {
      width: 29%;
    }
  }

  .select-position {
    margin-left: 5px;

    .position-icon {
      font-size: 25px;
    }

    p {
      font-size: 13px;
    }
  }

  .selecting {
    color: var(--icon-color);
    cursor: crosshair;
  }

  .draw-footer,
  .el-form {
    padding: 10px;
  }
}
</style>
