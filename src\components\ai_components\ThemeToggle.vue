<template>
  <button class="theme-toggle" @click="chatStore.toggleTheme">
    {{ chatStore.isDarkMode ? '🌙' : '☀️' }}
  </button>
</template>

<script setup>
import {useChatStore} from '@/pinia/chat';

const chatStore = useChatStore();
</script>

<style scoped>
.theme-toggle {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  font-size: 18px;
  cursor: pointer;
  z-index: 100;
  padding: 6px 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}
</style>
