<template>
  <button class="theme-toggle" @click="chatStore.toggleTheme">
    {{ chatStore.isDarkMode ? '🌙' : '☀️' }}
  </button>
</template>

<script setup>
import {useChatStore} from '@/pinia/chat';

const chatStore = useChatStore();
</script>

<style scoped>
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  z-index: 1000;
}
</style>
