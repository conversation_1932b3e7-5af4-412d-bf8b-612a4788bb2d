<!--
 * @Description: 实体参数配置组件
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-13 10:34:59
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-13 15:27:12
-->
<script setup>
import {ElMessage} from 'element-plus';
import {computed, onMounted, reactive, ref, toRefs, watch, getCurrentInstance} from 'vue';
import {useMapStore} from '@/pinia/map/index';
import {useStateStore} from '@/pinia/state/index';
import {storeToRefs} from 'pinia';
import timer from '@/utils/timer';
import {getLinkTargetAPI} from '@/api/index';
import {entityParamSerach, getRouteData, getZoneData, getScenarioModelParam, getLinkDatas, getScenarioZoneList, getLinkValues} from '@/utils/data_process';
import {ValidationRules, getStrWidth, calculateGMST, ecef2ECI, geodeticToECEF} from '@/utils/tools';
import {isBlockParam2} from '@/utils/data_process.js';
import {v4 as uuidv4} from 'uuid';

const INS = getCurrentInstance();
const {mapInstance} = storeToRefs(useMapStore());
const stateControler = useStateStore();

const props = defineProps({
  type: {
    type: String,
    default: 'platform',
  },
});
console.log('1 25544U 98067A   21274.73169991  .00002182  00000-0  50014-4 0  9996');
console.log('2 25544  51.6440 342.6535 0003824  69.2690 307.8388 15.48943284293991');
const {type} = toRefs(props);
const linkDatas = ref();
const cascaderValue = ref();
const state = reactive({
  row: '',
  form: {params: [], modules: [{params: []}]},
  sixLineForm: {},
  mountSchemeData: '', //挂载数据
  rules: ValidationRules,
  complexData: '', //当前编辑的复杂类型数据
  currentEditPropDataType: '', //当前复杂类型数据的类型名称
  routeData: [], //航路数据（库中的）
  zoneData: [], //区域数据（库中的）
  activeModule: '3',
  activeModuleTable: '',
  moduleTypes: [
    {
      mainCategoryId: null,
      displayName: '实体属性',
      name: 'params',
    },
    {
      mainCategoryId: '3',
      displayName: '传感器',
      name: 'sensor',
    },
    {
      mainCategoryId: '4',
      displayName: '机动',
      name: 'mover',
    },
    {
      mainCategoryId: '5',
      displayName: '武器',
      name: 'weapon',
    },
    {
      mainCategoryId: '6',
      displayName: '通信',
      name: 'comm',
    },
    {
      mainCategoryId: '7',
      displayName: '行为',
      name: 'processor',
    },
  ],
});

const datetime = ref(''); // 偏移时间的值
const timeChange = rowData => {
  // console.log(JSON.parse(rowData.value), state.form.time);
  let res;
  if (typeof rowData.value !== 'string') {
    res = rowData.value;
  } else {
    res = JSON.parse(rowData.value);
  }
  const dateStr = datetime.value.toDateString().split(' ');
  const month = dateStr[1];
  const year = dateStr[3];
  const day = dateStr[2];
  const timeStr = datetime.value.toLocaleString().split(' ');
  const time = timeStr[1];
  res[0].value = month;
  res[1].value = day;
  res[2].value = year;
  res[3].value = time;
  rowData.value = JSON.stringify(res);
};
const echoTime = rowData => {
  let res;
  if (typeof rowData.value !== 'string') {
    res = rowData.value;
  } else {
    res = JSON.parse(rowData.value);
  }
  if (res.some(i => i.value === '')) {
    datetime.value = new Date('2024/01/01 00:00:00');
    timeChange(rowData);
  } else {
    datetime.value = new Date(res.map(i => i.value).join(','));
  }
};

const satelliteTLE = (TLE1, TLE2, callback) => {
  const satrec = satellite.twoline2satrec(TLE1, TLE2);
  const time = timer.timestamp2Time(stateControler.startTime);
  const gstime = satellite.gstime(time);
  // 卫星位置和速度
  const positionAndVelocity = satellite.propagate(satrec, time);
  const positionEci = positionAndVelocity.position;

  if (positionEci) {
    const positionGd = satellite.eciToGeodetic(positionEci, gstime);
    // 转换为度
    const lon = satellite.degreesLong(positionGd.longitude);
    const lat = satellite.degreesLat(positionGd.latitude);
    const alt = positionGd.height;
    state.form.longitude = lon;
    state.form.latitude = lat;
    state.form.altitude = alt;
    if (callback) {
      callback([lon, lat, alt]);
    }
    calculateSatelliteTrack(TLE1, TLE2);
  } else {
    ElMessage.error('卫星根数或作战时间有误！');
  }
};

// 计算出卫星轨迹
let satelliteTrack = [];
const calculateSatelliteTrack = (TLE1, TLE2) => {
  satelliteTrack = [];
  const satrec = satellite.twoline2satrec(TLE1, TLE2);

  //获取轨道周期(分钟)
  // const Pi = 3.1415926535;
  const meanMotion = satrec.no;
  const orbitPeriod = (2 * Math.PI) / meanMotion;

  // 设置时间间隔和循环次数  (覆盖一整周轨道)
  const numPoints = 360;
  const timeInterval = (orbitPeriod * 60) / numPoints; //时间间隔秒
  // const time0 = state.form.time;
  const time0 = timer.timestamp2Time(stateControler.startTime);

  const positionEciAry = [];
  for (let index = 0; index <= numPoints; index++) {
    const time = new Date(time0.getTime() + index * timeInterval * 1000);
    const positionAndVelocity = satellite.propagate(satrec, time);
    const positionEci = positionAndVelocity.position;
    positionEciAry.push(positionEci);
  }
  const gmst = satellite.gstime(time0);
  positionEciAry.forEach(positionEci => {
    const positionGd = satellite.eciToGeodetic(positionEci, gmst);
    const longitude = satellite.degreesLong(positionGd.longitude);
    const latitude = satellite.degreesLat(positionGd.latitude);
    satelliteTrack.push([longitude, latitude]);
  });
  // 穿过180的处理
  satelliteTrack.forEach((coord, index) => {
    if (index > 0) {
      const prevCood = satelliteTrack[index - 1][0];
      if (coord[0] - prevCood >= 180) {
        coord[0] -= 360;
      } else if (coord[0] - prevCood < -180) {
        coord[0] += 360;
      }
    }
  });
  satelliteTrack.splice(satelliteTrack[satelliteTrack.length - 1], satelliteTrack[0]);

  const satelliteLineGeojsonData = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        properties: {},
        geometry: {type: 'LineString', coordinates: satelliteTrack},
      },
    ],
  };
  mapInstance.value.map.getSource('satellite-track').setData(satelliteLineGeojsonData);
};

// 目标列表
const targetList = computed(() => {
  return getScenarioModelParam(mapInstance.value.geojson).filter(i => {
    return i.entityId !== stateControler.currentModelData.properties.entityId;
  });
});

const modules = ref();
if (stateControler.entityModPar.get(stateControler.currentModelData?.properties?.id)) {
  modules.value = JSON.parse(JSON.stringify(stateControler.entityModPar.get(stateControler.currentModelData?.properties?.id)?.modules));
} else {
  modules.value = [];
}

// 当前实体的组件
const entityModules = computed(() => modules.value || []);

// 当前实体的参数
const entityParams = computed(() => JSON.parse(JSON.stringify(stateControler.entityModPar.get(stateControler.currentModelData.properties.id)?.params || [])));
watch(
  () => stateControler.currentModelData.properties.entityId,
  async newEntityId => {
    if (!newEntityId) return;
    modules.value = JSON.parse(JSON.stringify(stateControler.entityModPar.get(stateControler.currentModelData?.properties?.id)?.modules));
    const entityData = stateControler.currentModelData.properties;
    if (Object.keys(entityData).length === 0) return (state.form = {});
    state.form = entityData;
    // 兼容旧想定无参数
    state.form.appendValue = state.form.appendValue ? state.form.appendValue : 'msl';
    // 存上更改的参数和组件
    entityData.params = entityParams.value;
    entityData.modules = entityModules.value;
    // 如果是六根数卫星(回显历元日期时间)
    if (entityData.modelType === 2) {
      const mover = entityData.modules.find(i => i.mainCategoryId === 4);
      if (mover) {
        mover.params.forEach(item => {
          if (item.name === 'epoch_date_time') echoTime(item);
        });
      }
    }
    // 获取除航路以外所有link参数的下拉列表数据

    linkDatas.value = await getLinkDatas(entityData);
    // entityData.modules = getLinkValues(entityData.modules, linkDatas.value);
    if (type.value === 'deploy') return;
    //todo 为回显航路，绑定的值要改回数组
    const route = entityParams.value.find(i => i.name === 'route');
    if (route && route.value && typeof route.value === 'string') {
      route.value = route.value.split(',');
      route.value[0] = Number(route.value[0]);
    }
    //todo 为回显zone，绑定的值要改回数组
    const zone = entityParams.value.find(i => i.name === 'zone' && i.defaultType === 'link');
    if (zone && zone.value && typeof zone.value === 'string') {
      zone.value = zone.value.split(',');
      zone.value[0] = Number(zone.value[0]);
    }
    //todo 更新想定航路列表
    const index = state.routeData.findIndex(i => i.name === 'scenario_route');
    if (index === -1) return;
    state.routeData[index].selections = mapInstance.value.graphicalGeojson.features
      .map(i => {
        i.name = i.displayName;
        return i;
      })
      .filter(e => e.geometry.type === 'LineString');
    //如果想定航路为空时禁用选择
    state.routeData[index].disabled = state.routeData[index].selections.length === 0 ? true : false;
    //todo 更新想定zone列表
    state.zoneData[1].selections = getScenarioZoneList(mapInstance.value.graphicalGeojson, mapInstance.value.geojson);
    INS.proxy.$forceUpdate();
    clickTab(state.activeModule);
  },
  {deep: true}
);

const onInput = (type, value) => {
  if (value.indexOf('.') > 0) {
    value = value.slice(0, value.indexOf('.') + 7);
    switch (type) {
      case 'longitude':
        state.form.longitude = value;
        break;
      default:
        state.form.latitude = value;
        break;
    }
  }
};
const changeCascader = (val, row) => {
  // row.value = [Number(val.at(-1))];
};

const form = ref(null);
//验证表单
const validateForm = async () => {
  let flag;
  await form.value.validate((valid, msg) => {
    if (valid) {
      flag = true;
    } else {
      for (const key in msg) {
        ElMessage.error(msg[key][0].message);
      }
      flag = false;
    }
  });
  if (flag) datetime.value = ''; //保存完成后清掉时间
  return flag;
};
const handleBlurInput = row => {
  if (!row.value) return;
  if (row.minValue || row.maxValue) {
    row.value = Math.max(row.value, row.minValue);
    row.value = Math.min(row.value, row.maxValue);
    if (row.defaultType === 'int') row.value = Math.floor(row.value);
    row.value = row.value.toString();
    //todo 用户是否编辑了该字段(不编辑的不传给后端)
    // row.paramsIsEdit = true;
  }
};
const setHidden = ({row}) => {
  return entityParamSerach(row) ? '' : 'hidden';
};

// 复制参数
const copyParam = (table, index, copydata) => {
  const data = JSON.parse(JSON.stringify(copydata));
  const id = uuidv4().replace(/-/g, '');
  data.id = id;
  table.splice(index, 0, data);
  INS.proxy.$forceUpdate();
};
// 删除参数
const deleteParam = (table, data) => {
  const index = table.findIndex(i => i.id === data.id);
  table.splice(index, 1);
};
(async () => {
  state.routeData = await getRouteData(0);
  state.zoneData = await getZoneData(0);
})();
const clickTab = val => {
  const nowModules = entityModules.value.filter(item => {
    return item.mainCategoryId === val;
  });
  if (nowModules.length > 0) {
    state.activeModuleTable = nowModules[0].name;
  }
};
const isInCommchain = () => {
  let result = false;
  try {
    stateControler.commandChainData.forEach(side => {
      side.content.forEach(item => {
        if (item.children.length > 0) {
          result = findEntity(item.children);
          if (result) {
            throw new Error('break');
          }
        }
      });
    });
  } catch (error) {
    if (error) return true;
  }
  return result;
};
// const findPath = (id, path) => {
//   console.log('🚀 ~ findPath ~ id:', id);
//   console.log('🚀 ~ findPath ~ path:', path);
//   cascaderValue.value = [path.split(',').at(-1), id[0].toString()];
//   console.log('🚀 ~ findPath ~ cascaderValue.value:', cascaderValue.value);
//   // cascaderValue.value = findCascaderPath();
// };
const findEntity = datas => {
  return datas.some(item => {
    if (item.children.length > 0) {
      return findEntity(item.children);
    } else {
      return item.entityId === stateControler.currentModelData.properties.entityId;
    }
  });
};
const sideVisible = () => {
  if (isInCommchain()) {
    ElMessage.error('该实体存在于指挥链中，无法修改阵营信息，若要修改请先删除指挥链中的该实体。');
  }
};
// 获取长度单位
const getLengthUnit = data => {
  try {
    const id = [...stateControler.unitConfig.values()].find(i => i.name === 'length').id;
    const unitList = stateControler.unitConfig.get(id).params;
    const defaultUnit = unitList.find(i => i.default);
    // 判断是否有默认值设置
    if (!data.altitudeUnit) data.altitudeUnit = defaultUnit ? defaultUnit.name : '';
    return stateControler.unitConfig.get(id).params;
  } catch {
    return [];
  }
};
defineExpose({validateForm, satelliteTLE});
</script>

<template>
  <div class="entity-param-config">
    <el-form ref="form" label-width="70px" class="entity-param-form" label-position="left" :model="state.form">
      <!--批量部署只能编辑高度 -->
      <el-row v-if="type === 'deploy'" class="header-params">
        <el-form-item label="高度/m">
          <el-input v-model="state.form.altitude" type="number"></el-input>
        </el-form-item>
      </el-row>
      <el-row v-else class="header-params">
        <el-col class="header-paramters">
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称" label-width="45px" prop="entityName" :rules="state.rules.value">
                <el-input v-model.trim="state.form.entityName"></el-input>
              </el-form-item>
              <el-form-item label="经度" label-width="45px" prop="longitude" :rules="state.rules.lon">
                <el-input v-model="state.form.longitude" type="number" :disabled="state.form.modelType" :step="0.000001" @input="onInput('longitude', state.form.longitude)"></el-input>
              </el-form-item>
              <el-form-item label="&nbsp;&nbsp;高度" label-width="45px">
                <div class="height-box">
                  <el-input v-model="state.form.altitude" :disabled="state.form.modelType" :step="0.000001" type="number"></el-input>
                  <el-select v-if="stateControler.currentEntityId !== 0" v-model="state.form.appendValue">
                    <el-option value="msl" label="海拔">海拔</el-option>
                    <el-option value="agl" label="地表">地表</el-option>
                  </el-select>
                  <el-select v-if="stateControler.currentEntityId !== 0" v-model="state.form.altitudeUnit">
                    <el-option v-for="(item, index) in getLengthUnit(state.form)" :key="index" :value="item.name" :label="item.displayName">{{ item.name + ' - ' + item.displayName }}</el-option>
                  </el-select>
                  <!-- <i>米</i> -->
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示名称" label-width="75px" prop="entityDisplayName" :rules="state.rules.value">
                <el-input v-model.trim="state.form.entityDisplayName"></el-input>
              </el-form-item>
              <el-form-item label="纬度" label-width="75px" prop="latitude" :rules="state.rules.lat">
                <el-input v-model="state.form.latitude" type="number" :step="0.000001" :disabled="state.form.modelType" @input="onInput('latitude', state.form.latitude)"></el-input>
              </el-form-item>
              <el-form-item label="&nbsp;&nbsp;阵营" label-width="75px">
                <!-- 这里单元列表(side)和添加实体(side)的字段不一致区分处理 -->
                <el-select v-model="state.form.side" value-key="side" suffix-icon="CaretBottom" :disabled="isInCommchain()" placeholder="请选择" @click="sideVisible">
                  <el-option v-for="item in stateControler.entityListData" :key="item.side" :label="item.title" :value="item.side"> </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 卫星
            <el-col v-show="state.form.modelType == 1" :span="24">
              <el-form-item label="TLE两行轨道数据" label-width="140px">
                <el-input v-model="state.form.TLE1"></el-input>
              </el-form-item>
              <el-form-item label="" label-width="140px">
                <el-input v-show="state.form.modelType" v-model="state.form.TLE2"></el-input>
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-col>
      </el-row>

      <!-- 组件参数 -->
      <el-tabs v-model="state.activeModule" :stretch="true" type="card" @tab-change="clickTab">
        <!-- 五大组件类型遍历 -->
        <el-tab-pane v-for="(item, index) in state.moduleTypes" :key="index" :name="item.mainCategoryId" :label="item.displayName">
          <el-form-item v-if="item.mainCategoryId" class="paramters-table">
            <el-collapse v-model="state.activeModuleTable" accordion>
              <template v-for="ele in entityModules.filter(i => i.parentId !== 125)">
                <el-collapse-item v-if="ele.mainCategoryId === Number(state.activeModule) && ele.params && ele.params.length > 0" :key="ele.id" :name="ele.name">
                  <template #title>
                    <span :title="ele.name">{{ ele.displayName }}</span>
                  </template>

                  <el-table :show-header="false" border :data="ele.params" max-height="190" :tree-props="{children: 'params'}" row-key="id" default-expand-all :indent="5">
                    <el-table-column label="参数名称" :width="getStrWidth(state.form.params)">
                      <template #default="scope">
                        <span :title="scope.row.name">{{ scope.row.displayName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="参数值" :show-overflow-tooltip="true">
                      <template #default="scope">
                        <el-form-item>
                          <!-- link类型数据 -->
                          <el-cascader
                            v-if="scope.row.defaultType === 'link'"
                            v-model="scope.row.value"
                            :options="linkDatas?.get(scope.row.id) || []"
                            :show-all-levels="false"
                            :props="{
                              expandTrigger: 'hover',
                              value: 'id',
                              label: 'displayName',
                            }"
                            suffix-icon="CaretBottom"
                            @change="
                              val => {
                                changeCascader(val, scope.row);
                              }
                            "
                            ><template #default="{data}">
                              <span :title="data.name">{{ data.displayName }}</span>
                            </template>
                          </el-cascader>
                          <el-switch v-if="scope.row.defaultType === 'enum'" v-model="scope.row.value" size="small" active-text="on" active-value="on" inactive-value="off" inactive-text="off" />
                          <!-- 历元时刻 -->
                          <el-date-picker v-if="ele.mainCategoryId === 4 && scope.row.name === 'epoch_date_time'" v-model="datetime" type="datetime" placeholder="请选择时间偏移量" @change="timeChange(scope.row)" />
                          <!-- 基本数据类型输入 -->
                          <div class="param-input-box">
                            <el-input v-if="scope.row.defaultType === 'float' || scope.row.defaultType === 'int'" v-model="scope.row.value" :min="scope.row.minValue" :max="scope.row.maxValue" type="number" @blur="handleBlurInput(scope.row, 'module')"></el-input>
                            <el-input v-if="scope.row.defaultType === 'string'" v-model="scope.row.value" type="text"></el-input>
                            <i v-if="scope.row.isRepeatable" class="icon copy-icon" @click="copyParam(ele.params, scope.$index, scope.row)"> </i>
                            <i v-if="scope.row.isRepeatable" class="icon delete-icon" @click="deleteParam(ele.params, scope.row)"> </i>
                          </div>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="unitType" label="单位" align="center" width="80px" show-overflow-tooltip> </el-table-column>
                  </el-table>
                </el-collapse-item>
              </template>
            </el-collapse>
          </el-form-item>
          <el-form-item v-else class="paramters-table">
            <el-table v-show="entityParams" :show-header="false" border :data="entityParams" max-height="210" :row-class-name="row => setHidden(row)">
              <el-table-column label="参数名称" :width="getStrWidth(entityParams)" min-width="110">
                <template #default="scope">
                  <span :title="scope.row.name">{{ scope.row.displayName }}</span>
                </template>
              </el-table-column>
              <!-- 参数值列 -->
              <el-table-column align="center" label="参数值">
                <template #default="scope">
                  <!-- <el-form-item v-if="entityParamSerach(scope.row)" :prop="'.params.' + scope.$index + '.value'" :rules="state.rules.value"> -->
                  <el-form-item v-if="entityParamSerach(scope.row)" :prop="'.params.' + scope.$index + '.value'">
                    <!-- 航路 -->
                    <el-cascader
                      v-if="scope.row.name === 'route'"
                      v-model="scope.row.value"
                      :options="state.routeData"
                      :show-all-levels="false"
                      :props="{
                        expandTrigger: 'hover',
                        value: 'id',
                        label: 'displayName',
                        children: 'selections',
                      }"
                      suffix-icon="CaretBottom"
                      ><template #default="{data}">
                        <span :title="data.name">{{ data.displayName }}</span>
                      </template>
                    </el-cascader>
                    <!-- 可摧毁 -->
                    <el-select v-if="scope.row.name === 'indestructible'" v-model="scope.row.value" suffix-icon="CaretBottom" placeholder="请选择">
                      <el-option label="可摧毁" :value="'false'"></el-option>
                      <el-option label="不可摧毁" :value="'true'"></el-option>
                    </el-select>
                    <!-- 目标设置 -->
                    <div v-if="scope.row.name === 'track'" style="width: 100%">
                      <p v-for="(element, idx) in scope.row.params" :key="idx" class="track-param">
                        <span>{{ element.name }}:</span>
                        <el-select v-if="element.name === 'platform'" v-model="element.value" suffix-icon="CaretBottom" placeholder="请选择">
                          <el-option v-for="(val, i) in targetList" :key="i" :label="val.entityDisplayName" :value="val.entityName"></el-option>
                        </el-select>
                        <el-input v-else v-model="item.value"></el-input>
                      </p>
                    </div>
                    <!-- 区域设置 -->
                    <el-cascader
                      v-if="scope.row.name === 'zone' && scope.row.defaultType === 'link'"
                      v-model="scope.row.value"
                      :options="state.zoneData"
                      suffix-icon="CaretBottom"
                      :show-all-levels="false"
                      :props="{
                        expandTrigger: 'hover',
                        value: 'id',
                        label: 'displayName',
                        children: 'selections',
                      }"
                    >
                    </el-cascader>
                    <!-- 块级参数 -->
                    <div v-if="isBlockParam2(scope.row)" class="block-param">
                      <div v-for="params in scope.row.params" :key="params.id">
                        <p>{{ params.displayName }} ：</p>
                        <!-- 特殊处理该参数script_variable -->
                        <template v-if="params.name === 'script_variable'">
                          <template v-for="(paramValue, idx) in params.value" :key="idx">
                            <span v-if="paramValue.name === '名称'" :title="paramValue.value" class="script_variable_name text-ellipsis">{{ paramValue.value }} ：</span>
                            <el-input v-if="paramValue.name === '值'" v-model="paramValue.value" class="script_variable_value" type="text"></el-input>
                          </template>
                        </template>
                        <template v-else>
                          <p v-for="(paramValue, idx) in params.value" :key="idx">
                            <el-input v-model="paramValue.value" type="text"></el-input>
                          </p>
                        </template>
                      </div>
                    </div>
                    <!-- 脚本类型 -->
                    <el-input v-if="scope.row.defaultType === 'script'" v-model="scope.row.value" type="textarea" resize="none"></el-input>

                    <!-- 基本数据类型输入 -->
                    <el-input v-if="scope.row.defaultType === 'float' || scope.row.defaultType === 'int'" v-model="scope.row.value" :min="scope.row.minValue" :max="scope.row.maxValue" type="number" @blur="handleBlurInput(scope.row)"></el-input>
                    <el-input v-if="scope.row.defaultType === 'string'" v-model="scope.row.value" type="text"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="unitType" label="单位" align="center" width="80px"> </el-table-column>
            </el-table>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
  </div>
</template>

<style lang="less" scoped>
.entity-param-config {
  .title {
    margin: 5px 0;
    color: #fff;
    text-align: center;
  }

  .param-input-box {
    display: flex;
    width: 100%;
    align-items: center;

    .el-input {
      flex: 1;
    }

    i {
      margin-left: 5px;
    }
  }

  :deep(.el-tabs__header) {
    height: 31px !important;
    background-color: #14628f;
  }

  :deep(.el-date-editor) {
    width: 100%;
    height: 25px;
  }

  :deep(.el-tabs--card .el-tabs__nav) {
    height: 31px !important;
  }

  :deep(.el-tabs--card .el-tabs__item) {
    height: 31px !important;
  }

  :deep(.el-form-item__label) {
    padding: 0 !important;
  }

  .height-box {
    display: flex;

    .el-select {
      margin-left: 5px;
    }

    :deep(.el-select__wrapper) {
      padding: 4px 5px 4px 8px;
    }

    i {
      width: 28px;
      height: 28px;
      margin-left: 2px;
      font-style: normal;
      line-height: 25px;
      border: var(--border);
    }
  }

  .module-box {
    overflow: auto;
    color: #fff;
    text-align: left;
  }

  .param-input {
    margin-left: 10px;
  }

  .setting-param {
    position: relative;
    width: 100%;
  }

  .setting-param > .el-input {
    padding: 0 10px;
  }

  .no-data {
    width: 100%;
    margin: 5px;
    text-align: center;
  }

  .header-params {
    padding-right: 10px;
    align-items: center;

    .el-form-item {
      margin: 5px 0 5px 10px;
    }
  }

  .modules-param {
    margin-top: 3px;
  }

  .el-tabs--card {
    margin-top: 5px;
  }

  :deep(.el-tabs--card .el-tabs__item.is-active) {
    color: var(--font-color) !important;
    background: linear-gradient(to top, rgb(13 145 233 / 50%), transparent);
  }

  :deep(.header-params .el-input__inner) {
    padding: 0 5px;
  }

  .el-collapse {
    width: 100%;
  }

  .track-param {
    display: flex;
    justify-content: space-between;

    span {
      margin-right: 10px;
    }
  }

  .block-param {
    width: 100%;

    p {
      margin-bottom: 5px;
    }

    .script_variable_name {
      display: inline-block;
      width: 50px;
      height: 30px;
      line-height: 30px;
      vertical-align: -10px;
    }

    .script_variable_value {
      width: calc(100% - 50px);
    }
  }
}
</style>
