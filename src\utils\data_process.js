/*
 * @Description: 数据处理文件
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-24 18:05:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-08 18:17:27
 *
 */
import pinia from '@/pinia/index';
import {useStateStore} from '@/pinia/state/index';
import {Base64} from 'js-base64';
import {COE2TLEConversionAlg} from '/public/lib/computeSix.js';
const conversionAlg = new COE2TLEConversionAlg();

const stateControler = useStateStore(pinia);

import {getPlatFormTypeTaskAPI, getModelAPI, unitConvertApi, getModelDataParamApi, getParamDataApi, getDataResouce, getRouteDataApi, getZoneDataApi, getLinkTargetAPI, getLocalFileApi, uploadLocalFileApi} from '@/api/index';
import {v4 as uuidv4} from 'uuid';
import {ElMessage} from 'element-plus';
import {number} from 'echarts';

//!--------------------- 实体列表数据处理 ---------------------
export const campList = (currentCampData, model) => {
  //获取该阵营是否有创建的该模型
  const modelArr = currentCampData.filter(ele => {
    return ele.id === model.platformTypeId;
  });
  //获取当前模型在阵营数组中的index
  const index = currentCampData.findIndex(ele => {
    return ele.id === model.platformTypeId;
  });

  //如果该模型没有创建过
  if (modelArr.length < 1) {
    modelArr.push({
      id: model.platformTypeId,
      label: model.displayName,
      children: [
        {
          id: model.entityId,
          label: model.entityDisplayName,
          platformTypeId: model.platformTypeId,
        },
      ],
    });
  } else {
    // 遍历该模型的实体数组
    const idx = modelArr[0].children.findIndex(i => i.id === model.entityId);
    // 实体数据
    const entity = {
      id: model.entityId,
      label: model.entityDisplayName,
      platformTypeId: model.platformTypeId,
    };
    // 是否为编辑模型
    if (idx !== -1) {
      modelArr[0].children.splice(idx, 1, entity);
    } else {
      modelArr[0].children.push(entity);
    }
  }

  //将当前实体添加到实体列表中
  modelArr.forEach(el => {
    //判断当前模型是否有创建过
    if (index !== -1) {
      currentCampData[index] = el;
    } else {
      currentCampData.push(el);
    }
  });
  return currentCampData;
};
//!----------------------添加实体到实体列表(编辑同此方法)---------------------
export const addToEntityList = (entityListData, modelData) => {
  //获取阵营id
  const forceSideID = modelData.properties ? modelData.properties.side : modelData.side;
  //获取properties(模型)数据
  const properties = modelData.properties ? modelData.properties : modelData;
  //获取模型名称
  const entityDisplayName = modelData.properties ? modelData.properties.entityDisplayName : modelData.entityDisplayName;
  entityListData.forEach(item => {
    //遍历阵营中的模型
    item.content.forEach(el => {
      // //删除实体
      // el.children = el.children.filter(i => {
      //   return i.label !== entityDisplayName && i.id !== modelData.entityId;
      // });
      //如果当前模型类别下没有实体
      if (el.children.length === 0) {
        item.content = item.content.filter(i => {
          return i.id !== el.id;
        });
      }
    });
    //添加到实体列表
    if (item.side === forceSideID) {
      item.content = campList(item.content, properties);
    }
  });
};

//!---------------------修改模型列表参数（添加字段以便页面使用）---------------------
export const editModelList = data => {
  data = data.filter(i => i.children && i.children.length > 0);
  data.forEach(el => {
    if (el.nodeType !== 3) {
      console.log(el);
      editModelList(el.children);
    } else {
      el.leaf = true;
    }
  });
};
//!----------------------依据实体列表的排序重整geojson的实体顺序----------------------------
export const geoJsonEntitySort = geojson => {
  geojson.features = geojson.features.filter(i => i.properties.isEntity);
  // 依据实体列表的排序重整geojson的实体顺序
  const entitySort = [];
  stateControler.entityListData.forEach(sideData => {
    sideData.content.forEach(entityCatgory => {
      entityCatgory.children.forEach(entity => {
        const currentEntity = geojson.features.find(i => i.properties.entityId === entity.id);
        entitySort.push(currentEntity);
      });
    });
  });
  // 赋值排序后的数据
  geojson.features = entitySort;
  return geojson;
};
//!----------------------获取当前想定(打开的/创建的)的地图数据----------------------------
export const getMapData = async geojson => {
  // 深拷贝数据
  const geojsonData = JSON.parse(JSON.stringify(geojson));
  // 过滤掉显示路网区域的geojson(路网的geojson数据中含有mode字段)
  geojsonData.features = geojsonData.features.filter(i => !i.mode && (i.properties.isEntity || i.isEntity));
  for (const ele of geojsonData.features) {
    // 深拷贝数据，防止每次获取改掉数据
    const entityParams = JSON.parse(JSON.stringify(stateControler.entityModPar.get(ele.properties.id)?.params));
    const entityModules = JSON.parse(JSON.stringify(stateControler.entityModPar.get(ele.properties.id)?.modules));
    if (entityParams) {
      ele.properties.params = entityParams;
    }
    if (!entityParams) continue;

    for (const element of entityParams) {
      if (element.name === 'side') {
        element.value = ele.properties.side;
      }
      if (element.name === 'altitude') {
        element.value = String(ele.properties.altitude);
      }
      if (element.name === 'latitude') {
        element.value = String(ele.properties.latitude);
      }
      if (element.name === 'longitude') {
        element.value = String(ele.properties.longitude);
      }
      if (element.name === 'position') {
        const position = JSON.parse(element.configureJson);
        const values = JSON.parse(element.value);
        position.forEach((item, index) => {
          if (item.name === '经度') {
            values[index].value = String(ele.properties.longitude);
          } else if (item.name === '纬度') {
            values[index].value = String(ele.properties.latitude);
          }
        });
        element.value = JSON.stringify(values);
        // console.log(position);
      }
      if (element.name === 'route') {
        // 判断航路
        if (element.value instanceof Array) {
          element.value = element.value.join(',');
        }
      }
      if (element.name === 'zone' && element.defaultType === 'link') {
        if (element.value instanceof Array) {
          element.value = element.value.join(',');
        }
      }
      // 块级参数处理
      if (isBlockParam2(element)) {
        element.params.forEach(param => {
          param.configureJson = typeof param.configureJson !== 'string' ? JSON.stringify(param.configureJson) : param.configureJson;
          param.value = typeof param.value !== 'string' ? JSON.stringify(param.value) : param.value;
        });
      }
      // script处理
      if (element.defaultType === 'script' && element.value) {
        const fileName = 'data.txt';
        const file = new File([element.value], fileName, {type: 'text/plain'});
        const data = new FormData();
        data.append('name', fileName);
        data.append('file', file);
        const res = await uploadLocalFileApi(data);
        if (res.data) element.value = res.data;
      }
    }
    if (entityModules) {
      const processParams = arr => {
        arr.forEach(v => {
          if (v.name === '*' && typeof v.value !== 'string') {
            v.value = v.value ? 'on' : 'off';
          }

          if (v.defaultType === 'link' && typeof v.value !== 'string') {
            v.value = v.value.length > 0 ? String(v.value[v.value.length - 1]) : '';
          }
          if (v.params && v.params.length > 0) processParams(v.params);

          if (v.defaultType === 'struct' && v.value) {
            v.configureJson = typeof v.configureJson !== 'string' ? JSON.stringify(v.configureJson) : v.configureJson;
            v.value = typeof v.value !== 'string' ? JSON.stringify(v.value) : v.value;
          }
          if (v.defaultType === 'enum' && v.value) {
            v.configureJson = typeof v.configureJson !== 'string' ? JSON.stringify(v.configureJson) : v.configureJson;
          }
          // 块级参数处理
          if (isBlockParam2(v) && v.defaultType === 'none') {
            if (v.params) {
              v.params.forEach(param => {
                param.configureJson = typeof param.configureJson !== 'string' ? JSON.stringify(param.configureJson) : param.configureJson;
                param.value = typeof param.value !== 'string' ? JSON.stringify(param.value) : param.value;
              });
            }
          }
        });
      };

      ele.properties.modules = entityModules;
      //todo 暂时处理开关的参数不正确输入
      entityModules.forEach(item => {
        if (item.params) {
          processParams(item.params);
        }
      });
    }
  }

  geoJsonEntitySort(geojsonData);
  return geojsonData;
};

//!-----------------------获取添加模型所需数据-------------------
/**
 * @param {*} isPlatFormType 是否为模型,true为模型(plateformType),false为实体(plateform)
 * @param {*} platformTypeId platformTypeId
 * @param {*} currentModelId 模型累加的最新id
 * @param {*} side 阵营id
 * @returns 结果数据，如果是有地理位置为feature，无地理位置为实体配置参数数据
 */
export const getModelData = async (param, callback) => {
  const {isPlatFormType, platformTypeId, currentModelId, side} = param;
  let copySourceData = {};
  if (param.copySourceId) {
    copySourceData = stateControler.entityModPar.get(param.copySourceId);
    if (typeof copySourceData.modules === 'object' && copySourceData.modules?.length > 0) {
      depthDelUuid(copySourceData.modules);
    }
    if (typeof copySourceData.param === 'object' && copySourceData.param?.length > 0) {
      depthDelUuid(copySourceData.params);
    }
  }
  //判断获取的是模型还是实体
  // if (isPlatFormType) {
  //   const {data} = await getModelAPI(platformTypeId);
  //   resultData = data;
  // } else {
  //   const {data} = await getModelAPI(platformTypeId);
  //   resultData = data;
  // }
  const {data: resultData} = await getModelAPI(platformTypeId);
  if (!resultData.params) {
    ElMessage.warning('该实体缺少必要参数');
    return false;
  }
  // 如果实体为卫星且参数为六根数形式
  if (resultData.modelType === 2) {
    // 获取到卫星的机动组件
    const data = resultData.modules.find(i => i.mainCategoryId === 4);
    const moverParams = data?.params;
    if (moverParams) {
      const sixParams = [
        // {name: '轨道倾角', key: 'inclination'},
        // {name: '赤交点赤经', key: 'raan'},
        // {name: '真近点角', key: 'true_anomaly'},
        {name: '半长轴', key: 'semi_major_axis'},
        // {name: '偏心率', key: 'eccentricity'},
        // {name: '近地点幅角', key: 'argument_of_periapsis'},
        // {name: '历元日期时间', key: 'epoch_date_time'},
      ];
      const satelliteMoveParamKey = moverParams.map(i => i.name);

      for (const item of sixParams) {
        const isHave = satelliteMoveParamKey.includes(item.key);
        if (!isHave) {
          ElMessage.warning(`卫星缺少必要参数${item.name}-${item.key}`);
          return false;
        }
      }
    }
  }

  let altitude;
  let appendValue;
  const name = resultData.name;
  const displayName = resultData.displayName;

  const modules = resultData.modules ?? [];
  if (param.copySourceId) {
    stateControler.entityModPar.set(resultData.id, copySourceData);
  } else {
    for (const item of resultData.params) {
      if (item.name === 'altitude') {
        altitude = item.defaultValue === '' ? '0' : item.defaultValue;
        appendValue = item.appendValue;
        // item.unitType = 'm';
      }
      // 脚本回显
      if (item.defaultType === 'script' && item.value) {
        const {data} = await getLocalFileApi(item.value);
        item.value = Base64.decode(data.data);
      }
      // 块级参数处理
      if (isBlockParam2(item)) {
        item.params.forEach(param => {
          param.value = typeof param.value !== 'string' ? JSON.stringify(param.value) : param.value;
        });
      }
    }
    stateControler.entityModPar.set(resultData.id, {params: resultData.params, modules});
  }
  // 点击添加模型
  const feature = {
    type: 'Feature',
    properties: {
      modelType: resultData.modelType,
      platformTypeId: platformTypeId, //模型id
      name: name, //模型名称
      displayName: displayName, //模型显示名称(中文)
      entityId: currentModelId, //实体id
      entityDisplayName: displayName + '_' + currentModelId, //实体显示名称(中文)
      entityName: name + '_' + currentModelId, //实体名称
      side: side, //实体所属阵营
      altitude,
      appendValue,
      latitude: 0,
      longitude: 0,
      iconId: resultData.icon, //实体的图标id
      // yaw: 90,
      color: stateControler.entityListData.filter(i => i.side === side)[0].color, //实体阵营颜色
      id: resultData.id,
      isEntity: true,
    },
    geometry: {
      type: 'Point',
      coordinates: [0, 0],
    },
    id: 'entity',
  };

  // console.log(stateControler.entityModPar, '实体参数');

  //todo 回调获取航路geojson
  const routeGeojson = await getRouteGeojson(feature.properties, side, stateControler.entityListData);
  // console.log('默认航路', routeGeojson);
  if (callback && routeGeojson) callback(routeGeojson);
  return feature;
};

//!-----------------------依据航路绑定的数据获取geojson-------------------
export const getRouteGeojson = async (entity, side, entityListData) => {
  const params = stateControler.entityModPar.get(entity.id).params;
  if (!params) return;
  for (const item of params) {
    //todo航路数据处理
    if (item.name === 'route' && item.value && item.value.length > 1) {
      const value = typeof item.value === 'string' ? item.value.split(',') : JSON.parse(JSON.stringify(item.value));
      if (!Number(value[1]) || Number(value[0]) === 787) return;
      //获取航路的信息
      const {data: routeInfo} = await getParamDataApi(value[1]);
      const name = side + '-' + routeInfo.name;
      //获取航路的参数(点集)
      const {data} = await getModelDataParamApi(value[1]);
      console.log('获取航路数据', data);
      if (data.params) {
        //航路的组成点
        const points = data.params.map(i => {
          const position = JSON.parse(i.configureJson);
          //从value字段获取经纬度的值
          const positionValue = JSON.parse(i.value);
          return {
            longitude: Number(positionValue[position.findIndex(i => i.name === '经度')].value),
            latitude: Number(positionValue[position.findIndex(i => i.name === '纬度')].value),
            altitude: Number(i.params.find(i => i.name === 'altitude').defaultValue),
            speed: Number(i.params.find(i => i.name === 'speed').defaultValue),
          };
        });

        //拼出界面，draw控件可用的数据
        const routeGeojson = {
          type: 'Feature',
          properties: {
            // entityId: entity.entityId,
            displayName: name,
            side: side,
            camp_id: side,
            color: entityListData.filter(i => i.side === side)[0].color,
            tableData: points,
            drawingtype: 'LineString',
            speed: points.map(i => i.speed),
            altitude: points.map(i => i.altitude),
          },
          geometry: {
            coordinates: points.map(i => [i.longitude, i.latitude]),
            type: 'LineString',
          },
          side: side,
          displayName: name,
          name,
        };
        return routeGeojson;
      } else {
        return false;
      }
    }
  }
};
//!------------------------实体参数筛选(筛选出参数配置弹窗中列表的数据)--------------------------------
/**
 * @param {*} row 参数数据
 * @param {*} type 要筛选数据的类型（platform或module）
 * @param {*} filterHeader 是否过滤平台类型头部的参数
 * @returns true false
 */
export const entityParamSerach = row => {
  //参数弹窗中头部的参数过滤
  const headerParam = ['side', 'altitude', 'id', 'name', 'position', 'icon', 'fuel'];
  return !headerParam.includes(row.name);
};

//!------------------------处理获取自然环境的数据--------------------------------
export const getNaturalData = async () => {
  const {data} = await getDataResouce(116); //自然环境写死116
  const res = data[0].children;

  const natural = [];
  res.forEach(item => {
    natural.push(item);
  });
  const routeNet = natural.find(item => item.displayName === '路网').children[0];
  //获取路网集合里面的路网数据
  const routeList = routeNet.modelData;
  routeList.forEach(item => {
    //获取地点的数据
    const leftTopPoint = JSON.parse(item.params[0].parameters);
    const rightBottomPoint = JSON.parse(item.params[1].parameters);
    // console.log(item);
    const place = {
      id: item.id,
      name: item.name,
      displayName: item.name,
      coordinates: [
        {
          latitude: Number(leftTopPoint[1].defaultValue),
          longitude: Number(leftTopPoint[0].defaultValue),
        },
        {
          latitude: Number(rightBottomPoint[1].defaultValue),
          longitude: Number(rightBottomPoint[0].defaultValue),
        },
      ],
    };
    natural.find(item => item.displayName === '路网').children.push(place);
  });
  //踢掉集合
  natural.find(item => item.displayName === '路网').children.splice(0, 1);
  return natural;
};
//!------------------------获取平台类型下面的任务---------------------
export const getTaskData = async id => {
  const {data} = await getPlatFormTypeTaskAPI(id);
  if (data) return data;
};
//!------------------------获取航路数据列表---------------------
export const getRouteData = async scenarioId => {
  const {data} = await getRouteDataApi(scenarioId);
  const routes = data ? data : [];
  if (routes.length === 0) return [];

  return routes;
};

//!------------------------获取区域数据列表(级联选择器的所有数据)---------------------
export const getZoneData = async scenarioId => {
  const {data} = await getZoneDataApi(scenarioId);
  const routes = data ? data : [];
  if (routes.length === 0) return [];

  return routes;
};

//!------------------------获取想定的航路数据---------------------
export const getScenarioRouteData = routeGeojson => {
  const routes = [];
  routeGeojson.features.forEach(item => {
    if (item.geometry.type !== 'LineString') return;
    const route = {};
    route.name = item.displayName;
    route.camp = item.side;
    route.type = item.geometry.type;
    route.id = item.id;
    route.points = [];
    item.properties.tableData.forEach(({longitude, latitude, altitude, speed, speedUnit, altitudeUnit}) => {
      const point = {
        longitude,
        latitude,
        altitude,
        altitudeUnit,
      };
      if (speed) point.speed = speed;
      if (speedUnit) point.speedUnit = speedUnit;
      route.points.push(point);
    });
    routes.push(route);
  });
  return routes;
};
//!------------------------想定航路数据转换为地图可显示---------------------
export const convertScenarioRoute = routes => {
  const routeGeojson = [];
  routes.forEach((item, index) => {
    if (!item.points) return null;
    const route = {
      type: 'Feature',
      properties: {
        drawingtype: '',
        tableData: item.points.map(({longitude, latitude, altitude, speed, speedUnit, altitudeUnit}) => {
          const point = {
            longitude,
            latitude,
            altitude,
            altitudeUnit,
          };
          if (speed) point.speed = speed;
          if (speedUnit) point.speedUnit = speedUnit;
          return point;
        }),
        side: item.camp,
        displayName: item.name,
        camp_id: item.camp,
      },
      geometry: {
        coordinates: [],
        type: item.type,
      },
      globalID: index + 1,
    };
    switch (item.type) {
      case 'LineString':
        route.geometry.coordinates = item.points.map(({longitude, latitude}) => [longitude, latitude]);
        break;
      // case 'Polygon':
      //   route.geometry.coordinates = [item.points.map(({longitude, latitude}) => [longitude, latitude])];
      //   break;
      case 'Point':
        route.geometry.coordinates = [item.points[0].longitude, item.points[0].latitude];
        break;

      default:
        break;
    }
    route.displayName = item.name;
    route.side = item.camp;
    route.properties.drawingtype = item.type;
    route.id = item.id;
    routeGeojson.push(route);
  });
  return routeGeojson;
};
const upDataGeojson = source => {
  source.forEach(feature => {
    feature.properties.params.forEach(param => {
      if (param.templateParamId === 'a3a8d430d19b4ac5bb492b46e1b418ff') {
        param.value = JSON.stringify(
          JSON.parse(param.value).filter(item => {
            return item.name !== '高度';
          })
        );
        param.configureJson = JSON.stringify(
          JSON.parse(param.configureJson).filter(item => {
            return item.name !== '高度';
          })
        );
      }
      console.log('🚀 ~ upDataGeojson ~ param:', param);
    });
  });
};
//!------------------------获取想定接口所需模型数据---------------------
export const getScenarioModelParam = (geojson, scenarioId) => {
  const res = geojson.features.filter(i => i.properties.entityName);
  // upDataGeojson(res); //该函数处理了模版配置错误后位置中多了高度的问题
  const entityData = JSON.parse(JSON.stringify(res.map(i => i.properties)));
  entityData.forEach(item => {
    delete item.altitudeUnit;
    delete item.altitude;
    delete item.latitude;
    delete item.longitude;
    delete item.side;
    item.platformTypeId = item.platformTypeId.toString();
    item.name = item.entityName;
    item.displayName = item.entityDisplayName;
    //todo 如果是修改想定则设置想定id
    scenarioId && (item.scenarioId = scenarioId);
  });
  return entityData;
};
const depthDelUuid = source => {
  source.forEach(item => {
    delete item.id;
    if (typeof item.param === 'object' && item.param?.length > 0) {
      depthDelUuid(item.param);
    } else {
      return;
    }
  });
};

//!------------------------删除重复的---------------------
export const delRepetModelParam = geojson => {
  const res = geojson.features.filter(i => i.properties.entityName);
  const entityData = JSON.parse(JSON.stringify(res.map(i => i.properties)));
  entityData.forEach(item => {
    if (typeof item.modules === 'object' && item.modules?.length > 0) {
      depthDelUuid(item.modules);
    }
    if (typeof item.param === 'object' && item.param?.length > 0) {
      depthDelUuid(item.params);
    }
    delete item.altitudeUnit;
    delete item.altitude;
    delete item.latitude;
    delete item.longitude;
    delete item.side;
    item.name = item.entityName;
    item.displayName = item.entityDisplayName;
  });
  return entityData;
};

//!------------------------平台类型子实体数据处理---------------------
export const getSubPlatformData = async entityData => {
  // todo 1.获取平台类型的数据
  const {data} = await getModelAPI(entityData.platformTypeId);
  // todo 2.获取平台类型的子平台参数
  const haveSub = data.params.filter(i => i.name === 'sub_platform_type');
  const subEntitys = [];
  if (haveSub.length === 0) return false;
  // todo 3.遍历子平台参数
  for (const item of haveSub) {
    const subData = JSON.parse(item.parameters);
    const subPlatNum = Number(subData[1].defaultValue);
    const subPlatformId = subData[0].defaultValue[1];

    // todo 4.获取子平台数据
    const resPlat = await getModelAPI(subPlatformId);
    const subPlatData = resPlat.data;

    // todo 5.依据数量生成实体
    for (let index = 0; index < subPlatNum; index++) {
      subEntitys.push({
        platFormId: subPlatformId, //存储子平台的id(用于获取任务)
        name: subPlatData.name + '-' + (index + 1),
        displayName: subPlatData.displayName + '-' + (index + 1),
        entityId: entityData.entityId + '-' + (index + 1),
        task: [],
        parentEntity: entityData.entityName,
      });
    }
  }
  return subEntitys;
};

//!------------------------另存想定参数处理---------------------
export const scenarioSaveAsParam = param => {
  const geojson = JSON.parse(param.mapInfo);
  geojson.features = geojson.features.filter(i => i.properties.isEntity); //只保留实体
  param.models.forEach(item => {
    // 生成id
    item.id = uuidv4().replace(/-/g, '');
    // geojson的id更新
    geojson.features.forEach(val => {
      if (val.properties.entityName === item.name) {
        val.properties.id = item.id;
      }
    });
    // 参数id去除
    if (item.params) item.params.forEach(ele => delete ele.id);

    if (item.modules) {
      // 组件id去除
      item.modules.forEach(ele => {
        delete ele.id;
        // 组件参数id去除
        if (ele.params) ele.params.forEach(v => delete v.id);
      });
    }
  });
  if (param.routes) {
    // 航路
    param.routes.forEach(ele => {
      // 重新生成id
      ele.id = uuidv4().replace(/-/g, '');
    });
  }
  param.mapInfo = JSON.stringify(geojson);
  return param;
};
// 通过id查找祖先节点
export const findAncestors = (tree, idToFind, ancestors = []) => {
  for (const node of tree) {
    if (node.id === idToFind) {
      return ancestors;
    }
    if (node.children) {
      const foundAncestors = findAncestors(node.children, idToFind, [...ancestors, node.id]);
      if (foundAncestors.length > 0) {
        return foundAncestors;
      }
    }
  }
  return [];
};

//获取实体下所有参数的link列表数据
export const getLinkDatas = async entity => {
  const linkDatas = new Map();

  const processItem = async item => {
    if (item.defaultType === 'link' && item.name !== 'route') {
      const {data} = await getLinkTargetAPI(item.componentPath);
      linkDatas.set(item.id, data);
      const res = findAncestors(data, item.value, []).reverse();
      res.push(item.value);
      if (typeof item.value === 'string') item.value = res;
    }
    if (item.params && item.params.length > 0) {
      await processItems(item.params);
    }
  };
  const processItems = async arr => {
    await Promise.all(arr.map(item => processItem(item)));
  };
  const allPromises = [];
  if (entity?.modules && entity?.modules.length > 0) {
    for (const module of entity.modules) {
      if (module.params?.length > 0) {
        allPromises.push(processItems(module.params));
      }
    }
  }
  if (entity.params?.length > 0) {
    allPromises.push(processItems(entity.params));
  }
  await Promise.all(allPromises);
  return linkDatas;
};
const findCascaderPath = (options, targetValue, path = []) => {
  for (const option of options) {
    const newPath = [...path, option.value];
    if (option.value === targetValue) return newPath;
    if (option.children) {
      const childPath = findCascaderPath(option.children, targetValue, newPath);
      if (childPath) return childPath;
    }
  }
  return null;
};
export const getLinkValues = (modules, optionsMap) => {
  const linkDatas = new Map();
  const getData = arr => {
    arr.forEach(async item => {
      if (item.defaultType === 'link' && item.name !== 'route') {
        item.cascaderValue = findCascaderPath(optionsMap.get(item.id), item.value[0]);
      }
      if (item.params && item.params.length > 0) getData(item.params);
    });
  };
  if (modules && modules.length > 0) modules.forEach(i => (i.params.length > 0 ? getData(i.params) : ''));
  // if (entity.params && entity.params.length > 0) getData(entity.params);
  return modules;
};

// 获取单位默认值
export const getDefaultUnit = unit => {
  let id;
  if (typeof unit === 'string') {
    id = [...stateControler.unitConfig.values()].find(i => i.name === unit).id;
  } else {
    id = [...stateControler.unitConfig.values()].find(i => i.id === unit).id;
  }
  const unitList = stateControler.unitConfig.get(id).params;
  const defaultUnit = unitList.find(i => i.default);
  return defaultUnit;
};

//!------------------------获取想定的zone数据（为接口提供数据）---------------------
export const getScenarioZoneData = async (graphicalGeojson, geoJson) => {
  const zones = [];
  graphicalGeojson.features.forEach(item => {
    if (item.geometry.type !== 'Polygon') return;
    const data = item.properties;
    const zone = {
      id: data.id,
      color: data.color,
      displayName: data.displayName,
      name: data.name,
      side: data.side,
      type: data.zoneType,
      polygonal: {
        coordinates: item.geometry.coordinates[0],
        heading: data.heading,
        headingUnit: data.headingUnit,
        maximumAltitude: data.maximumAltitude,
        maximumAltitudeUnit: data.maximumAltitudeUnit,
        minimumAltitude: data.minimumAltitude,
        minimumAltitudeUnit: data.minimumAltitudeUnit,
      },
    };
    zones.push(zone);
  });

  for (const item of geoJson.features) {
    if (item.properties.isEntity) continue;
    const data = item.properties;
    // 转换单位（地图只能用m显示）
    const res = await unitConvertApi('m', data.maximumRadiusUnit, Number(data.maximumRadius));
    const zone = {
      id: data.id,
      color: data.color,
      displayName: data.displayName,
      name: data.name,
      side: data.side,
      type: data.zoneType,
      circular: {
        coordinate: item.geometry.coordinates.map(i => Number(i)),
        heading: data.heading,
        headingUnit: data.headingUnit,
        maximumAltitude: data.maximumAltitude,
        maximumAltitudeUnit: data.maximumAltitudeUnit,
        minimumAltitude: data.minimumAltitude,
        minimumAltitudeUnit: data.minimumAltitudeUnit,
        stopAngle: data.stopAngle,
        stopAngleUnit: data.stopAngleUnit,
        maximumRadius: res.data,
        maximumRadiusUnit: data.maximumRadiusUnit,
        minimumRadius: data.minimumRadius,
        minimumRadiusUnit: data.minimumRadiusUnit,
        startAngle: data.startAngle,
        startAngleUnit: data.startAngleUnit,
      },
    };
    data.referencePlatform ? (zone.bindTargetId = data.referencePlatform) : '';
    zones.push(zone);
  }

  return zones;
};
//!------------------------zone数据转换为地图可显示---------------------
export const convertScenarioZone = async zones => {
  const resJson = [];

  for (const item of zones) {
    const isPolygonal = item.type === 'polygonal';
    const zone = {
      type: 'Feature',
      properties: {
        id: item.id,
        displayName: item.displayName,
        name: item.name,
        side: item.side,
        color: item.color,
        zoneType: item.type,

        minimumAltitude: isPolygonal ? item.polygonal.minimumAltitude : item.circular.minimumAltitude,
        maximumAltitude: isPolygonal ? item.polygonal.maximumAltitude : item.circular.maximumAltitude,
        minimumAltitudeUnit: isPolygonal ? item.polygonal.minimumAltitudeUnit : item.circular.minimumAltitudeUnit,
        maximumAltitudeUnit: isPolygonal ? item.polygonal.maximumAltitudeUnit : item.circular.maximumAltitudeUnit,
        heading: isPolygonal ? item.polygonal.heading : item.circular.heading,
        headingUnit: isPolygonal ? item.polygonal.headingUnit : item.circular.headingUnit,
        isEntity: false,
      },
      geometry: {
        coordinates: [],
        type: isPolygonal ? 'Polygon' : 'Point',
      },
    };

    if (item.type === 'polygonal') {
      zone.geometry.coordinates.push(item.polygonal.coordinates);
    } else if (item.type === 'circular') {
      const res = await unitConvertApi(item.circular.maximumRadiusUnit, 'm', Number(item.circular.maximumRadius));
      zone.geometry.coordinates = item.circular.coordinate;
      zone.properties.stopAngle = item.circular.stopAngle;
      zone.properties.stopAngleUnit = item.circular.stopAngleUnit;
      zone.properties.maximumRadius = res.data; //半径需要转换
      zone.properties.maximumRadiusUnit = item.circular.maximumRadiusUnit;
      zone.properties.minimumRadius = item.circular.minimumRadius;
      zone.properties.minimumRadiusUnit = item.circular.minimumRadiusUnit;
      zone.properties.startAngle = item.circular.startAngle;
      zone.properties.startAngleUnit = item.circular.startAngleUnit;
      zone.properties.longitude = item.circular.coordinate[0];
      zone.properties.latitude = item.circular.coordinate[1];
      zone.properties.angle = Math.abs(item.circular.stopAngle - item.circular.startAngle);
      item.bindTargetId ? (zone.properties.referencePlatform = item.bindTargetId) : '';
    }

    zone.id = item.id;
    resJson.push(zone);
  }

  return resJson;
};
//!------------------------获取所有的区域数据---------------------
export const getScenarioZoneList = (graphicalGeojson, geoJson) => {
  const zones = [];
  graphicalGeojson.features.forEach(item => {
    if (item.geometry.type !== 'Polygon') return;
    zones.push(item.properties);
  });

  for (const item of geoJson.features) {
    if (item.properties.isEntity) continue;
    zones.push(item.properties);
  }
  return zones;
};
// 实体参数处理更新uuid
export const entityParamProcess = data => {
  const res = JSON.parse(JSON.stringify(data));
  if (res.modules && res.modules.length > 0) {
    res.modules.forEach(item => {
      item.id = uuidv4().replace(/-/g, '');
    });
  }

  if (res.params && res.params.length > 0) {
    res.params.forEach(item => {
      item.id = uuidv4().replace(/-/g, '');
    });
  }
  const {params, modules} = res;
  return {params, modules};
};

// 判断要处理的块参数
export const isBlockParam2 = param => {
  // 不需要处理的块参数
  const noProcess = ['track', 'route'];
  return param.isBlockParam === 2 && param.params && !noProcess.includes(param.name);
};

//!------------------------六根数卫星加载---------------------
export const sixParamToTwoLine = entity => {
  // 获取到卫星的机动组件
  const data = entity.properties.modules.find(i => i.mainCategoryId === 4);
  const moverParams = data?.params;
  const inclination = moverParams.find(i => i.name === 'inclination'); // 轨道倾角，单位：度
  const raan = moverParams.find(i => i.name === 'raan'); // 升交点赤经，单位：度
  const true_anomaly = moverParams.find(i => i.name === 'true_anomaly'); // 历元时刻的真近点角，单位为度
  const semi_major_axis = moverParams.find(i => i.name === 'semi_major_axis'); // 轨道半长轴，单位：千米
  const eccentricity = moverParams.find(i => i.name === 'eccentricity'); // 轨道偏心率
  const argument_of_periapsis = moverParams.find(i => i.name === 'argument_of_periapsis'); //近地点幅角
  let timeValArr;
  let epochTime;
  let Year;
  let Day;
  const modulesTime = moverParams.find(i => i.name === 'epoch_date_time')?.value;
  if (modulesTime !== undefined && typeof modulesTime !== 'string' && modulesTime.length > 0) {
    epochTime = modulesTime;
    // Year = epochTime[2].value.slice(2, 4);
    // Day = epochTime[1].value;
    Year = computeDays(epochTime).year + '';
    Day = computeDays(epochTime).dayOfYear + '';
  } else {
    if (modulesTime === undefined) {
      epochTime = new Date();
    } else {
      timeValArr = JSON.parse(moverParams.find(i => i.name === 'epoch_date_time').value);
      epochTime = new Date(timeValArr.map(i => i.value).join(','));
    }
    //历元时刻
    Year = String(epochTime.getFullYear()).slice(2, 4);
    Day = String(((epochTime - new Date(`20${Year}/1/1 00:00:00`)) / 1000 / 60 / 60 / 24).toFixed(8));
    if (Day < 10) Day = '00' + Day;
    if (Day < 100 && Day >= 10) Day = '0' + Day;
  }
  const sname = '01'; // 卫星编号
  const orbitalElements = {
    semiMajorAxis: semi_major_axis ? Number(semi_major_axis.value) : 7000, // 轨道半长轴，单位：千米
    eccentricity: eccentricity ? Number(eccentricity.value) : 0, // 轨道偏心率
    inclination: inclination ? Number(inclination.value) : 0, // 轨道倾角，单位：度
    argPerigee: argument_of_periapsis ? Number(argument_of_periapsis.value) : 45, // 近地点幅角，单位：度
    raan: raan ? Number(raan.value) : 0, // 升交点赤经，单位：度
    // trueAnomaly: true_anomaly ? Number(true_anomaly.value) : 0, // 历元时刻的真近点角，单位为度转换为弧度
    trueAnomaly: true_anomaly ? Number(true_anomaly.value) : Year + Day, // 历元时刻的真近点角，单位为度转换为弧度
  };

  console.log(orbitalElements, '六根数');
  console.log(Year + Day, '时间');
  const tles = conversionAlg.simpleCoe2Tle(orbitalElements, Year + Day, sname);
  return tles;
};

const computeDays = input => {
  // const input = [
  //   {value: 'May', name: '月', defaultType: 'enum'},
  //   {value: '20', name: '日', defaultType: 'int'},
  //   {value: '2025', name: '年', defaultType: 'string'},
  //   {value: '00:02:04', name: '时间（格式：hh：mm：ss）', defaultType: 'string'},
  // ];

  // 把英文月份转成数字（0-11）
  const monthMap = {
    Jan: 0,
    Feb: 1,
    Mar: 2,
    Apr: 3,
    May: 4,
    Jun: 5,
    Jul: 6,
    Aug: 7,
    Sep: 8,
    Oct: 9,
    Nov: 10,
    Dec: 11,
  };

  // 提取年月日
  const year = parseInt(input.find(i => i.name === '年').value);
  const day = parseInt(input.find(i => i.name === '日').value);
  const monthStr = input.find(i => i.name === '月').value;
  const month = monthMap[monthStr]; // 转成 0-11 的数字

  // 构建 Date 对象（忽略时间部分也没关系）
  const date = new Date(year, month, day);

  // 计算这个日期是当年的第几天
  const startOfYear = new Date(year, 0, 1); // 当年1月1日
  const dayOfYear = Math.floor((date - startOfYear) / (1000 * 60 * 60 * 24)) + 1;
  return {year, dayOfYear};
};
