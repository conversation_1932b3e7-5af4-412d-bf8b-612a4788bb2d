<!--
 * @Author: 新版预演
 * @Date: 2024-05-13 14:47:20
 * @LastEditors: 老范
 * @LastEditTime: 2025-03-26 09:47:31
 * @Description: 请填写简介
-->
<script setup>
import {addMapLayer} from '@/modules/mapLayerControler';
import Maplibre from '@/modules/Maplibre';
import {convertMsToTime, throttle} from '@/utils/tools';
import * as turf from '@turf/turf';
import {scenarioPreviewControlAPI, scenarioPreviewSocket, scenarioPreviewInitAPI, previewProgressState} from '@/api/index';
import {reactive, ref, computed, watch, onMounted} from 'vue';
import {ElMessage, ElLoading} from 'element-plus';
import {useStateStore} from '@/pinia/state/index';
import {useControler} from '@/pinia/map/controler';
import {storeToRefs} from 'pinia';
import bus from '@/utils/bus';

const stateControler = useStateStore();
const {statusList} = storeToRefs(useControler());

const stabilizeStatusAry = [0, 1, 2, 3, 4];
const emit = defineEmits(['close']);
const props = defineProps({
  scenarioPreviewVisible: {
    type: Boolean,
    default: false,
  },
});
const content = ref(null);
const vernierMove = ref(false);
const state = reactive({
  isInit: true,
  isStart: false,
  isPause: false,
  isContinue: false,
  isStop: false,
  isReset: false,

  mapInstance: '',
  jumpRange: '',
  progress: 0,
  speeds: [0.1, 0.2, 0.5, 1, 2, 5, 10, 20, 50, 100, 200],
  speed: 1,
  previewDataSocket: null,
  progressDataSocket: null,
  schedule: 0,
  runningMode: true, //运行模式
  simTime: 0, //仿真时间
  duringTime: 0,
  timer: null, //轮询的定时器
  lastFrameTimeStamp: 0,
  mouseDown: false,
});
watch(
  () => state.runningMode,
  () => {
    scenarioPreviewControlAPI({
      cmd: 4,
      mode: state.runningMode,
      scheduleRatio: Number(state.progress),
      speedRatio: state.speed,
    }).catch(() => ElMessage.error('运行错误'));
  }
);
watch(
  () => state.simTime,
  () => {
    timeScale.value.seekTo(1, state.simTime);
  }
);
watch(
  () => state.duringTime,
  value => {
    if (value === 0) {
      timeScaleInit(3600);
    } else {
      timeScaleInit(value);
    }
  }
);
const dialogVisible = computed(() => {
  return props.scenarioPreviewVisible;
});
const closeDialog = () => {
  reset();
  emit('close');
  if (state.timer) {
    clearInterval(state.timer);
  }
};
const render = () => {
  state.mapInstance = new Maplibre();
  state.mapInstance.initMap(content.value, true);
  state.mapInstance.map.on('load', () => {
    addMapLayer(state.mapInstance.map);
    initSseControl();
    //开启轮询
    // subscribe();
    if (state.previewDataSocket) return;
    state.previewDataSocket = new WebSocket(scenarioPreviewSocket());
    state.previewDataSocket.onmessage = e => {
      if (e.data) {
        const data = JSON.parse(e.data).geojson;
        state.simTime = JSON.parse(e.data).timeStamp * 1000;
        state.mapInstance.map.getSource('previewSource').setData(data);
      }
    };
  });
};

const changeSpeed = () => {
  scenarioPreviewControlAPI({cmd: 4, mode: state.runningMode, speedRatio: state.speed}).catch(() => ElMessage.error('运行错误'));
};
const reset = () => {
  socketControl('3');
  state.runningMode = true;
  state.progress = 0;
  state.duringTime = 0;
  state.simTime = 0;
  // state.mapInstance.map.getSource('previewSource').setData({type: 'FeatureCollection', features: []});
  // state.mapInstance.map.getSource('lineTrack').setData({type: 'FeatureCollection', features: []});
};

const jump = time => {
  scenarioPreviewControlAPI({
    cmd: 5,
    mode: state.runningMode,
    scheduleRatio: Number(time / 1000),
    speedRatio: state.speed,
  }).catch(() => ElMessage.error('运行错误'));
};

const subscribe = async () => {
  const {data} = await previewProgressState();
  if (data) {
    // 获取并显示消息 成功
    getEngineStatus(data);
    // 再次调用 this.subscribe() 以获取下一条消息
    await new Promise(resolve => (state.timer = setTimeout(resolve, 1000)));
    await subscribe();
  }
  // else {
  //   // 一秒后重新连接
  //   await new Promise(resolve => (state.timer = setTimeout(resolve, 1000)));
  //   await subscribe();
  // }
};
// 获取引擎状态
const getEngineStatus = data => {
  // console.log('获取引擎状态', data);
  state.schedule = (Number(data.schedule) * 100).toFixed(2);
  state.progress = Number(data.schedule) * 100;
  state.runningMode = data.mode;
  state.speed = data.speed;
  state.duringTime = data.timeStamp;
  // state.simTime = convertMsToTime(data.timeStamp);
  state.lastFrameTimeStamp = data.lastFrameTimeStamp / 1000;
  if (stabilizeStatusAry.includes(data.taskStatus)) {
    changeBtnsStatus(statusList.value[data.taskStatus]);

    if (data.taskStatus === 0) {
      state.mapInstance.map.getSource('previewSource').setData({type: 'FeatureCollection', features: []});
      state.mapInstance.map.getSource('lineTrack').setData({type: 'FeatureCollection', features: []});
      vernierMove.value = false;
    } else {
      vernierMove.value = true;
    }
  } else {
    ElMessage.error('后端异常,请排查后端服务!');
  }
};
const changeBtnsStatus = list => {
  state.isInit = list.isInit;
  state.isStart = list.isStart;
  state.isPause = list.isPause;
  state.isContinue = list.isContinue;
  state.isStop = list.isStop;
  state.isReset = list.isReset;
};
// 运控部分
const socketControl = async order => {
  await scenarioPreviewControlAPI({
    cmd: Number(order),
    mode: state.runningMode,
    // scheduleRatio: Number(this.progress),
  }).catch(() => ElMessage.error('运行错误'));
};
//  初始化
const initSseControl = async () => {
  scenarioPreviewControlAPI({
    cmd: 3,
    mode: state.runningMode,
    // scheduleRatio: Number(this.progress),
  }).then(async res => {
    state.runningMode = true;
    state.progress = 0;
    state.duringTime = 0;
    state.simTime = 0;
    // if (!res) return;
    if (stateControler.scenarioId === '') {
      // return ElMessage.error('请先选择想定!');
    }
    const loading = ElLoading.service({
      lock: true,
      text: '初始化想定...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    });
    try {
      const {data, msg} = await scenarioPreviewInitAPI(stateControler.scenarioId);
      if (msg === '初始化成功') {
        state.mapInstance.map.getSource('previewSource').setData(data.platforms);
        const routes = data.routes;
        state.mapInstance.map.getSource('lineTrack').setData(routes);
        ElMessage.success('初始化成功');
        clearInterval(state.timer);
        state.timer = null;
        subscribe();
        // if (state.timer) {
        //   // return;
        // } else {
        //   subscribe();
        // }
      } else {
        ElMessage.error('服务端错误，初始化失败');
      }
      loading.close();
    } catch (error) {
      loading.close();
    }
  });
};
const timeScale = ref();

const timeScaleInit = duration => {
  const sectionArr = [
    {
      id: 1,
      duration: duration * 1000,
      status: true,
      // duration: 10000
    },
  ];
  const clipsArr = [{startTime: 0, endTime: 5000000}];
  timeScale.value = timescale.render({
    ele: 'timeScale',
    sectionArr: sectionArr,
    clipsArr: clipsArr,
    showSectionStatus: true,
    indexEnable: true,
    clipEnable: true,
    // backgroundColor: '#a0a0a0',
    // fontColor: '#333',
    // iconColor: '#333'
  });
  // console.log(timeScale)
  timeScale.value.play();
  // let time = 20000;
  // timeScale.value.seekTo(1, time);
  // setInterval(function () {
  //   time += 1000;
  //   timeScale.value.seekTo(1, time);
  // }, 10);
  timeScale.value.on('play', () => {
    console.log('play');
  });
  timeScale.value.on('pause', () => {
    console.log('pause');
  });
  timeScale.value.on('clip', (clipsArr, clippedArr) => {
    console.log(clipsArr);
    console.log(clippedArr);
  });
  timeScale.value.on('seekTo', time1 => {
    console.log(time1);
    if (!vernierMove.value) {
      return;
    }
    jump(time1.time);
    // state.simTime = time1.time;
  });
};
bus.on('reSetPreview', () => {
  // console.log('需要重置');
});
</script>

<template>
  <div class="scenario-preview">
    <el-dialog v-model="dialogVisible" center title="想定预演" :before-close="() => closeDialog()" style="height: 100%; margin: 0" top="0px" width="100%" @opened="render">
      <div ref="content" class="content"></div>

      <div class="controlerGroup">
        <div class="mode-time">
          <span class="fontTime">
            <span>
              <div class="time">仿真总时长 :</div>
              <b>{{ convertMsToTime(state.duringTime * 1000) }}</b>
            </span>
            <span>
              <div class="time">当前仿真时间 :</div>
              <b>{{ convertMsToTime(state.simTime) }}</b>
            </span>
          </span>
          <span class="font">运行模式:</span>
          <el-switch v-model="state.runningMode" active-text="正向" inactive-text="逆向"> </el-switch>
          <div class="font">
            <el-select v-model="state.speed" style="width: 120px" suffix-icon="CaretBottom" @change="changeSpeed()">
              <el-option v-for="(item, index) in state.speeds" :key="index" :value="item" :label="item + 'x'"></el-option>
            </el-select>
          </div>
        </div>

        <div class="preview-control">
          <div class="controlBox">
            <div class="controlBtn">
              <el-button size="small" :disabled="!state.isInit" title="初始化" @click="initSseControl">
                <i :class="state.isInit == '' ? 'init' : 'init_light'"></i>
              </el-button>
            </div>
            <div class="controlBtn">
              <el-button size="small" :disabled="!state.isStart" title="开始" @click="socketControl('0')">
                <i :class="state.isStart ? 'begin_light' : 'begin'"></i>
              </el-button>
            </div>
            <div class="controlBtn">
              <el-button size="small" :disabled="!state.isPause" title="暂停" @click="socketControl('1')">
                <i :class="state.isPause ? 'pause_light' : 'pause'"></i>
              </el-button>
            </div>
            <div class="controlBtn">
              <el-button size="small" :disabled="!state.isContinue" title="继续" @click="socketControl('2')">
                <i :class="state.isContinue ? 'continue_light' : 'continue'"></i>
              </el-button>
            </div>
            <div class="controlBtn">
              <el-button size="small" :disabled="!state.isStop" title="停止" @click="reset">
                <i :class="state.isStop ? 'finish_light' : 'finish'"></i>
              </el-button>
            </div>
            <!-- <div class="controlBtn">
              <el-button type="primary" size="small" title="重置" :disabled="!state.isReset" @click="reset">
                <i :class="state.isReset ? 'reset_light' : 'reset'"></i>
              </el-button>
            </div> -->
            <div></div>
          </div>
        </div>
        <div id="timeScale"></div>
      </div>
    </el-dialog>
  </div>
</template>

<style>
.scenario-preview .content {
  height: 100vh;
}

.scenario-preview .el-dialog__header {
  padding: 2px 1px 0;
}

:deep(.scenario-preview .el-dialog__body) {
  padding: 4px !important;
}

.el-slider__runway {
  margin: 0;
}

.preview-control,
.control-box {
  display: flex;
  height: 60px;
  color: #fff;
  align-items: center;
  justify-content: space-between;
}

.controlBox {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.controlBox .el-button {
  background: none !important;
}

.controlTitle {
  position: absolute;
  top: 20px;
  left: 0;
  font-size: 20px;
}

.control-box {
  margin: 0 10px;
}

.control-box li {
  margin: 0 5px;
  font-size: 20px;
}

.scenario-preview .el-dialog__wrapper {
  overflow: hidden;
}

.controlBtn {
  margin: 0 10px;
}

.preview-control .el-button {
  padding: 0 !important;
  border: 0;
}

.preview-control .el-button i {
  display: inline-block;
  width: 35px;
  height: 35px;
}

.init {
  background: url('@/assets/images/init_dark.png') no-repeat;
  background-size: 100% 100%;
}

.begin {
  background: url('@/assets/images/start_dark.png') no-repeat;
  background-size: 100% 100%;
}

.pause {
  background: url('@/assets/images/pause_dark.png') no-repeat;
  background-size: 100% 100%;
}

.continue {
  background: url('@/assets/images/continue_dark.png') no-repeat;
  background-size: 100% 100%;
}

.finish {
  background: url('@/assets/images/stop_dark.png') no-repeat;
  background-size: 100% 100%;
}

.reset {
  background: url('@/assets/images/reset_dark.png') no-repeat;
  background-size: 100% 100%;
}

.init_light {
  background: url('@/assets/images/init_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.begin_light {
  background: url('@/assets/images/start_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.pause_light {
  background: url('@/assets/images/pause_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.continue_light {
  background: url('@/assets/images/continue_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.finish_light {
  background: url('@/assets/images/stop_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.reset_light {
  background: url('@/assets/images/reset_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.mode-time {
  display: flex;
  align-items: center;
  margin: 12px;
}

.font {
  display: inline-block;
  margin: 0 5px;
  color: #fff;
}

.fontTime {
  display: flex;
  margin: 0 25px 0 15px;
  color: #fff;
  flex-direction: column;
}

.controlerGroup {
  /* margin-top: -20px; */
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgb(0 0 0 / 50%);
}

.fontTime > span {
  display: flex;
}

.fontTime .time {
  width: 110px;
}

#timeScale {
  position: absolute;
  right: 30px;
  bottom: 0;
  z-index: 5;
  width: 67%;
  height: 120px;

  /* border: 1px solid #ccc; */
}
</style>
