<!--
 * @Author: 老范
 * @Date: 2025-06-26 11:14:35
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-02 18:23:49
 * @Description: 请填写简介
-->

<script setup lang="ts">
import {ref, onMounted, onUnmounted} from 'vue';
import {useStateStore} from '@/pinia/state/index';

import bus from '@/utils/bus';
import {useChatStore} from '@/pinia/chat';
import ChatContainer from '@/components/ai_components/ChatContainer.vue';
import ThemeToggle from '@/components/ai_components/ThemeToggle.vue';
const stateControler = useStateStore();

const chatStore = useChatStore();

onMounted(() => {
  document.body.classList.remove('dark-mode'); // 移除暗色模式
  document.body.classList.add('light-mode'); // 可选：添加明亮主题class
  toggleExpand();
});
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const isExpanded = ref(false);
const position = ref({x: 0, y: 0});
const isDragging = ref(false);
const dragOffset = ref({x: 0, y: 0});
const wasDragging = ref(false);

// iframe窗口大小和位置
const iframeSize = ref({width: 350, height: 736});
const iframePosition = ref({x: 1560, y: 95.5});
const isResizing = ref(false);
const resizeDirection = ref('');
const resizeStartPos = ref({x: 0, y: 0});
const resizeStartSize = ref({width: 0, height: 0});
const resizeStartPosition = ref({x: 0, y: 0});
const isMovingIframe = ref(false);
const iframeMoveOffset = ref({x: 0, y: 0});

// 节流函数，用于优化拖拽性能
let dragAnimationFrame: number | null = null;
let resizeAnimationFrame: number | null = null;

// iframe URL - 替换为你需要嵌入的页面URL
const iframeUrl = ref(`${globalConfig.scenarioAi}`);
// iframe是否已加载
const iframeLoaded = ref(false);

// 存储位置和大小的key
const POSITION_STORAGE_KEY = 'iframe-viewer-position';
const IFRAME_SIZE_STORAGE_KEY = 'iframe-viewer-size';
const IFRAME_POS_STORAGE_KEY = 'iframe-viewer-iframe-position-Task';

// 切换展开/收起状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;

  if (isExpanded.value && !iframeLoaded.value) {
    // 首次展开时，将iframe窗口居中并标记为已加载
    // centerIframeWindow();
    iframeLoaded.value = true;
  }
};

// 将iframe窗口居中
const centerIframeWindow = () => {
  const centerX = (window.innerWidth - iframeSize.value.width) / 2;
  const centerY = (window.innerHeight - iframeSize.value.height) / 2;

  // 应用边界约束到居中位置
  const constrainedPosition = constrainIframePosition(centerX, centerY, iframeSize.value.width, iframeSize.value.height);

  iframePosition.value = constrainedPosition;
};

// 开始拖动图标
const startDrag = (event: MouseEvent) => {
  if (isExpanded.value) return;

  isDragging.value = true;
  wasDragging.value = false;
  const iconElement = event.currentTarget as HTMLElement;
  const rect = iconElement.getBoundingClientRect();

  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
  };

  // 添加临时事件监听器，只在拖拽时监听
  document.addEventListener('mousemove', onDrag, {passive: false});
  document.addEventListener('mouseup', endDrag, {passive: false});

  // 添加拖拽样式类
  iconElement.classList.add('dragging');

  event.preventDefault();
  event.stopPropagation();
};

// 开始移动iframe窗口
const startMoveIframe = (event: MouseEvent) => {
  if (event.target instanceof HTMLButtonElement) return; // 避免点击关闭按钮时触发移动

  isMovingIframe.value = true;

  // 计算鼠标在iframe头部的偏移量
  iframeMoveOffset.value = {
    x: event.clientX - iframePosition.value.x,
    y: event.clientY - iframePosition.value.y,
  };

  // 阻止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();

  // 添加临时事件监听器
  window.addEventListener('mousemove', moveIframe);
  window.addEventListener('mouseup', stopMoveIframe);
};

// iframe窗口边界检测函数
const constrainIframePosition = (x: number, y: number, width: number, height: number) => {
  const margin = 10; // 距离边界的最小距离

  return {
    x: Math.max(margin, Math.min(window.innerWidth - width - margin, x)),
    y: Math.max(margin, Math.min(window.innerHeight - height - margin, y)),
  };
};

// 获取调整大小时的光标样式
const getResizeCursor = (direction: string) => {
  const cursorMap: Record<string, string> = {
    n: 'n-resize',
    s: 's-resize',
    e: 'e-resize',
    w: 'w-resize',
    ne: 'ne-resize',
    nw: 'nw-resize',
    se: 'se-resize',
    sw: 'sw-resize',
  };
  return cursorMap[direction] || 'default';
};

// 移动iframe窗口
const moveIframe = (event: MouseEvent) => {
  if (!isMovingIframe.value) return;

  const newX = event.clientX - iframeMoveOffset.value.x;
  const newY = event.clientY - iframeMoveOffset.value.y;

  // 应用边界约束
  const constrainedPosition = constrainIframePosition(newX, newY, iframeSize.value.width, iframeSize.value.height);

  iframePosition.value = {
    x: constrainedPosition.x,
    y: constrainedPosition.y,
  };

  event.preventDefault();
};

// 停止移动iframe窗口
const stopMoveIframe = () => {
  if (!isMovingIframe.value) return;

  isMovingIframe.value = false;

  // 保存iframe位置到localStorage
  localStorage.setItem(IFRAME_POS_STORAGE_KEY, JSON.stringify(iframePosition.value));

  // 移除临时事件监听器
  window.removeEventListener('mousemove', moveIframe);
  window.removeEventListener('mouseup', stopMoveIframe);
};

// 开始调整iframe大小
const startResize = (event: MouseEvent, direction: string) => {
  isResizing.value = true;
  resizeDirection.value = direction;
  resizeStartPos.value = {x: event.clientX, y: event.clientY};
  resizeStartSize.value = {...iframeSize.value};
  resizeStartPosition.value = {...iframePosition.value};

  // 阻止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();

  // 添加全局事件监听器，确保即使鼠标移出手柄区域也能继续调整
  document.addEventListener('mousemove', resizeIframe, {passive: false});
  document.addEventListener('mouseup', stopResizeIframe, {passive: false});

  // 同时添加到window，确保在所有情况下都能捕获事件
  window.addEventListener('mousemove', resizeIframe, {passive: false});
  window.addEventListener('mouseup', stopResizeIframe, {passive: false});

  // 设置鼠标捕获，防止快速移动时丢失事件
  if (event.target && 'setPointerCapture' in event.target && (event as PointerEvent).pointerId) {
    (event.target as Element).setPointerCapture((event as PointerEvent).pointerId);
  }

  // 添加样式类，改变光标
  document.body.style.cursor = getResizeCursor(direction);
  document.body.style.userSelect = 'none';

  // 为iframe窗口添加调整大小的样式类
  const iframeWrapper = document.querySelector('.iframe-wrapper') as HTMLElement;
  if (iframeWrapper) {
    iframeWrapper.classList.add('resizing');
  }
};

// 调整iframe大小
const resizeIframe = (event: MouseEvent) => {
  if (!isResizing.value) return;

  // 取消之前的动画帧
  if (resizeAnimationFrame) {
    cancelAnimationFrame(resizeAnimationFrame);
  }

  // 使用 requestAnimationFrame 优化性能
  resizeAnimationFrame = requestAnimationFrame(() => {
    // 再次检查是否还在调整状态（防止竞态条件）
    if (!isResizing.value) {
      resizeAnimationFrame = null;
      return;
    }

    const dx = event.clientX - resizeStartPos.value.x;
    const dy = event.clientY - resizeStartPos.value.y;

    let newWidth = iframeSize.value.width;
    let newHeight = iframeSize.value.height;
    let newX = iframePosition.value.x;
    let newY = iframePosition.value.y;

    // 根据调整方向更新大小和位置
    if (resizeDirection.value.includes('e')) {
      newWidth = Math.max(300, resizeStartSize.value.width + dx);
    }
    if (resizeDirection.value.includes('s')) {
      newHeight = Math.max(200, resizeStartSize.value.height + dy);
    }
    if (resizeDirection.value.includes('w')) {
      newWidth = Math.max(300, resizeStartSize.value.width - dx);
      newX = resizeStartPosition.value.x + (resizeStartSize.value.width - newWidth);
    }
    if (resizeDirection.value.includes('n')) {
      newHeight = Math.max(200, resizeStartSize.value.height - dy);
      newY = resizeStartPosition.value.y + (resizeStartSize.value.height - newHeight);
    }

    // 应用边界约束
    const constrainedPosition = constrainIframePosition(newX, newY, newWidth, newHeight);

    // 更新值
    iframeSize.value.width = newWidth;
    iframeSize.value.height = newHeight;
    iframePosition.value.x = constrainedPosition.x;
    iframePosition.value.y = constrainedPosition.y;

    resizeAnimationFrame = null;
  });

  // 阻止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();
};

// 停止调整iframe大小
const stopResizeIframe = () => {
  if (!isResizing.value) return;

  isResizing.value = false;

  // 取消未完成的动画帧
  if (resizeAnimationFrame) {
    cancelAnimationFrame(resizeAnimationFrame);
    resizeAnimationFrame = null;
  }

  // 恢复默认光标和用户选择
  document.body.style.cursor = '';
  document.body.style.userSelect = '';

  // 移除iframe窗口的调整大小样式类
  const iframeWrapper = document.querySelector('.iframe-wrapper') as HTMLElement;
  if (iframeWrapper) {
    iframeWrapper.classList.remove('resizing');
  }

  // 保存iframe大小和位置到localStorage
  localStorage.setItem(IFRAME_SIZE_STORAGE_KEY, JSON.stringify(iframeSize.value));
  localStorage.setItem(IFRAME_POS_STORAGE_KEY, JSON.stringify(iframePosition.value));

  // 移除所有事件监听器
  document.removeEventListener('mousemove', resizeIframe);
  document.removeEventListener('mouseup', stopResizeIframe);
  window.removeEventListener('mousemove', resizeIframe);
  window.removeEventListener('mouseup', stopResizeIframe);
};

// 边界检测函数
const constrainPosition = (x: number, y: number) => {
  const iconSize = 50; // 图标大小（包括padding）
  const margin = 10; // 距离边界的最小距离

  return {
    x: Math.max(margin, Math.min(window.innerWidth - iconSize - margin, x)),
    y: Math.max(margin, Math.min(window.innerHeight - iconSize - margin, y)),
  };
};

// 拖动图标
const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return;

  // 如果鼠标移动了一定距离，标记为正在拖动
  wasDragging.value = true;

  // 取消之前的动画帧
  if (dragAnimationFrame) {
    cancelAnimationFrame(dragAnimationFrame);
  }

  // 使用 requestAnimationFrame 优化性能
  dragAnimationFrame = requestAnimationFrame(() => {
    const newX = event.clientX - dragOffset.value.x;
    const newY = event.clientY - dragOffset.value.y;

    // 应用边界约束
    const constrainedPosition = constrainPosition(newX, newY);

    position.value = {
      x: constrainedPosition.x,
      y: constrainedPosition.y,
    };
    dragAnimationFrame = null;
  });

  // 防止选中文本
  event.preventDefault();
};

// 结束拖动图标
const endDrag = () => {
  if (!isDragging.value) return;

  isDragging.value = false;

  // 取消未完成的动画帧
  if (dragAnimationFrame) {
    cancelAnimationFrame(dragAnimationFrame);
    dragAnimationFrame = null;
  }

  // 移除临时事件监听器
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', endDrag);

  // 移除拖拽样式类
  const iconElement = document.querySelector('.icon-wrapper') as HTMLElement;
  if (iconElement) {
    iconElement.classList.remove('dragging');
  }

  // 保存位置到localStorage（使用防抖，避免频繁写入）
  setTimeout(() => {
    localStorage.setItem(POSITION_STORAGE_KEY, JSON.stringify(position.value));
  }, 100);
};

// 点击图标
const handleIconClick = (event: MouseEvent) => {
  // 只有在没有拖动过的情况下才切换展开状态
  if (!wasDragging.value) {
    toggleExpand();
  }

  // 重置拖动状态
  wasDragging.value = false;

  // 防止事件冒泡
  event.stopPropagation();
};

// 窗口大小变化处理
const handleWindowResize = () => {
  // 确保图标位置在新的窗口边界内
  const constrainedPosition = constrainPosition(position.value.x, position.value.y);
  position.value = constrainedPosition;

  // 如果iframe窗口展开，也需要检查其位置
  if (isExpanded.value) {
    const constrainedIframePosition = constrainIframePosition(iframePosition.value.x, iframePosition.value.y, iframeSize.value.width, iframeSize.value.height);
    iframePosition.value = constrainedIframePosition;
  }
};

onMounted(() => {
  // 从localStorage加载保存的位置
  const savedPosition = localStorage.getItem(POSITION_STORAGE_KEY);
  if (savedPosition) {
    const loadedPosition = JSON.parse(savedPosition);
    // 应用边界约束到加载的位置
    position.value = constrainPosition(loadedPosition.x, loadedPosition.y);
  } else {
    // 设置默认位置为右下角，并应用边界约束
    const defaultPosition = constrainPosition(window.innerWidth - 60, window.innerHeight - 60);
    position.value = defaultPosition;
  }

  // 从localStorage加载保存的iframe大小
  const savedSize = localStorage.getItem(IFRAME_SIZE_STORAGE_KEY);
  if (savedSize) {
    iframeSize.value = JSON.parse(savedSize);
  }

  // 从localStorage加载保存的iframe位置
  const savedIframePos = localStorage.getItem(IFRAME_POS_STORAGE_KEY);
  if (savedIframePos) {
    iframePosition.value = JSON.parse(savedIframePos);
  }

  // 监听窗口大小变化
  window.addEventListener('resize', handleWindowResize);

  // 移除全局事件监听，改为在拖拽开始时动态添加
  // document.addEventListener('mousemove', onDrag);
  // document.addEventListener('mouseup', endDrag);

  // 监听AI助手弹窗事件
  bus.on('open-ai-helper', openAiHelperFromBus);
  // initWs();
  document.addEventListener('keydown', event => {
    // 检查是否同时按下了Ctrl键和Q键
    if (event.ctrlKey && event.key === 'q') {
      // 阻止默认行为（如果有的话）
      event.preventDefault();

      // 在这里执行你的代码
      console.log('Ctrl+Q pressed');
      // 调用你的函数
      saveTask();
    }
  });
});
const saveTask = () => {
  const a = [
    {
      name: 'red',
      displayName: '红方任务矩阵',
      side: 'red',
      stages: [
        {
          name: 'detect',
          displayName: '侦察',
          entityInfo:
            '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"8d3be79cf10a2f4394fe67bf83a65da2","params":[{"id":"62ad667b3ac240b7b634e4a5b6c069f3","name":"FunName","value":"goDetect","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"520daaab3d8c420fba16d942a0a844ba","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_update_route"},{"id":"3f046373f0864cef9bcd64b4a7cf6e3b","name":"script","value":"990ce49a0970408caaac35929d3e85b4","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"3440329e75a257449b7cd977a0a46116","params":[{"id":"d24b339350164125903f7b10492cfeb8","name":"FunName","value":"wait","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"f57855e432ee457a8b6d159c2f6fea3b","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_init_route"},{"id":"22108e29cb1441ee98768f13e878fa6f","name":"script","value":"673390bfc6ac44c9bf2994012dfd48db","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
          conditionType: 'sequence',
          condition: '',
          eventParamValue: '',
          timeCondition: '',
          timeUnit: '',
          sort: 1,
        },
        {
          name: 'attack',
          displayName: '打击',
          entityInfo:
            '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"ded1201b9c4eb948b59855c984515306","params":[{"id":"4f161d6681124ce6b8ea6f119734deb6","name":"FunName","value":"wait","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"eb28504c6b8f48dbb49021cbcaf1d2d9","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_init_route"},{"id":"ac8c45c8b0c54a5f971b836ddb6c7074","name":"script","value":"c97a026757db44eb860720f2f69647b9","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"825ab4490355c043050dfaf044ea74b5","params":[{"id":"163d11cefa72481fa9fbce49c6911060","name":"FunName","value":"goAttack","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"52530f327c2a4d4a97b17709456c9259","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"GROUND_MOTOR_VEHICLE_1"},{"id":"492da722285745b883d264f99f6002e1","name":"script","value":"b641e98994d14fe9a7d59b5d112e7c39","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
          conditionType: 'event',
          condition: 'SENSOR_DETECTION',
          eventParamValue:
            '[{"id":1,"eventId":1,"name":"aPlatform.Name()","soft":1,"displayName":"平台类型名称","paramType":"platform","value":"COA_DETECT_UAV"},{"id":2,"eventId":1,"name":"aTrack.TargetName()","soft":2,"displayName":"目标名称","paramType":"platform","value":"GROUND_MOTOR_VEHICLE_1"}]',
          timeCondition: '',
          timeUnit: '',
          sort: 2,
        },
        {
          name: 'assessment',
          displayName: '评估',
          entityInfo:
            '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"2bcd9961ab4e1a4ed3aea36a096135e7","params":[{"id":"0caa947f28f246169db0b89083ce8ba8","name":"FunName","value":"DamageAssessment","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"a33e035d327c4efdb13c8a48588f2098","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_update_route"},{"id":"f573f53dd9e14209988f50540350a56f","name":"script","value":"710722c0b49c443abcb06f76b9a96118","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""},{"id":"33bc09f534a7e3403f3a6fe376c66464","params":[{"id":"1d99019a0c454573961c41a0ba26462e","name":"FunName","value":"goHome","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"b2d3d86c4a7a46c1bb2bb6e72289b767","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"Airport"},{"id":"d84765cbcbdf4613a2f8649b6690eb06","name":"script","value":"2a39b3cf602a4122a1e7493b21a542c2","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"time","condition":"22","timeUnit":"m","timeCondition":">="}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"93cb882e8e07d843588b13886c39fae4","params":[{"id":"da96e50827564cb899997ed9f7c2df6f","name":"FunName","value":"goHome","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"8755b9c5f0e2478c8b3975db4e32a563","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"Airport"},{"id":"5fdb03edd46b42c2889366b577a3832b","name":"script","value":"083aef6903e04eca83a6705c7524b12f","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
          conditionType: 'event',
          condition: 'PLATFORM_DELETED',
          eventParamValue: '[{"id":5,"eventId":7,"name":"aPlatform.Name()","soft":1,"displayName":"平台类型名称","paramType":"platform","value":"GROUND_MOTOR_VEHICLE_2"}]',
          timeCondition: '',
          timeUnit: '',
          sort: 3,
        },
      ],
    },
  ];
  stateControler.missionPlanData = a;
};
const initWs = () => {
  const res = new WebSocket('ws://**************:9212');
  res.onmessage = (e: {data: string}) => {
    if (!e.data) return;
    const data = JSON.parse(e.data);
    // console.log('🚀 ~ initWs ~ data:', JSON.parse(e.data));
    // stateControler.missionPlanData = JSON.parse(e.data);
    if (e.data) {
      const a = [
        {
          name: 'red',
          displayName: '红方任务矩阵',
          side: 'red',
          stages: [
            {
              name: 'detect',
              displayName: '侦察',
              entityInfo:
                '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"8d3be79cf10a2f4394fe67bf83a65da2","params":[{"id":"62ad667b3ac240b7b634e4a5b6c069f3","name":"FunName","value":"goDetect","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"520daaab3d8c420fba16d942a0a844ba","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_update_route"},{"id":"3f046373f0864cef9bcd64b4a7cf6e3b","name":"script","value":"990ce49a0970408caaac35929d3e85b4","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"3440329e75a257449b7cd977a0a46116","params":[{"id":"d24b339350164125903f7b10492cfeb8","name":"FunName","value":"wait","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"f57855e432ee457a8b6d159c2f6fea3b","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_init_route"},{"id":"22108e29cb1441ee98768f13e878fa6f","name":"script","value":"673390bfc6ac44c9bf2994012dfd48db","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
              conditionType: 'sequence',
              condition: '',
              eventParamValue: '',
              timeCondition: '',
              timeUnit: '',
              sort: 1,
            },
            {
              name: 'attack',
              displayName: '打击',
              entityInfo:
                '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"ded1201b9c4eb948b59855c984515306","params":[{"id":"4f161d6681124ce6b8ea6f119734deb6","name":"FunName","value":"wait","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"eb28504c6b8f48dbb49021cbcaf1d2d9","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_init_route"},{"id":"ac8c45c8b0c54a5f971b836ddb6c7074","name":"script","value":"c97a026757db44eb860720f2f69647b9","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"825ab4490355c043050dfaf044ea74b5","params":[{"id":"163d11cefa72481fa9fbce49c6911060","name":"FunName","value":"goAttack","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"52530f327c2a4d4a97b17709456c9259","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"GROUND_MOTOR_VEHICLE_1"},{"id":"492da722285745b883d264f99f6002e1","name":"script","value":"b641e98994d14fe9a7d59b5d112e7c39","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
              conditionType: 'event',
              condition: 'SENSOR_DETECTION',
              eventParamValue:
                '[{"id":1,"eventId":1,"name":"aPlatform.Name()","soft":1,"displayName":"平台类型名称","paramType":"platform","value":"COA_DETECT_UAV"},{"id":2,"eventId":1,"name":"aTrack.TargetName()","soft":2,"displayName":"目标名称","paramType":"platform","value":"GROUND_MOTOR_VEHICLE_1"}]',
              timeCondition: '',
              timeUnit: '',
              sort: 2,
            },
            {
              name: 'assessment',
              displayName: '评估',
              entityInfo:
                '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"2bcd9961ab4e1a4ed3aea36a096135e7","params":[{"id":"0caa947f28f246169db0b89083ce8ba8","name":"FunName","value":"DamageAssessment","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"a33e035d327c4efdb13c8a48588f2098","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_update_route"},{"id":"f573f53dd9e14209988f50540350a56f","name":"script","value":"710722c0b49c443abcb06f76b9a96118","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""},{"id":"33bc09f534a7e3403f3a6fe376c66464","params":[{"id":"1d99019a0c454573961c41a0ba26462e","name":"FunName","value":"goHome","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"b2d3d86c4a7a46c1bb2bb6e72289b767","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"Airport"},{"id":"d84765cbcbdf4613a2f8649b6690eb06","name":"script","value":"2a39b3cf602a4122a1e7493b21a542c2","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"time","condition":"22","timeUnit":"m","timeCondition":">="}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"93cb882e8e07d843588b13886c39fae4","params":[{"id":"da96e50827564cb899997ed9f7c2df6f","name":"FunName","value":"goHome","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"8755b9c5f0e2478c8b3975db4e32a563","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"Airport"},{"id":"5fdb03edd46b42c2889366b577a3832b","name":"script","value":"083aef6903e04eca83a6705c7524b12f","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
              conditionType: 'event',
              condition: 'PLATFORM_DELETED',
              eventParamValue: '[{"id":5,"eventId":7,"name":"aPlatform.Name()","soft":1,"displayName":"平台类型名称","paramType":"platform","value":"GROUND_MOTOR_VEHICLE_2"}]',
              timeCondition: '',
              timeUnit: '',
              sort: 3,
            },
          ],
        },
      ];
      stateControler.missionPlanData = a;
    }
  };
};
// setTimeout(() => {
//   const a = [
//     {
//       name: 'red',
//       displayName: '红方任务矩阵',
//       side: 'red',
//       stages: [
//         {
//           name: 'detect',
//           displayName: '侦察',
//           entityInfo:
//             '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"8d3be79cf10a2f4394fe67bf83a65da2","params":[{"id":"62ad667b3ac240b7b634e4a5b6c069f3","name":"FunName","value":"goDetect","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"520daaab3d8c420fba16d942a0a844ba","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_update_route"},{"id":"3f046373f0864cef9bcd64b4a7cf6e3b","name":"script","value":"990ce49a0970408caaac35929d3e85b4","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"3440329e75a257449b7cd977a0a46116","params":[{"id":"d24b339350164125903f7b10492cfeb8","name":"FunName","value":"wait","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"f57855e432ee457a8b6d159c2f6fea3b","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_init_route"},{"id":"22108e29cb1441ee98768f13e878fa6f","name":"script","value":"673390bfc6ac44c9bf2994012dfd48db","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
//           conditionType: 'sequence',
//           condition: '',
//           eventParamValue: '',
//           timeCondition: '',
//           timeUnit: '',
//           sort: 1,
//         },
//         {
//           name: 'attack',
//           displayName: '打击',
//           entityInfo:
//             '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"ded1201b9c4eb948b59855c984515306","params":[{"id":"4f161d6681124ce6b8ea6f119734deb6","name":"FunName","value":"wait","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"eb28504c6b8f48dbb49021cbcaf1d2d9","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_init_route"},{"id":"ac8c45c8b0c54a5f971b836ddb6c7074","name":"script","value":"c97a026757db44eb860720f2f69647b9","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"825ab4490355c043050dfaf044ea74b5","params":[{"id":"163d11cefa72481fa9fbce49c6911060","name":"FunName","value":"goAttack","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"52530f327c2a4d4a97b17709456c9259","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"GROUND_MOTOR_VEHICLE_1"},{"id":"492da722285745b883d264f99f6002e1","name":"script","value":"b641e98994d14fe9a7d59b5d112e7c39","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
//           conditionType: 'event',
//           condition: 'SENSOR_DETECTION',
//           eventParamValue:
//             '[{"id":1,"eventId":1,"name":"aPlatform.Name()","soft":1,"displayName":"平台类型名称","paramType":"platform","value":"COA_DETECT_UAV"},{"id":2,"eventId":1,"name":"aTrack.TargetName()","soft":2,"displayName":"目标名称","paramType":"platform","value":"GROUND_MOTOR_VEHICLE_1"}]',
//           timeCondition: '',
//           timeUnit: '',
//           sort: 2,
//         },
//         {
//           name: 'assessment',
//           displayName: '评估',
//           entityInfo:
//             '[{"platFormType":"COA_DETECT_UAV","name":"COA_DETECT_UAV","displayName":"侦察无人机","entityId":2,"task":[{"id":"2bcd9961ab4e1a4ed3aea36a096135e7","params":[{"id":"0caa947f28f246169db0b89083ce8ba8","name":"FunName","value":"DamageAssessment","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"a33e035d327c4efdb13c8a48588f2098","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"uav_update_route"},{"id":"f573f53dd9e14209988f50540350a56f","name":"script","value":"710722c0b49c443abcb06f76b9a96118","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""},{"id":"33bc09f534a7e3403f3a6fe376c66464","params":[{"id":"1d99019a0c454573961c41a0ba26462e","name":"FunName","value":"goHome","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"b2d3d86c4a7a46c1bb2bb6e72289b767","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"Airport"},{"id":"d84765cbcbdf4613a2f8649b6690eb06","name":"script","value":"2a39b3cf602a4122a1e7493b21a542c2","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"time","condition":"22","timeUnit":"m","timeCondition":">="}]},{"platFormType":"COA_ATTACK_UAV","name":"COA_ATTACK_UAV","displayName":"攻击无人机","entityId":3,"task":[{"id":"93cb882e8e07d843588b13886c39fae4","params":[{"id":"da96e50827564cb899997ed9f7c2df6f","name":"FunName","value":"goHome","defaultType":"string","templateParamId":"a8c10567db8b4eb597ce8d37ae124473"},{"defaultType":"struct","id":"8755b9c5f0e2478c8b3975db4e32a563","templateParamId":"6bd7357e2a3249cbb0b2d9db186f5cbe","value":"Airport"},{"id":"5fdb03edd46b42c2889366b577a3832b","name":"script","value":"083aef6903e04eca83a6705c7524b12f","defaultType":"script","templateParamId":"67842eeb7b1b401ba27e79861ac4bb89"}],"conditionType":"sequence","condition":""}]}]',
//           conditionType: 'event',
//           condition: 'PLATFORM_DELETED',
//           eventParamValue: '[{"id":5,"eventId":7,"name":"aPlatform.Name()","soft":1,"displayName":"平台类型名称","paramType":"platform","value":"GROUND_MOTOR_VEHICLE_2"}]',
//           timeCondition: '',
//           timeUnit: '',
//           sort: 3,
//         },
//       ],
//     },
//   ];
//   stateControler.missionPlanData = a;
// }, 2000);
onUnmounted(() => {
  // 确保移除所有可能的事件监听器
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', endDrag);
  window.removeEventListener('mousemove', moveIframe);
  window.removeEventListener('mouseup', stopMoveIframe);

  // 移除调整大小相关的事件监听器
  document.removeEventListener('mousemove', resizeIframe);
  document.removeEventListener('mouseup', stopResizeIframe);
  window.removeEventListener('mousemove', resizeIframe);
  window.removeEventListener('mouseup', stopResizeIframe);

  window.removeEventListener('resize', handleWindowResize);

  // 清理调整状态
  if (isResizing.value) {
    isResizing.value = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }

  // 取消未完成的动画帧
  if (dragAnimationFrame) {
    cancelAnimationFrame(dragAnimationFrame);
    dragAnimationFrame = null;
  }
  if (resizeAnimationFrame) {
    cancelAnimationFrame(resizeAnimationFrame);
    resizeAnimationFrame = null;
  }
  // 移除AI助手弹窗事件监听
  bus.off('open-ai-helper', openAiHelperFromBus);
});

// 响应AI助手弹窗事件
function openAiHelperFromBus() {
  if (!isExpanded.value) {
    toggleExpand();
  }
}
</script>

<template>
  <div class="iframe-container">
    <!-- 图标 -->
    <!-- <div v-if="!isExpanded" class="icon-wrapper" title="ai问答" :style="{position: 'fixed', left: position.x + 'px', top: position.y + 'px'}" @click="handleIconClick">
      @mousedown="startDrag"
      <el-icon class="iframe-icon"><Monitor /></el-icon>
    </div> -->

    <!-- iframe窗口 -->
    <div
      v-show="props.visible"
      class="iframe-wrapper"
      :style="{
        position: 'fixed',
        width: iframeSize.width + 'px',
        left: iframePosition.x + 'px',
        top: iframePosition.y + 'px',
      }"
    >
      <div class="iframe-header" @mousedown="startMoveIframe">
        <span>任务规划助手</span>
        <button class="close-btn" @click="toggleExpand">×</button>
      </div>
      <!-- <iframe v-if="iframeLoaded" :src="iframeUrl" frameborder="0" class="iframe-content"></iframe> -->
      <div :class="{'dark-mode': chatStore.isDarkMode}">
        <ThemeToggle />
        <ChatContainer />
      </div>

      <!-- 调整大小的手柄 -->
      <!-- <div class="resize-handle resize-e" @mousedown="e => startResize(e, 'e')"></div>
      <div class="resize-handle resize-s" @mousedown="e => startResize(e, 's')"></div>
      <div class="resize-handle resize-w" @mousedown="e => startResize(e, 'w')"></div>
      <div class="resize-handle resize-n" @mousedown="e => startResize(e, 'n')"></div>
      <div class="resize-handle resize-ne" @mousedown="e => startResize(e, 'ne')"></div>
      <div class="resize-handle resize-nw" @mousedown="e => startResize(e, 'nw')"></div>
      <div class="resize-handle resize-se" @mousedown="e => startResize(e, 'se')"></div>
      <div class="resize-handle resize-sw" @mousedown="e => startResize(e, 'sw')"></div> -->
    </div>
  </div>
</template>

<style lang="css">
/* 全局样式与原始HTML中的相同 */
@import '@/assets/style/global.css';
</style>

<style lang="less" scoped>
.el-icon {
  margin: 0;
}
.iframe-container {
  position: absolute;
  z-index: 1100;
  top: 0px;
  right: -500px;
  // width: 500px;
}

.icon-wrapper {
  cursor: move;
  padding: 8px;
  border-radius: 50%;
  background: #0ea8ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: background 0.3s, transform 0.3s; /* 只对背景和缩放应用过渡，不包括位置 */
  user-select: none;
  display: none;
  // display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
  will-change: transform; /* 优化GPU加速 */

  &:hover {
    background: #1b8fd8;
    transform: scale(1.05);
  }

  /* 拖拽时禁用过渡效果 */
  &.dragging {
    transition: none !important;
  }
}

.iframe-icon {
  font-size: 20px;
  color: white;
  pointer-events: none;
}

.iframe-wrapper {
  background: #f9fafb;
  border: 1px solid #0ea8ff;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  height: 80vh;
}

.iframe-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #176797;
  color: white;
  cursor: move;
  user-select: none;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0 5px;
}

.iframe-content {
  flex: 1;
  width: 100%;
  height: calc(100% - 40px);
  border: none;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: var(--background-light);
  color: var(--text-light);
  transition: all 0.3s ease;
}

.iframe-content.dark-mode {
  background-color: var(--background-dark);
  color: var(--text-dark);
}

/* 确保 iframe 内的所有元素都能正确继承主题样式 */
.iframe-content * {
  box-sizing: border-box;
}

.iframe-content button {
  font-family: inherit;
}

.iframe-content textarea {
  font-family: inherit;
}

/* 调整大小的手柄 */
.resize-handle {
  position: absolute;
  background: transparent;
  z-index: 1001;
  transition: background-color 0.2s ease;
}

/* 增加手柄的有效拖拽区域，防止快速移动时丢失 */
.resize-e {
  top: 0;
  right: -12px;
  width: 24px;
  height: 100%;
  cursor: e-resize;
}

.resize-s {
  bottom: -12px;
  left: 0;
  width: 100%;
  height: 24px;
  cursor: s-resize;
}

.resize-w {
  top: 0;
  left: -12px;
  width: 24px;
  height: 100%;
  cursor: w-resize;
}

.resize-n {
  top: -12px;
  left: 0;
  width: 100%;
  height: 24px;
  cursor: n-resize;
}

.resize-ne {
  top: -12px;
  right: -12px;
  width: 24px;
  height: 24px;
  cursor: ne-resize;
}

.resize-nw {
  top: -12px;
  left: -12px;
  width: 24px;
  height: 24px;
  cursor: nw-resize;
}

.resize-se {
  bottom: -12px;
  right: -12px;
  width: 24px;
  height: 24px;
  cursor: se-resize;
}

.resize-sw {
  bottom: -12px;
  left: -12px;
  width: 24px;
  height: 24px;
  cursor: sw-resize;
}

/* 调整手柄悬停效果，便于用户识别 */
.resize-handle:hover {
  background-color: transparent;
}

/* 调整时的视觉反馈 */
.iframe-wrapper.resizing {
  user-select: none;
}

.iframe-wrapper.resizing .resize-handle {
  background-color: transparent;
}
</style>
