/*
 * @Author: 老范
 * @Date: 2025-06-09 15:13:22
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-03 10:00:10
 * @Description: 请填写简介
 */
import {ref} from 'vue';
import {marked} from 'marked';
import {useChatStore} from '@/pinia/chat';
import hljs from 'highlight.js';
export function useChat() {
  const chatStore = useChatStore();
  let controller = null;

  // 配置marked.js
  marked.setOptions({
    gfm: true,
    breaks: true,
    highlight: (code, lang) => {
      const language = hljs.getLanguage(lang) ? lang : 'plaintext';
      return hljs.highlight(code, {language}).value;
    },
  });

  const askQuestion = async (files = []) => {
    try {
      chatStore.isLoading = true;
      chatStore.tokenCount = 0;
      const startTime = Date.now();
      chatStore.answer = '';

      controller = new AbortController();

      // 构造 formData
      const formData = new FormData();
      let resText = '';
      if (chatStore.apiType === 'task') {
        resText = '请为想定ID319' + chatStore.question;
      } else {
        resText = chatStore.question;
      }
      formData.append('input_text', resText);
      if (Array.isArray(files)) {
        files.forEach(file => formData.append('files', file));
      } else if (files) {
        formData.append('files', files);
      }

      const response = await fetch(chatStore.apiEndpoint, {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      });
      console.log('🚀 ~ askQuestion ~ response:', response);
      const customHeader = response.headers.get('x-task-id');
      console.log('🚀 ~ askQuestion ~ customHeader:', customHeader);

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let markdownBuffer = '';

      while (true) {
        const {done, value} = await reader.read();
        if (done) break;

        const textChunk = decoder.decode(value, {stream: true});
        chatStore.tokenCount += textChunk.length;
        chatStore.responseTime = (Date.now() - startTime) / 1000;

        markdownBuffer += textChunk;
        chatStore.answer = marked.parse(markdownBuffer);
      }

      chatStore.apiStatus = '正常';
    } catch (error) {
      if (error.name !== 'AbortError') {
        chatStore.answer += `<div class="security-note">错误: ${error.message}</div>`;
        chatStore.apiStatus = '异常';
        console.error('API请求错误:', error);
      }
    } finally {
      chatStore.isLoading = false;
      console.log('已完成');
    }
  };

  const stopResponse = () => {
    console.log('🚀 ~ stopResponse ~ stopResponse:', controller);
    if (controller) {
      controller.abort();
      chatStore.answer += '<div class="security-note">回答已中断</div>';
    }
  };

  return {
    askQuestion,
    stopResponse,
  };
}
