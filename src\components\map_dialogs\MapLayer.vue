<template>
  <el-dialog v-model="stateControler.controlLayer" :close-on-click-modal="false" draggable title="地图图源" center width="500px">
    <div class="map-source">
      <div class="map-select-box">
        <div class="child-box">
          <div :class="['icon', currentMap === '卫星地图' ? 'seleted' : '']" @click="changeMapLayer('卫星地图')">
            <span>卫星地图</span>
          </div>
        </div>
        <div class="child-box">
          <div :class="['icon', currentMap === '深色地图' ? 'seleted' : '']" @click="changeMapLayer('深色地图')">
            <span>深色地图</span>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
import {ref} from 'vue';
import {addMapLayer} from '@/modules/mapLayerControler';
import {useMapStore} from '@/pinia/map/index';
import {useStateStore} from '@/pinia/state/index';
import {storeToRefs} from 'pinia';

const {MapGl, mapInstance} = storeToRefs(useMapStore());
const stateControler = useStateStore();
const currentMap = ref('深色地图');
const changeMapLayer = innerText => {
  if (innerText === currentMap.value) return;
  switch (innerText) {
    case '深色地图':
      mapInstance.value.map.setStyle(`${globalConfig.MapUrl}/map_dark.json`);
      break;
    case '卫星地图':
      mapInstance.value.map.setStyle(`${globalConfig.MapUrl}/map_satellite.json`);

      break;
  }
  mapInstance.value.map.once('styledata', () => {
    addMapLayer(mapInstance.value.map);
    mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
  });
  currentMap.value = innerText;
};
</script>

<style scoped>
.map-source .screenage {
  font-size: 12px;
  color: #fff;
}

.map-select-box {
  height: 130px;
  border: 1px solid rgb(217 255 245 / 10%);
  border-radius: 4px;
}

.child-box {
  position: relative;
  float: left;
  width: 62px;
  height: 62px;
  margin: 10px;
  color: #fff;
  text-align: center;
}

.child-box .icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 50px;
  border: 1px solid rgb(0 0 0 / 0%);
  cursor: pointer;
}

.icon span {
  position: absolute;
  bottom: -20px;
  left: -10px;
  display: inline-block;
  width: 70px;
}

.map-select-box :nth-child(1) .icon {
  background: url('@/assets/images/卫星地图.png') no-repeat center;
}

.map-select-box :nth-child(2) .icon {
  background: url('@/assets/images/深色地图.png') no-repeat center;
}

.seleted {
  color: var(--border-color);
  border: 1px solid #fff;
}

.close {
  float: right;
  margin: 10px 0;
}
</style>
