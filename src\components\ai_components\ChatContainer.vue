<!--
 * @Author: 老范
 * @Date: 2025-06-09 15:07:41
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-02 15:23:52
 * @Description: 请填写简介
-->
<template>
  <div :class="['iframe-chat-container', containerSizeClass]">
    <div class="iframe-main-content">
      <div class="iframe-chat-section">
        <AnswerDisplay />
      </div>

      <SettingsPanel />
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue';
import {useChatStore} from '@/pinia/chat';
import SettingsPanel from '@/components/ai_components/SettingsPanel.vue';
import AnswerDisplay from '@/components/ai_components/AnswerDisplay.vue';

const chatStore = useChatStore();

// 响应式样式类
const containerSizeClass = ref('');

// 检测iframe-chat-container宽度并应用相应的样式类
function updateContainerSizeClass() {
  const chatContainer = document.querySelector('.iframe-chat-container');
  if (chatContainer) {
    const containerWidth = chatContainer.clientWidth;
    let newClass = '';
    if (containerWidth < 600) {
      newClass = 'chat-container-small';
    } else if (containerWidth < 768) {
      newClass = 'chat-container-medium';
    } else {
      newClass = 'chat-container-large';
    }

    // 调试信息
    console.log(`Chat container width: ${containerWidth}px, Class: ${newClass}`);
    containerSizeClass.value = newClass;
  }
}

let resizeObserver = null;

onMounted(() => {
  // 初始化容器尺寸检测
  updateContainerSizeClass();
  window.addEventListener('resize', updateContainerSizeClass);

  // 使用 ResizeObserver 监听容器尺寸变化
  const chatContainer = document.querySelector('.iframe-chat-container');
  if (chatContainer && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateContainerSizeClass();
    });
    resizeObserver.observe(chatContainer);
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerSizeClass);
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});
</script>

<style scoped>
.iframe-chat-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
}

.iframe-main-content {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 15px;
  flex: 1;
  min-height: 0; /* 允许子元素收缩 */
}

.iframe-chat-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许子元素收缩 */
  overflow: hidden;
}

/* 基于iframe-chat-container宽度的响应式样式 */
.iframe-chat-container.chat-container-medium .iframe-main-content {
  flex-direction: column !important;
  gap: 10px !important;
}

.iframe-chat-container.chat-container-medium .iframe-chat-section {
  flex: 1 !important;
  min-height: 38.8889vh !important;
}

.iframe-chat-container.chat-container-medium {
  padding: 8px !important;
}

/* 小容器样式 */
.iframe-chat-container.chat-container-small .iframe-main-content {
  flex-direction: column !important;
  gap: 8px !important;
}

.iframe-chat-container.chat-container-small .iframe-chat-section {
  flex: 1 !important;
  min-height: 38.8889vh !important;
}

.iframe-chat-container.chat-container-small {
  padding: 5px !important;
}
</style>
