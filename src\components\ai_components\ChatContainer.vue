<!--
 * @Author: 老范
 * @Date: 2025-06-09 15:07:41
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-19 16:42:53
 * @Description: 请填写简介
-->
<template>
  <div class="iframe-chat-container">
    <div class="iframe-main-content">
      <div class="iframe-chat-section">
        <AnswerDisplay />
      </div>

      <SettingsPanel />
    </div>
  </div>
</template>

<script setup>
import {useChatStore} from '@/pinia/chat';
import SettingsPanel from '@/components/ai_components/SettingsPanel.vue';
import AnswerDisplay from '@/components/ai_components/AnswerDisplay.vue';

const chatStore = useChatStore();
</script>

<style scoped>
.iframe-chat-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
}

.iframe-main-content {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 15px;
  flex: 1;
  min-height: 0; /* 允许子元素收缩 */
}

.iframe-chat-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许子元素收缩 */
  overflow: hidden;
}

/* 响应式设计 - 小屏幕时垂直布局 */
@media (max-width: 768px) {
  .iframe-main-content {
    flex-direction: column !important;
    gap: 10px !important;
  }

  .iframe-chat-section {
    flex: 1 !important;
    min-height: 200px !important;
  }

  .iframe-chat-container {
    padding: 8px !important;
  }
}

/* 针对iframe环境的额外响应式样式 */
@media (max-width: 600px) {
  .iframe-main-content {
    flex-direction: column !important;
    gap: 8px !important;
  }

  .iframe-chat-section {
    flex: 1 !important;
    min-height: 150px !important;
  }

  .iframe-chat-container {
    padding: 5px !important;
  }
}
</style>
