<!--
 * @Author: 老范
 * @Date: 2025-06-09 15:07:41
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-19 16:42:53
 * @Description: 请填写简介
-->
<template>
  <div class="container">
    <!-- <header>
      <h1>OneSim智能想定生成系统 <span class="security-badge">实时问答</span></h1>
      <p>基于AFSIM+DEEPSEEK文档内容的实时问答服务</p>
    </header> -->

    <div class="main-content">
      <div class="chat-container">
        <AnswerDisplay />
        <!-- <div class="response-stats">
          <span>Tokens: {{ chatStore.tokenCount }}</span>
          <span>响应时间: {{ chatStore.formattedResponseTime }}</span>
        </div> -->
      </div>

      <SettingsPanel />
    </div>

    <!-- <footer>
      <p>© 2025 OneSim智能想定生成系统 | 基于AFSim和DeepSeek-Chat大模型 | 版本 2.0</p>
    </footer> -->
  </div>
</template>

<script setup>
import {useChatStore} from '@/pinia/chat';
import SettingsPanel from '@/components/ai_components/SettingsPanel.vue';
import AnswerDisplay from '@/components/ai_components/AnswerDisplay.vue';

const chatStore = useChatStore();
</script>
