<!--
 * @Description: 任务规范
 * @Author: tian<PERSON><PERSON><PERSON>
 * @Date: 2023-08-11 09:18:09
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-26 11:34:43
-->
<script setup>
import {getModelAPI, getTaskEventListApi, getUnitConfigApi} from '@/api/index';
import IframeViewerTask from '@/components/IframeViewerTask.vue';
import {inputChange, onBlur} from '@/utils/tools.js';
import {getTaskData, getSubPlatformData, getDefaultUnit} from '@/utils/data_process';
import Sortable from 'sortablejs';
import {reactive, computed, ref, watch, nextTick} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
import {ValidationRules} from '@/utils/tools';

const stateControler = useStateStore();
const {mapInstance} = storeToRefs(useMapStore());

const emit = defineEmits(['close']);
const props = defineProps({
  missionPlanningVisible: {
    type: Boolean,
    default: false,
  },
});
const matrixFormDom = ref('');
const stageFormDom = ref('');
const state = reactive({
  ValidationRules,
  missionPlanData: [],
  entityList: [],
  taskEventList: [],
  matrixVisible: false,
  matrixForm: {
    name: '',
    displayName: '',
    side: '',
  },
  stageForm: {
    stageDisplayName: '',
    stageName: '',
    conditionType: 'time',
    condition: '',
  },
  selectedEntity: '',
  stageTaskVisible: false,
  stageVisible: false, //阶段操作弹窗显隐
  isEditStage: false,
  isEditMatrix: false,
  isAddMatrix: false,
  addMatrixData: {name: ''},
  activeIndex: 0, //编辑的矩阵index
  selectedStageIndex: null, //当前选中的阶段
  addPosition: null,
  currentEntityTasks: [], //当前实体携带的任务列表（回显使用）
  selectedTask: '', //选择的任务
  currentStageEntity: '', //当前阶段的实体任务（源数据）
  currentStageTasks: [], //当前阶段的任务
});
// 任务相关数据
const taskState = reactive({
  taskTypes: [
    {name: '时间', value: 'time'},
    {name: '事件', value: 'event'},
    {name: '顺序', value: 'sequence'},
  ],
  eventList: [],
});

const dialogVisible = computed(() => props.missionPlanningVisible);
const abled = computed(() => state.missionPlanData.length > 0);
// 阶段列表
const stages = computed(() => {
  if (state.missionPlanData[state.activeIndex] && state.missionPlanData[state.activeIndex].stages) {
    return state.missionPlanData[state.activeIndex].stages;
  } else {
    return [];
  }
});
// 当前矩阵的实体信息
const entityData = computed(() => {
  if (state.missionPlanData[state.activeIndex]?.stages?.length) return state.missionPlanData[state.activeIndex].stages[0].entityInfo;
  else return [];
});
// 可选择的实体列表(将当前矩阵中已经添加过的实体过滤掉)
const entityOptional = computed(() => state.entityList.filter(i => !entityData.value.find(v => v.name === i.entityName) && i.side === state.missionPlanData[state.activeIndex].side));
// 是否可编辑阵营
const sideEdit = computed(() => {
  if (state.missionPlanData[state.activeIndex]?.stages?.length && state.missionPlanData[state.activeIndex]?.stages[0]?.entityInfo?.length > 0) return true;
  else return false;
});
// 查询任务的显示名称
const taskDisplayName = (entity, taskId) => {
  const platformTypeId = state.entityList.find(i => i.entityId === entity.entityId).platformTypeId;
  return taskList.value.get(platformTypeId)?.find(i => i.id === taskId)?.displayName || '';
};

// 查询参数的数据（名称、类型）
const paramDisplay = (paramId, type) => {
  // console.log(paramsList.value, paramId);
  const res = paramsList.value.find(i => i.id === paramId);
  if (!res) return ElMessage.error('任务参数丢失，请确保数据完整');
  if (type === 'defaultType') return res?.defaultType || '';
  const data = JSON.parse(res.value);
  if (type === 'name') return data.find(i => i.name === '参数名称').value;
  if (type === 'type') return data.find(i => i.name === '参数类型').value;
  if (type === 'unit') {
    const res = data.find(i => i.name === '参数单位');

    return res.value ? Number(res.value) : false;
  }
};

watch(dialogVisible, val => {
  if (!val) return;
  getTaskEventData();

  state.missionPlanData = stateControler.missionPlanData;
  console.log(state.missionPlanData);

  processMissionPlanData();

  state.entityList = mapInstance.value.geojson.features.filter(i => i.properties.isEntity).map(i => i.properties);
  //todo 实体名称修改后对任务规划的实体同步处理
  entityData.value?.forEach(item => {
    const currentEntity = state.entityList.find(i => i.entityId === item.entityId);
    if (!currentEntity) return;
    const shouldUpdateName = currentEntity?.entityName !== item.name;
    const shouldUpdateDisplayName = currentEntity?.entityDisplayName !== item.displayName;
    if (shouldUpdateName || shouldUpdateDisplayName) {
      item.displayName = currentEntity.entityDisplayName;
      item.name = currentEntity.entityName;
    }
  });
  //todo 删掉地图删除的实体的id集合
  let deletedEntityIds = entityData.value?.filter(item => !state.entityList.some(entity => entity.entityId === item.entityId)).map(item => item.entityId) || [];

  //todo 地图实体的名称集合
  const entityNames = state.entityList?.map(item => item.entityName) || [];

  //同步删除实体的子平台(子实体的id是entityId-序号)
  const deleteSubPlat = deletedEntityIds.map(i => i + '-');
  deletedEntityIds = deletedEntityIds.concat(deleteSubPlat);

  // 遍历矩阵
  state.missionPlanData.forEach(element => {
    // 阶段遍历
    element.stages.forEach(item => {
      // 阶段触发条件为事件时,需判断事件参数是否有platform类型参数
      if (item.conditionType === 'event') {
        item.eventParamValue.forEach(param => {
          if (param.paramType === 'platform') {
            entityNames.includes(param.value) ? '' : (param.value = '');
          }
        });
      }

      // 当前矩阵所属阵营的所有实体（用于删除改变阵营的实体）
      const currentSideEntityIds = state.entityList.filter(i => i.side === element.side).map(i => i.entityId);
      //删除所有阶段中被删除的以及更改阵营的实体(子实体除外)
      item.entityInfo = item.entityInfo.filter(i => !deletedEntityIds.includes(i.entityId) && currentSideEntityIds.includes(i.entityId));

      // 遍历实体
      item.entityInfo.forEach(entity => {
        //遍历任务
        entity.task.forEach(element => {
          // 任务触发条件为事件时,需判断事件参数是否有platform类型参数
          if (element.conditionType === 'event') {
            element.eventParamValue.forEach(param => {
              if (param.paramType === 'platform') {
                entityNames.includes(param.value) ? '' : (param.value = '');
              }
            });
          }
          // 处理任务参数
          element.params.forEach(ele => {
            // 如果参数绑定的平台类型被删除，置空
            if (ele.type === 'platform' && !entityNames.includes(ele.value)) ele.value = '';
          });
        });
      });
    });
  });
  getAllTaskList();
});
const sortInstance = ref(null);

watch(
  () => state.stageTaskVisible,
  val => {
    if (!val) return;
    console.log(sortInstance.value);
    nextTick(() => {
      const box = document.querySelector('.stage-task-box');
      const opt = {
        animation: 200,
        handle: '.handle',
        onEnd: evt => {
          const {newIndex, oldIndex} = evt;
          const oldData = state.currentStageTasks[oldIndex];
          const newData = state.currentStageTasks[newIndex];
          state.currentStageTasks[oldIndex] = newData;
          state.currentStageTasks[newIndex] = oldData;
        },
      };
      sortInstance.value = Sortable.create(box, opt);
    });
  }
);

const getTaskEventData = async () => {
  const {data} = await getTaskEventListApi();
  taskState.eventList = data;
};

const taskList = ref(new Map());
const paramsList = ref([]);

// 获取所有实体的任务列表并更新所有阶段实体的任务参数
const getAllTaskList = async () => {
  // 获取所有实体的类型数量
  const entityRes = Array.from(new Set(state.entityList.map(i => i.platformTypeId)));

  for (let index = 0; index < entityRes.length; index++) {
    const platformTypeId = entityRes[index];
    const res = await getTaskData(platformTypeId);
    console.log(res);
    if (taskList.value.get(platformTypeId) || !res) continue;
    taskList.value.set(platformTypeId, res);
  }
  if (taskList.value.size === 0) return;
  console.log(taskList.value, '任务列表');
  // 获取所有的参数列表
  paramsList.value = [...taskList.value.values()]
    .flat(2)
    .map(i => i.params)
    .flat(2);
  console.log(paramsList.value, '所有参数列表');

  state.missionPlanData.forEach(element => {
    element.stages.forEach(stage => {
      stage.entityInfo.forEach(entity => {
        // 获取实体的所有任务列表
        const platformTypeId = state.entityList.find(i => i.entityId === entity.entityId).platformTypeId;
        let taskDataIds;
        if (taskList.value.get(platformTypeId)) {
          taskDataIds = taskList.value.get(platformTypeId).map(i => i.id);
        }
        // 如果数据库已经删除的任务，则需要删掉
        entity.task = entity.task.filter(i => taskDataIds.includes(i.id));
        entity.task.forEach(task => {
          const currentTask = taskList.value.get(platformTypeId).find(i => i.id === task.id);
          // 获取当前任务的参数数据列表
          const currentTaskParamIds = currentTask.params.map(i => i.id);
          // 如果数据库已经删除的参数，则需要删掉
          task.params = task.params.filter(i => currentTaskParamIds.includes(i.id));
          // 判断是否有添加的参数
          currentTask.params.forEach(param => {
            const isHave = task.params.map(i => i.id).includes(param.id);

            if (!isHave) {
              // 函数名不做处理
              if (param.defaultType !== 'struct') return {id: param.id, value: param.value, templateParamId: param.templateParamId};
              // 获取参数结构
              const paramData = JSON.parse(param.value);
              const res = {};
              paramData.forEach(i => {
                let defaultUnit;
                switch (i.name) {
                  case '参数单位':
                    if (i.value) defaultUnit = getDefaultUnit(Number(i.value));
                    res.unit = defaultUnit ? defaultUnit.name : '';

                    break;
                  default:
                    break;
                }
                res.id = param.id;
                res.templateParamId = param.templateParamId;
                res.value = '';
              });
              task.params.push(res);
            }
          });
        });
      });
    });
  });
};

const processMissionPlanData = () => {
  state.missionPlanData.forEach(element => {
    if (!element.stages) return;
    //处理阶段的实体数据为界面可使用数据
    element.stages.forEach(item => {
      if (typeof item.entityInfo === 'string') item.entityInfo = JSON.parse(item.entityInfo);
      if (typeof item.eventParamValue === 'string' && item.conditionType === 'event') item.eventParamValue = JSON.parse(item.eventParamValue);
    });
  });
};

//显示矩阵弹窗
const showMatrixDialog = matrixData => {
  if (!matrixData) {
    state.isAddMatrix = true;
    state.matrixForm.name = '';
    state.matrixForm.displayName = '';
    state.matrixForm.side = '';
  } else {
    state.isAddMatrix = false;
    state.matrixForm = JSON.parse(JSON.stringify(matrixData));
  }
  state.matrixVisible = true;
};
//添加、编辑矩阵
const addEditMatrix = async () => {
  const res = await matrixFormDom.value.validate(valid => (valid ? true : false));
  if (!res) {
    if (!state.matrixForm.name) return ElMessage.warning('请输入矩阵名称');
    if (!state.matrixForm.displayName) return ElMessage.warning('请输入矩阵显示名称');
    if (!state.matrixForm.side) return ElMessage.warning('请输入矩阵阵营');
  }
  const nameRepeat = state.missionPlanData.find(i => i.name === state.matrixForm.name);
  const displayNameRepeat = state.missionPlanData.find(i => i.displayName === state.matrixForm.displayName);
  if (state.isAddMatrix) {
    if (nameRepeat) return ElMessage.warning('矩阵名称不能重复');
    if (displayNameRepeat) return ElMessage.warning('矩阵显示名称不能重复');
    state.missionPlanData.push({...state.matrixForm, stages: []});
  } else {
    const nameRepeatIndex = state.missionPlanData.findIndex(i => i.name === state.matrixForm.name);
    const displayNameRepeatIndex = state.missionPlanData.findIndex(i => i.displayName === state.matrixForm.displayName);

    if (nameRepeat && state.activeIndex !== nameRepeatIndex) return ElMessage.warning('矩阵名称不能重复');
    if (displayNameRepeat && state.activeIndex !== displayNameRepeatIndex) return ElMessage.warning('矩阵显示名称不能重复');

    state.missionPlanData[state.activeIndex].name = state.matrixForm.name;
    state.missionPlanData[state.activeIndex].displayName = state.matrixForm.displayName;
    state.missionPlanData[state.activeIndex].side = state.matrixForm.side;
    console.log('编辑矩阵');
  }
  state.matrixVisible = false;
};
// 删除矩阵
const deleteMissionPlan = () => {
  ElMessageBox.confirm('确定要删除该阶段?', {closeOnClickModal: false})
    .then(() => state.missionPlanData.splice(state.activeIndex, 1))
    .catch(() => '');
};
// 展示添加阶段弹窗
const showAddStage = addPosition => {
  state.stageVisible = true;
  state.isEditStage = false;
  state.addPosition = addPosition;
};
// 展示编辑阶段弹窗
const showEditStage = item => {
  state.stageForm.conditionType = item.conditionType;
  state.stageForm.condition = item.condition;
  state.stageForm.stageDisplayName = item.displayName;
  state.stageForm.stageName = item.name;
  if (state.stageForm.conditionType === 'time') {
    state.stageForm.timeCondition = item.timeCondition;
    state.stageForm.timeUnit = item.timeUnit;
  } else if (state.stageForm.conditionType === 'event') {
    state.stageForm.eventParamValue = item.eventParamValue;
  }
  state.stageVisible = true;
  state.isEditStage = true;
  console.log(state.stageForm, '编辑的阶段');
};
//编辑/添加阶段
const addEditStage = async () => {
  stageFormDom.value.validate((prop, valid, msg) => {
    console.log(prop, valid, msg);
  });
  stageFormDom.value.validate(valid => (valid ? true : false));

  if (!state.stageForm.stageName) return ElMessage.warning('请输入阶段名称');
  if (!state.stageForm.stageDisplayName) return ElMessage.warning('请输入阶段显示名称');
  if (!state.stageForm.conditionType) return ElMessage.warning('请输入阶段触发类型');
  switch (state.stageForm.conditionType) {
    case 'time':
      console.log(state.stageForm);
      if (!state.stageForm.condition) return ElMessage.warning('请输入触发条件');
      if (!state.stageForm.timeUnit) return ElMessage.warning('请输入触发时间单位');
      if (!state.stageForm.timeCondition) return ElMessage.warning('请输入触发条件');
      break;
    case 'event':
      if (!state.stageForm.condition) return ElMessage.warning('请输入触发条件');
      console.log(state.stageForm);

      for (const condition of state.stageForm.eventParamValue) {
        if (!condition.value) {
          return ElMessage.warning(`请输入事件参数:${condition.displayName}`);
        }
      }
      break;
    default:
      break;
  }

  if (state.missionPlanData.length === 0) return ElMessage.warning('请保证矩阵存在且已选中');
  if (state.isEditStage) {
    const editStage = state.missionPlanData[state.activeIndex].stages[state.selectedStageIndex];

    editStage.displayName = state.stageForm.stageDisplayName;
    editStage.name = state.stageForm.stageName;
    editStage.condition = state.stageForm.condition;
    editStage.conditionType = state.stageForm.conditionType;

    if (editStage.conditionType === 'time') {
      editStage.timeCondition = state.stageForm.timeCondition;
      editStage.timeUnit = state.stageForm.timeUnit;
    } else if (editStage.conditionType === 'event') {
      editStage.eventParamValue = state.stageForm.eventParamValue;
    }
  } else {
    if (state.stageForm.stageDisplayName && state.stageForm.stageName) {
      //添加的新阶段也需要有实体数据（空的任务）
      const entityInfo = JSON.stringify(entityData.value?.map(i => ({...i, task: []})) || []);
      // 获取当前的阶段列表
      const selectStages = state.missionPlanData[state.activeIndex].stages;
      // 添加的阶段数据
      const addStage = {
        displayName: state.stageForm.stageDisplayName,
        name: state.stageForm.stageName,
        condition: state.stageForm.condition,
        conditionType: state.stageForm.conditionType,
        entityInfo,
      };
      // 判断是否为时间和事件
      if (addStage.conditionType === 'time') {
        addStage.timeCondition = state.stageForm.timeCondition;
        addStage.timeUnit = state.stageForm.timeUnit;
      } else if (addStage.conditionType === 'event') {
        addStage.eventParamValue = state.stageForm.eventParamValue;
      }
      // 阶段不为空且已选中阶段
      const flag = selectStages.length > 0 && state.selectedStageIndex !== null;
      // 左添加
      if (state.addPosition === 'left' && flag) {
        // 如果选择的索引为0 第一位添加
        if (state.selectedStageIndex - 1 < 0) {
          selectStages.unshift(addStage);
        } else {
          selectStages.splice(state.selectedStageIndex, 0, addStage);
        }
      } else if (state.addPosition === 'right' && flag) {
        //右添加
        selectStages.splice(state.selectedStageIndex + 1, 0, addStage);
      } else {
        selectStages.push(addStage);
      }
    }
  }
  processMissionPlanData();

  state.stageForm.stageDisplayName = '';
  state.stageForm.stageName = '';
  state.stageForm.conditionType = '';
  state.stageForm.condition = '';
  state.stageVisible = false;
};
const copyStage = () => {
  if (state.selectedStageIndex === null) return ElMessage.warning('请先选择阶段');
  // 获取当前的阶段列表
  const selectStages = state.missionPlanData[state.activeIndex].stages;
  // 添加的阶段数据
  const copyTarget = JSON.parse(JSON.stringify(state.missionPlanData[state.activeIndex].stages[state.selectedStageIndex]));
  copyTarget.displayName = copyTarget.displayName + `_副本`;
  copyTarget.name = copyTarget.name + `_copy`;
  //右添加
  selectStages.splice(state.selectedStageIndex + 1, 0, copyTarget);
};
const moveStage = flag => {
  if (state.selectedStageIndex === null) return ElMessage.warning('请先选择阶段');
  // 获取当前的阶段列表
  const selectStages = state.missionPlanData[state.activeIndex].stages;
  if (flag === 'right') {
    if (state.selectedStageIndex === selectStages.length - 1) return ElMessage.warning('阶段已处于最右侧');

    const temp = selectStages[state.selectedStageIndex + 1];
    selectStages[state.selectedStageIndex + 1] = selectStages[state.selectedStageIndex];
    selectStages[state.selectedStageIndex] = temp;
    state.selectedStageIndex += 1;
  } else {
    if (state.selectedStageIndex === 0) return ElMessage.warning('阶段已处于最左侧');
    const temp = selectStages[state.selectedStageIndex - 1];
    selectStages[state.selectedStageIndex - 1] = selectStages[state.selectedStageIndex];
    selectStages[state.selectedStageIndex] = temp;
    state.selectedStageIndex -= 1;
  }
};

//删除阶段
const deleteStage = () => {
  if (state.selectedStageIndex === null) return ElMessage.warning('请先选择阶段');
  ElMessageBox.confirm('确定要删除该阶段?', {closeOnClickModal: false})
    .then(() => {
      state.missionPlanData[state.activeIndex].stages.splice(state.selectedStageIndex, 1);
      state.selectedStageIndex = null;
      ElMessage.success('删除成功!');
      console.log(state.missionPlanData, stateControler.missionPlanData);
    })
    .catch(() => '');
};
//删除实体
const deleteEntity = entity => {
  ElMessageBox.confirm('确定要删除该实体?', {closeOnClickModal: false})
    .then(() => {
      //todo 删除所有阶段的该实体
      stages.value.map(item => {
        //删除所有阶段的该实体和绑定该实体的子平台
        item.entityInfo = item.entityInfo.filter(i => i.name !== entity.name && (!i.parentEntity || i.parentEntity !== entity.name));
      });
    })
    .catch(() => '');
};
//添加实体
const addEntity = async () => {
  if (!state.selectedEntity) return ElMessage.error('请先选择实体');
  if (stages.value.length === 0) return ElMessage.warning('请先添加阶段');
  //获取子实体
  const subPlatformData = await getSubPlatformData(state.selectedEntity);
  const entity = {
    platFormType: state.selectedEntity.name,
    name: state.selectedEntity.entityName,
    displayName: state.selectedEntity.entityDisplayName,
    entityId: state.selectedEntity.entityId,
    task: [],
  };

  // 检查任务重合(同一个实体是否在多个矩阵中存在)
  const allMatrixEntity = getAllMatrixEntity();
  const isHave = allMatrixEntity.find(i => i.entityId === entity.entityId);
  if (isHave) ElMessage.info('该实体已存在于其他任务矩阵');
  stages.value.forEach(i => {
    i.entityInfo.push(JSON.parse(JSON.stringify(entity)));
    if (subPlatformData) {
      subPlatformData.forEach(v => i.entityInfo.push(v));
    }
  });
  //todo 更新所有阶段的实体信息entityInfo
  state.selectedEntity = '';
};

// 改变为事件类型时添加数组
const changeConditionType = form => {
  if (form.conditionType === 'event') (form.eventParamValue = []), (form.condition = '');
};

//展示编辑阶段任务弹窗
const showEditStageTask = async (stageIndex, entity) => {
  const currEntity = state.entityList.find(i => i.entityId === entity.entityId);
  let platformTypeId = currEntity.platformTypeId;
  //获取到实体的数据
  if (entity.parentEntity) {
    //子平台的id获取任务
    const {data} = await getModelAPI(platformTypeId);
    platformTypeId = data.platformTypeId;
  }
  const tasks = await getTaskData(platformTypeId);
  if (!tasks) return ElMessage.warning('当前实体没有任务');
  // console.log(tasks, '可选任务列表');
  // console.log(entity.task, '已选任务');

  state.currentEntityTasks = tasks; //当前实体可选的任务列表
  state.stageTaskVisible = true;
  state.currentStageTasks = JSON.parse(JSON.stringify(entity.task)).filter(i => tasks.find(e => e.id === i.id));
  state.currentStageEntity = entity; //存起来当前实体任务，最后保存赋值
  state.stageTaskVisible = true;
  state.selectedStageIndex = stageIndex;
};
//编辑阶段任务
const editStageTask = () => {
  for (const item of state.currentStageTasks) {
    // 条件校验
    if (!item.conditionType) return ElMessage.warning('请输入触发类型');
    switch (item.conditionType) {
      case 'time':
        if (!item.condition) return ElMessage.warning('请输入触发条件');
        if (!item.timeUnit) return ElMessage.warning('请输入触发时间单位');
        if (!item.timeCondition) return ElMessage.warning('请输入触发条件');
        break;
      case 'event':
        if (!item.condition) return ElMessage.warning('请输入触发条件');
        for (const condition of item.eventParamValue) {
          if (!condition.value) {
            return ElMessage.warning(`请输入事件参数:${condition.displayName}`);
          }
        }
        break;
      default:
        break;
    }

    // 参数校验
    if (item.params && item.params.length > 0) {
      for (const element of item.params) {
        if (!element.value) return ElMessage.warning(`请输入${paramDisplay(element.id, 'name')}`);
      }
    }
  }

  // 保存赋值
  state.currentStageEntity.task = JSON.parse(JSON.stringify(state.currentStageTasks));
  state.stageTaskVisible = false;
  state.selectedTask = '';
};

//添加阶段任务
const addStageTasks = () => {
  if (state.selectedTask === '') return ElMessage.warning('请先选择阶段任务');
  const isHave = state.currentStageTasks.some(i => i.id === state.selectedTask.id);
  if (isHave) return ElMessage.warning('当前实体该阶段已存在此任务');
  // console.log(state.selectedTask, '选择的任务');
  // console.log(state.selectedTask.params, '参数数据---------------');
  //用于去重参数
  const uquineName = [];
  // 处理任务参数为界面可用
  state.selectedTask.params = state.selectedTask.params.map(item => {
    // 函数名不做处理
    if (item.defaultType !== 'struct') {
      // 如果参数存在不返回数据
      if (uquineName.includes(item.name)) return null;
      uquineName.push(item.name);
      return {id: item.id, name: item.name, value: item.value, defaultType: item.defaultType, templateParamId: item.templateParamId};
    }
    //参数
    else if (item.defaultType === 'struct' && item.value !== '' && JSON.parse(item.value)?.length > 0) {
      const param = {defaultType: 'struct'};
      const paramData = JSON.parse(item.value);
      const paramName = paramData.find(i => i.name === '参数名称').value;

      // 如果参数存在不返回数据
      if (uquineName.includes(paramName)) return null;
      uquineName.push(paramName);
      // console.log(paramData, '参数数据');
      paramData.forEach(i => {
        let defaultUnit;
        switch (i.name) {
          case '参数单位':
            if (!i.value || i.value === '') return;
            defaultUnit = getDefaultUnit(Number(i.value));
            param.unit = defaultUnit ? defaultUnit.name : '';
            break;
          default:
            break;
        }
        param.id = item.id;
        param.templateParamId = item.templateParamId;

        param.value = '';
      });
      return param;
    }
  });
  // 去除重复的参数（重复的参数为null）
  state.selectedTask.params = state.selectedTask.params.filter(i => i);
  console.log(state.selectedTask.params, '任务参数结果---------------');

  state.currentStageTasks.push({
    id: state.selectedTask.id,
    params: JSON.parse(JSON.stringify(state.selectedTask.params)),
  });
  // console.log(state.currentStageTasks, '选择任务列表');

  state.selectedTask = '';
};

const getTimeUnit = data => {
  const id = [...stateControler.unitConfig.values()].find(i => i.name === 'time').id;
  const unitList = stateControler.unitConfig.get(id).params;
  const defaultUnit = unitList.find(i => i.default);
  // 判断是否有默认值设置
  if (!data.timeUnit) data.timeUnit = defaultUnit ? defaultUnit.name : '';
  return stateControler.unitConfig.get(id).params;
};

const getSideColor = side => stateControler.entityListData.filter(i => i.side === side)[0]?.color;
// 获取到矩阵用到的所有实体
const getAllMatrixEntity = () => {
  const allMatrixEntity = state.missionPlanData
    .map(matrix => {
      if (matrix.stages.length > 0) {
        return matrix.stages[0].entityInfo.flat(2);
      }
    })
    .flat(2)
    .filter(i => i);
  return allMatrixEntity;
};
// 检查任务重合(同一个实体是否在多个矩阵中存在)
const checkData = () => {
  const allMatrixEntity = getAllMatrixEntity();
  //检查实体不在任务矩阵中（即没有进行任务规划的实体）
  const notInMatrixEntitys = state.entityList.filter(item => !allMatrixEntity.find(ele => ele.entityId === item.entityId)).map(i => i.entityDisplayName);
  console.log('没有进行任务规划的数据', notInMatrixEntitys);
  return notInMatrixEntitys;
};

defineExpose({missionPlanData: state.missionPlanData, checkData});
</script>

<template>
  <div class="mission-planning-dialog">
    <el-dialog v-model="dialogVisible" title="任务规划" :close-on-click-modal="false" center :before-close="() => emit('close')" top="5%" left="30%" width="1200">
      <div class="control">
        <el-button class="add-matrix" @click="showMatrixDialog()">创建矩阵</el-button>
        <el-button class="add-matrix" :disabled="!abled" @click="deleteMissionPlan">删除矩阵</el-button>
      </div>
      <div class="mission-planning">
        <!-- 矩阵列表 -->
        <div class="matrix-box">
          <div class="header">矩阵列表</div>
          <div v-for="(item, index) in state.missionPlanData" :key="index" class="matrix" @click="() => ((state.activeIndex = index), (state.selectedStageIndex = null))" @dblclick="showMatrixDialog(item)">
            <div :class="[state.activeIndex === index ? 'active ' : '', 'text-ellipsis']" :style="{color: getSideColor(item.side)}">
              <span :title="item.displayName">{{ item.displayName }}</span>
              <i v-if="state.activeIndex === index" title="编辑" class="icon edit-icon task-icon" @click="showMatrixDialog(item)"></i>
            </div>
          </div>
        </div>
        <div class="right-box">
          <!-- 操作按钮部分 -->
          <div class="header-control">
            <el-button class="add-matrix" :disabled="!abled" @click="showAddStage('left')">左侧插入阶段</el-button>
            <el-button class="add-matrix" :disabled="!abled" @click="showAddStage('right')">右侧插入阶段</el-button>
            <el-button class="add-matrix" :disabled="!abled" @click="copyStage">复制阶段</el-button>
            <el-button class="add-matrix" :disabled="!abled" @click="deleteStage">删除阶段</el-button>
            <el-button class="add-matrix" :disabled="!abled" @click="moveStage('left')">左移阶段</el-button>
            <el-button class="add-matrix" :disabled="!abled" @click="moveStage('right')">右移阶段</el-button>
          </div>
          <!-- 阶段展示 -->
          <div class="stage-box">
            <!-- 阶段 -->
            <div v-for="(item, index) in stages" :key="index" :class="[state.selectedStageIndex === index ? 'active-stage' : '', 'stages']">
              <h4 class="stage-header">
                <div v-if="index === 0" class="table-head">
                  <span class="t-entity">阶段</span>
                  <span class="t-stage">实体</span>
                </div>
                <div :class="['stage-title']" @click="state.selectedStageIndex = index">
                  <span class="stage-name text-ellipsis" :title="item.displayName">{{ item.displayName }}</span>

                  <i v-show="state.selectedStageIndex === index" title="编辑" class="icon edit-icon" @click="showEditStage(item)"></i>
                </div>
              </h4>
              <div class="stage-content">
                <!-- 实体名称 -->
                <div v-if="index === 0" class="entity-box">
                  <div v-for="entity in item.entityInfo" :key="entity.name" class="entity">
                    <span class="text-ellipsis" :title="entity.displayName">{{ entity.displayName }}</span>
                    <span class="text-ellipsis" :title="entity.platFormType">{{ entity.platFormType }}</span>

                    <i class="task-icon icon delete-icon" title="删除" @click.stop="deleteEntity(entity)"></i>
                  </div>
                  <!-- 最后一行添加实体 -->
                  <div class="entity">
                    <el-select v-model="state.selectedEntity" value-key="entityId" suffix-icon="CaretBottom" placeholder="添加实体" @change="addEntity">
                      <el-option v-for="ele in entityOptional" :key="ele.value" :label="ele.entityDisplayName" :value="ele"> </el-option>
                    </el-select>
                  </div>
                </div>
                <!-- 实体的任务 -->
                <div class="tasks">
                  <div v-for="entity in item.entityInfo" :key="entity.name" class="task-box">
                    <p class="text-ellipsis" :title="entity.task?.map(i => taskDisplayName(entity, i.id)).join('->')">{{ entity.task?.map(i => taskDisplayName(entity, i.id)).join('->') }}</p>

                    <i title="编辑" class="icon edit-icon task-icon" @click="showEditStageTask(index, entity)"></i>
                  </div>
                  <div class="task-box"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <IframeViewerTask :visible="props.missionPlanningVisible" />
    </el-dialog>
    <!-- 添加/编辑矩阵 -->
    <el-dialog v-model="state.matrixVisible" title="添加/编辑矩阵" :close-on-click-modal="false" destroy-on-close width="400px" center>
      <el-form ref="matrixFormDom" class="form-box" :model="state.matrixForm">
        <el-form-item label="矩阵名称" label-width="120px" prop="name" :rules="ValidationRules.value">
          <el-input v-model="state.matrixForm.name"></el-input>
        </el-form-item>
        <el-form-item label="矩阵显示名称" label-width="120px" prop="displayName" :rules="ValidationRules.value">
          <el-input v-model="state.matrixForm.displayName"></el-input>
        </el-form-item>
        <el-form-item label="矩阵阵营" label-width="120px" prop="side" :rules="ValidationRules.value">
          <el-select v-model="state.matrixForm.side" placeholder="请选择" :disabled="sideEdit && !state.isAddMatrix" suffix-icon="CaretBottom">
            <el-option v-for="(item, index) in stateControler.entityListData" :key="index" :label="item.title" :value="item.side"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="state.matrixVisible = false">取 消</el-button>
        <el-button type="primary" @click="addEditMatrix">确 定</el-button>
      </template>
    </el-dialog>

    <!-- 添加/编辑阶段 -->
    <el-dialog v-model="state.stageVisible" title="添加/编辑阶段" destroy-on-close :close-on-click-modal="false" width="500px" center>
      <el-form ref="stageFormDom" class="form-box" :model="state.stageForm">
        <el-form-item label="阶段名称" label-width="120px" prop="stageName" :rules="ValidationRules.value">
          <el-input v-model="state.stageForm.stageName"></el-input>
        </el-form-item>
        <el-form-item label="阶段显示名称" label-width="120px" prop="stageDisplayName" :rules="ValidationRules.value">
          <el-input v-model="state.stageForm.stageDisplayName"></el-input>
        </el-form-item>
        <el-form-item label="阶段触发类型" label-width="120px" prop="conditionType" :rules="ValidationRules.value">
          <el-select v-model="state.stageForm.conditionType" suffix-icon="CaretBottom" placeholder="请选择" @change="changeConditionType(state.stageForm)">
            <el-option v-for="(item, index) in taskState.taskTypes" :key="index" :label="item.name" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="state.stageForm.conditionType !== 'sequence'" label="阶段触发条件" label-width="120px">
          <p class="stage-condition-box">
            <span v-if="state.stageForm.conditionType === 'time'" class="time-box">
              <el-form-item prop="timeCondition" :rules="ValidationRules.value">
                <el-select v-model="state.stageForm.timeCondition" suffix-icon="CaretBottom" placeholder="请选择">
                  <el-option label="大于" value=">"> </el-option>
                  <el-option label="大于等于" value=">="> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="condition" :rules="ValidationRules.value">
                <el-input v-model="state.stageForm.condition" type="text" @input="state.stageForm.condition = inputChange(state.stageForm.condition)" @blur="state.stageForm.condition = onBlur(state.stageForm.condition)"></el-input>
              </el-form-item>
              <!-- 单位 -->
              <el-form-item prop="timeUnit" :rules="ValidationRules.value">
                <el-select v-model="state.stageForm.timeUnit" suffix-icon="CaretBottom" placeholder="请选择">
                  <el-option v-for="(item, index) in getTimeUnit(state.stageForm)" :key="index" :value="item.name"> {{ item.name + ' - ' + item.displayName }}</el-option>
                </el-select>
              </el-form-item>
            </span>
            <!-- 事件 -->
            <span v-if="state.stageForm.conditionType === 'event'" class="event-box">
              <el-form-item prop="condition" :rules="ValidationRules.value">
                <el-select v-model="state.stageForm.condition" suffix-icon="CaretBottom" placeholder="请选择" @change="state.stageForm.eventParamValue = taskState.eventList.find(i => i.name === state.stageForm.condition)?.params || []">
                  <el-option v-for="(v, i) in taskState.eventList" :key="i" :label="v.displayName" :value="v.name"> </el-option>
                </el-select>
              </el-form-item>
              <div class="event-param">
                <div v-for="(item, index) in state.stageForm.eventParamValue" :key="index">
                  <p>{{ item.displayName }}：</p>
                  <el-form-item :prop="`eventParamValue[${index}].value`" :rules="ValidationRules.value">
                    <el-input v-if="item.paramType !== 'platform'" v-model="item.value"></el-input>
                    <el-select v-else v-model="item.value" suffix-icon="CaretBottom">
                      <el-option v-for="ele in state.entityList" :key="ele.value" :label="ele.entityDisplayName" :value="ele.entityName"> </el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
            </span>
          </p>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="state.stageVisible = false">取 消</el-button>
        <el-button type="primary" @click="addEditStage">确 定</el-button>
      </template>
    </el-dialog>
    <!-- 阶段任务 -->
    <el-dialog v-model="state.stageTaskVisible" :title="`${state.currentStageEntity.displayName} — 阶段任务`" destroy-on-close :close-on-click-modal="false" width="800px" center>
      <div class="task-content">
        <div class="task-list">
          <div class="header">任务列表</div>
          <ul>
            <div v-for="(item, index) in state.currentEntityTasks" :key="index" :class="[state.selectedTask.name === item.name ? 'active' : '', 'task text-ellipsis']" :title="item.displayName" @click="state.selectedTask = JSON.parse(JSON.stringify(item))">
              {{ item.displayName }}
            </div>
          </ul>
          <div class="add-task-btn" @click="addStageTasks">
            <i class="icon add-icon"></i>
            添加
          </div>
        </div>
        <div class="stage-task-box">
          <div v-for="(item, index) in state.currentStageTasks" :key="item.name" class="stage-task">
            <h4 class="task-attr">
              <span class="task-label">任务名称:{{ taskDisplayName(state.currentStageEntity, item.id) }}</span>
              <div>
                <el-icon class="handle" :size="16" title="移动"><Rank /></el-icon>
                <el-icon :size="16" title="删除" @click="state.currentStageTasks.splice(index, 1)">
                  <Delete />
                </el-icon>
              </div>
            </h4>
            <div class="item-task-content">
              <!-- 触发条件 -->
              <div class="task-condition-box" title="触发">
                <p>
                  <b class="task-param-name">触发类型</b>
                  <el-select v-model="item.conditionType" placeholder="请选择" suffix-icon="CaretBottom" @change="item.condition = ''">
                    <el-option v-for="(v, i) in taskState.taskTypes" :key="i" :label="v.name" :value="v.value"> </el-option>
                  </el-select>
                </p>
                <p v-if="item.conditionType !== 'sequence'">
                  <b class="task-param-name">触发条件</b>
                  <span v-if="item.conditionType === 'time'" class="time-box">
                    <el-select v-model="item.timeCondition" placeholder="请选择" suffix-icon="CaretBottom">
                      <el-option label="大于" value=">"> </el-option>
                      <el-option label="大于等于" value=">="> </el-option>
                    </el-select>
                    <el-input v-model="item.condition" type="text" @input="item.condition = inputChange(item.condition)" @blur="item.condition = onBlur(item.condition)"></el-input>
                    <el-select v-model="item.timeUnit" suffix-icon="CaretBottom" placeholder="请选择">
                      <el-option v-for="(ele, idx) in getTimeUnit(item)" :key="idx" :value="ele.name">{{ ele.name + ' - ' + ele.displayName }}</el-option>
                    </el-select>
                  </span>
                  <!-- 事件 -->
                  <span v-if="item.conditionType === 'event'" class="event-box">
                    <el-select v-model="item.condition" suffix-icon="CaretBottom" placeholder="请选择" @change="item.eventParamValue = JSON.parse(JSON.stringify(taskState.eventList.find(i => i.name === item.condition)?.params)) || []">
                      <el-option v-for="(v, i) in taskState.eventList" :key="i" :label="v.displayName" :value="v.name"> </el-option>
                    </el-select>
                    <div class="event-param">
                      <div v-for="(val, idx) in item.eventParamValue" :key="idx">
                        <p>{{ val.displayName }}：</p>
                        <el-input v-if="val.paramType !== 'platform'" v-model="val.value"></el-input>
                        <el-select v-else v-model="val.value" suffix-icon="CaretBottom">
                          <el-option v-for="ele in state.entityList" :key="ele.value" :label="ele.entityDisplayName" :value="ele.entityName"> </el-option>
                        </el-select>
                      </div>
                    </div>
                  </span>
                </p>
              </div>

              <!-- 判断参数是否存在 -->
              <div v-if="item.params && item.params.length > 1" class="task-params-box" title="参数">
                <!-- 遍历参数 -->
                <div v-for="(ele, idx) in item.params" :key="idx" class="stage-task-params">
                  <div v-if="paramDisplay(ele.id, 'defaultType') === 'struct'" class="params-box">
                    <!-- 参数名称/参数值 -->
                    <b class="task-param-name text-ellipsis" :title="paramDisplay(ele.id, 'name')">{{ paramDisplay(ele.id, 'name') }}:</b>
                    <el-input v-if="paramDisplay(ele.id, 'type') !== 'platform'" v-model="ele.value"></el-input>
                    <el-select v-if="paramDisplay(ele.id, 'type') === 'platform'" v-model="ele.value" suffix-icon="CaretBottom" placeholder="请选择">
                      <el-option v-for="v in state.entityList" :key="v.value" :label="v.entityDisplayName" :value="v.entityName"> </el-option>
                    </el-select>
                    <!-- 参数单位 -->
                    <div v-if="paramDisplay(ele.id, 'unit')" class="param-unit">
                      <el-select v-model="ele.unit" placeholder="请选择" suffix-icon="CaretBottom">
                        <el-option v-for="(e, ix) in stateControler.unitConfig.get(paramDisplay(ele.id, 'unit')).params ?? []" :key="ix" :value="e.name">{{ e.name + ' - ' + e.displayName }}</el-option>
                      </el-select>
                    </div>
                  </div>
                  <div v-else-if="paramDisplay(ele.id, 'defaultType') !== 'script' && ele.name !== 'FunName'" class="params">
                    <b class="task-param-name text-ellipsis" :title="paramDisplay(ele.id, 'name')">{{ paramDisplay(ele.id, 'name') }}: </b><el-input v-model="ele.value"></el-input>
                  </div>
                  <!-- <div v-else>暂无可用参数</div> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="state.stageTaskVisible = false">取 消</el-button>
        <el-button type="primary" @click="editStageTask">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less">
.mission-planning {
  display: flex;
  height: 645px;
  margin-bottom: 5px;
}

.el-icon {
  margin: 0 5px;
  font-size: 21px;
  color: var(--border-color);
  cursor: pointer;
}

.right-box {
  height: 100%;
  overflow: auto;
  border: 1px solid #2b8ada;
  flex: 1;

  .task-icon {
    color: #36b6ff;
    visibility: hidden;
  }

  .header-control {
    display: flex;
    height: 33px;
    padding-left: 5px;
    background: #0e4f75;
    border-bottom: 1px solid #2b8ada;
    align-items: center;
  }

  .stage-box {
    display: flex;
    max-height: calc(100% - 55px);
    padding-bottom: 5px;
    margin: 5px;
    overflow: auto;
    color: #fff;
    box-sizing: border-box;

    .stages {
      height: 100%;
      min-width: 185px;
      overflow: hidden;
      border: 1px solid #2b8ada;
      border-right: 0;
      flex: 1;
    }

    .stages:first-child {
      flex: 1.5;
      min-width: 440px;
    }

    .stages:last-child {
      border: 1px solid #2b8ada;
    }

    .stage-header {
      display: flex;
      height: 40px;
      font-size: 16px;
      background: var(--title-bg-color);
      justify-content: space-between;
      align-items: center;

      .stage-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;

        .edit-icon {
          margin-right: 10px;
        }

        .stage-name {
          font-size: 15px;
          font-weight: bold;
          color: #36b6ff;
          text-align: center;
          flex: 1;
        }
      }

      .table-head {
        position: relative;
        width: 258px;
        height: 100%;
        min-width: 258px;
        padding: 5px;
        font-size: 15px;
        color: #36b6ff;

        > span {
          position: absolute;
        }

        .t-entity {
          top: 0;
          right: 10px;
        }

        .t-stage {
          bottom: 0;
          left: 5px;
        }
      }

      .table-head ~ .stage-title {
        border-left: 1px solid #2b8ada;
      }

      .table-head::before {
        position: absolute;
        top: 18px;
        left: -30px;
        width: 112%;
        height: 1px;
        background-color: #36b6ff;
        content: '';
        transform: rotate(8deg);
      }
    }

    .stage-header:hover {
      cursor: pointer;
    }

    .stage-content {
      display: flex;
      height: calc(100% - 40px);

      .tasks {
        flex: 1;
        overflow: hidden;
      }
    }

    .entity-box {
      width: 259px;
      height: 100%;
      border-right: 1px solid #2b8ada;

      .entity {
        display: flex;
        height: 40px;
        padding: 0 10px;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #2b8ada;
        font-size: 14px;

        .el-select {
          width: 100%;
        }

        > span {
          flex: 2;
          width: 120px;
          height: 39px;
          padding: 0 5px;
          line-height: 39px;
        }

        > span:first-child {
          flex: 3;
          border-right: 1px solid #2b8ada;
        }
      }

      .entity:nth-child(even) {
        background: #053c5c;
      }

      .entity:last-child {
        border-bottom: none;
      }

      .entity:hover {
        cursor: pointer;

        .task-icon {
          visibility: visible;
        }
      }
    }

    .task-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      padding: 0 10px;
      font-size: 15px;
      border-bottom: 1px solid #2b8ada;

      p {
        font-size: 14px;
        text-align: center;
        flex: 1;
      }
    }

    .task-box:nth-child(even) {
      background: #053c5c;
    }

    .task-box:last-child {
      border-bottom: none;
    }

    .task-box:hover {
      cursor: pointer;

      /* stylelint-disable-next-line no-descending-specificity */
      .task-icon {
        visibility: visible;
      }
    }
  }
}

.matrix-box {
  position: relative;
  width: 245px;
  margin-right: 5px;
  border: 1px solid #2b8ada;
  box-sizing: border-box;

  .matrix {
    height: 30px;
    margin: 2px;
    overflow: hidden;
    line-height: 30px;
    color: #fff;
    background: #083e5f;
    border: 1px solid #196a99;
  }

  .header {
    height: 35px;
    padding-left: 20px;
    font-size: 15px;
    font-weight: bold;
    line-height: 35px;
    color: #fff;
    background: url('@/assets/images/title-bg.png') no-repeat 0 0 / 100% 100%;
  }

  .matrix > div {
    padding: 0 5px;
    text-align: center;
  }

  .matrix:hover {
    background: #015788;
  }
}

.event-box {
  flex-direction: column;

  .el-form-item {
    width: 100%;
  }

  .event-param > div {
    margin: 5px 0;
    margin-left: 0 !important;
  }
}

.task-content {
  display: flex;
  height: 450px;
  color: #fff;

  /* stylelint-disable-next-line no-descending-specificity */
  .el-select {
    width: 100%;
  }

  .header {
    height: 30px;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;
    line-height: 30px;
    color: #fff;
    background: url('@/assets/images/title-bg.png') no-repeat 0 0 / 100% 100%;
  }

  .task-list {
    height: 100%;
    border: 1px solid #2b8ada;

    ul {
      width: 130px;
      height: calc(100% - 60px);
      overflow: auto;
    }

    .task {
      height: 30px;
      padding: 0 10px;
      line-height: 30px;
      text-align: center;
      border-bottom: 1px solid #2b8ada;
    }

    .add-task-btn {
      display: flex;
      height: 30px;
      align-items: center;
      border-top: 1px solid #2b8ada;
      justify-content: center;
      cursor: pointer;
    }

    .task:hover {
      background: #043b7bf0;
      cursor: pointer;
    }
  }

  .stage-task-box {
    height: 100%;
    margin-bottom: 30px;
    margin-left: 5px;
    overflow: auto;
    border: 1px solid #2b8ada;
    flex: 1;

    .task-attr {
      display: flex;
      height: 32px;
      padding: 5px;
      font-size: 15px;
      color: #5ec5ff;
      background: var(--title-bg-color);
      align-items: center;
      justify-content: space-between;

      > div {
        margin-top: 8px;
      }

      .el-icon {
        font-size: 21px !important;
      }
    }
  }

  .item-task-content {
    padding: 5px;

    .task-condition-box,
    .task-params-box {
      position: relative;
      padding: 5px;
      margin: 10px 5px;
      border: 1px solid #217fb5;
      border-radius: 2px;
    }

    .task-condition-box {
      p {
        display: flex;
        margin: 5px 0;
        align-items: center;

        b {
          width: 80px;
        }
      }
    }

    .task-condition-box::before {
      position: absolute;
      padding: 0 5px;
      font-size: 15px;
      color: #36b6ff;
      background: var(--panel-bg-color);
      content: attr(title);
      transform: translate(10px, -16px);
    }

    .task-params-box {
      .stage-task-params {
        margin: 5px;
      }

      .el-select .el-input {
        height: 30px;
      }

      .params-box {
        display: flex;
        color: #fff;
        align-items: center;
      }

      .params,
      .param-unit {
        display: flex;
        min-width: 120px;
        margin-left: 5px;
      }

      .task-param-name {
        min-width: 80px;
        margin-right: 5px;
        color: #fff;
        text-align: right;
      }
    }

    .task-params-box::before {
      position: absolute;
      padding: 0 5px;
      font-size: 15px;
      color: #36b6ff;
      background: var(--panel-bg-color);
      content: attr(title);
      transform: translate(10px, -16px);
    }
  }

  .task-tag {
    margin: 0 5px;
  }
}

.time-box,
.event-box {
  display: flex;
  width: 100%;
  align-items: center;
  color: #fff;

  > div {
    flex: 1;
  }

  /* stylelint-disable-next-line no-descending-specificity */
  > span {
    width: 150px;
    margin-left: 5px;
  }

  > :not(:first-child) {
    margin-left: 5px;
  }
}

.control {
  display: flex;
  height: 35px;
  padding: 2px;
  margin-bottom: 5px;
  background: url('@/assets/images/title-bg.png') no-repeat 0 0 / 100% 100%;
  border-bottom: none;
  align-items: center;
}

.add-matrix {
  width: 116px;
}

.active {
  position: relative;
  background: #015788;

  .edit-icon {
    position: absolute;
    top: 3px;
    right: 5px;
  }
}

.stages:not(:first-child).active-stage {
  border: 2px solid #36b6ff !important;
}

.stages:first-child.active-stage {
  .tasks {
    border: 1px solid #36b6ff;
    border-top: 0;
  }

  .table-head ~ .stage-title {
    border-left: 2px solid #36b6ff;
    border-right: 1px solid #36b6ff;
    border-top: 1px solid #36b6ff;
  }
}

.el-table .el-table__cell {
  padding: 0 !important;
}

.el-table th.el-table__cell > .cell {
  width: auto !important;
}

.el-table--border th.el-table__cell,
.el-table__fixed-right-patch {
  border: 0;
}

.form-box {
  margin: 20px 0 0;
}

.dialog-footer {
  position: relative;
  top: -55px;
}

.stage-condition-box,
.event-box {
  width: 100%;

  .event-param {
    width: 100%;
  }
}
</style>
