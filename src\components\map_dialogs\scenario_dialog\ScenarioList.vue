<script setup>
import {isJsonString} from '@/utils/tools';
import {getScenarioListAPI, deletScenarioAPI, getMissionPlanListApi, getScenarioInfoAPI, exportScenarioScriptAPI, copyScenarioAPI, getLocalFileApi} from '@/api/index';
import {reactive, computed, watch} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {Base64} from 'js-base64';
import {storeToRefs} from 'pinia';
import {isBlockParam2} from '@/utils/data_process.js';
const stateControler = useStateStore();

const {mapInstance} = storeToRefs(useMapStore());

const emit = defineEmits(['clearData', 'clearFormData', 'openScenario', 'close']);
const props = defineProps({
  taskListVisible: {
    type: Boolean,
    default: false,
  },
});
const state = reactive({
  currentPage: 1, //当前页
  pageSize: 10, //每页
  count: 0, //任务条数
  tableData: [], //仿真想定列表数据
  searchString: '',
  asc: false, //想定列表查询升序
  orderBy: null, //想定排序的依据字段 ct,ut
  startDate: '',
  endDate: '',
  scenarioForm: '',
});
const dialogVisible = computed(() => {
  return props.taskListVisible;
});
watch(dialogVisible, val => {
  if (val) {
    getScenarioList();
  } else {
    state.searchString = '';
    state.startDate = '';
  }
});
const search = async () => {
  try {
    state.currentPage = 1;
    const param = {
      pageNum: state.currentPage,
      pageSize: state.pageSize,
      s: state.searchString || undefined,
      orderBy: state.orderBy,
      asc: state.asc,
    };
    if (state.startDate && state.endDate) {
      const dateRange = JSON.stringify([state.startDate, state.endDate]);
      param.ct = dateRange;
    }

    const {data} = await getScenarioListAPI(param);

    state.count = data.count;
    state.tableData = data.list;
  } catch (error) {
    console.error('搜索失败:', error);
  }
};
const sortTable = column => {
  if (column.prop === 'updateTime' && column.order) {
    state.orderBy = 'ut';
  } else if (column.prop === 'createTime' && column.order) {
    state.orderBy = 'ct';
  } else {
    state.orderBy = null;
  }
  column.order === 'ascending' ? (state.asc = true) : (state.asc = false);
  search();
};

// 获取想定列表
const getScenarioList = async () => {
  const {data} = await getScenarioListAPI({
    pageIndex: state.currentPage,
    pageSize: state.pageSize,
  });
  if (data) {
    state.count = data.count;
    state.tableData = data.list;
  }
};
// 点击删除想定按钮
const handleScenarioDelet = (index, row) => {
  ElMessageBox.confirm('确定删除该想定?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    closeOnClickModal: false,
  })
    .then(async () => {
      const {code} = await deletScenarioAPI(row.id);
      if (code === 200) {
        ElMessage.success('删除成功');
        // 重新加载想定列表
        getScenarioList();
        // 清空处理
        emit('clearData');
      }
    })
    .catch(() => ElMessage.info('已取消'));
};
// 选择每页条数
const handleSizeChange = val => {
  state.pageSize = val;
  getScenarioList();
};
// 选择跳转页
const handleCurrentChange = val => {
  state.currentPage = val;
  getScenarioList();
};
// 编辑或查看想定
const handleScenarioEdit = row => {
  if (row.type == 2) return;
  emit('clearFormData', false);
  ElMessageBox.confirm('确定打开该想定?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    closeOnClickModal: false,
  })
    .then(() => {
      // 清空处理
      emit('clearData');
      // 获取想定数据
      getScenario(row.id);
      // !保存想定列表数据处理
      state.scenarioForm = {
        name: row.name,
        description: row.description,
      };
    })
    .catch(() => '');
};
// 打开想定请求
const getScenario = async scenarioId => {
  const res = await getScenarioInfoAPI(scenarioId);
  const {mapInfo, models, cmdChain, startTime, endTime, routes, environment, zones, globalScripts, plans, environments, routesNetworks, terrain, customScriptFileId, networks} = res.data;
  //!地图geojson的数据
  if (!isJsonString(mapInfo)) return ElMessage.error('该想定geojson数据错误');
  const geoJson = JSON.parse(mapInfo);
  const chainCommandRes = JSON.parse(cmdChain);
  if (chainCommandRes.map(i => i.content).flat(Infinity).length > 0) {
    stateControler.commandChainData = chainCommandRes;
  }
  // 将draw控件数据清空
  mapInstance.value.graphicalGeojson.features = [];

  // 存储model的params 和modules
  for (const item of models) {
    for (const entityParam of item.params) {
      // script处理
      if (entityParam.defaultType === 'script' && entityParam.value) {
        const {data} = await getLocalFileApi(entityParam.value);
        entityParam.value = Base64.decode(data.data);
      }
      // 块级参数处理（库中存储为json，需转化为object）
      if (isBlockParam2(entityParam)) {
        entityParam.params.forEach(param => {
          param.value = typeof param.value !== 'string' ? JSON.stringify(param.value) : param.value;
        });
      }
    }
    // 实体的组件参数处理
    if (item.modules && item.modules.length > 0) {
      for (const module of item.modules) {
        if (module.params) {
          module.params.forEach(item => {
            if (item.defaultType === 'struct' && item.value) {
              item.configureJson = JSON.parse(item.configureJson);
              item.value = JSON.parse(item.value);
            }
            if (item.defaultType === 'enum' && item.value) {
              item.configureJson = JSON.parse(item.configureJson);
            }
            // 块级参数处理
            if (isBlockParam2(item)) {
              item.params.forEach(param => {
                param.configureJson = typeof param.configureJson !== 'string' ? JSON.stringify(param.configureJson) : param.configureJson;
                param.value = typeof param.value !== 'string' ? JSON.stringify(param.value) : param.value;
              });
            }
          });
        }
      }
    }
    // console.log('🚀 ~ getScenario ~ item:', item);
    stateControler.entityModPar.set(item.id, {
      modules: item.modules,
      params: item.params,
    });

    for (const feature of geoJson.features) {
      if (feature.properties.id === item.id) {
        if (item.icon) {
          feature.properties.iconId = item.icon;
        } else feature.properties.iconId = '6200';
      }
    }
  }

  //todo 存储当前打开想定的所有数据传递到父组件操作
  // state.scenarioForm.startTime = startTime;
  stateControler.startTime = startTime;
  state.scenarioForm.scenarioId = scenarioId;
  state.scenarioForm.geoJson = geoJson;
  state.scenarioForm.endTime = endTime;
  state.scenarioForm.routes = routes;
  state.scenarioForm.zones = zones;
  state.scenarioForm.environment = environment;
  state.scenarioForm.globalScripts = globalScripts;
  state.scenarioForm.customScriptFileId = customScriptFileId;
  state.scenarioForm.networks = networks;
  // 环境及路网\地形回显
  stateControler.weatherCheckList = environments;
  stateControler.routesNetworks = routesNetworks;
  stateControler.terrain = terrain ? [terrain] : [];
  emit('openScenario', state.scenarioForm);
  // 获取任务规划数据
  stateControler.missionPlanData = plans ?? [];

  ElMessage.success('打开成功!');
};
//导出脚本
const exportScript = async data => {
  const res = await exportScenarioScriptAPI(data.id);
  const blob = new Blob([res], {type: 'application/zip'});
  // eslint-disable-next-line node/no-unsupported-features/node-builtins
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.download = data.name + '.zip';
  a.href = url;
  a.click();
  // eslint-disable-next-line node/no-unsupported-features/node-builtins
  URL.revokeObjectURL(url);
};
//复制想定
const copyScenario = async row => {
  if (row.type == 2) return;
  await copyScenarioAPI(row.id);
  ElMessage.success('复制成功');
  await getScenarioList();
};
</script>

<template>
  <div>
    <!--想定列表-->
    <el-dialog v-model="dialogVisible" title="仿真想定列表" center class="scenario-list" :close-on-click-modal="false" :before-close="() => emit('close')" width="1338" top="12vh" draggable>
      <div class="search">
        <el-input v-model="state.searchString" clearable placeholder="请输入ID/名称" prefix-icon="el-icon-search"> </el-input>
        <!-- <el-date-picker v-model="state.startDate" unlink-panels type="daterange" placeholder="选择创建时间" value-format="YYYY-MM-DD" format="YYYY/MM/DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"> </el-date-picker> -->

        <el-date-picker v-model="state.startDate" unlink-panels type="date" placeholder="开始日期" value-format="YYYY-MM-DD" format="YYYY/MM/DD"> </el-date-picker>
        <span>至</span>
        <el-date-picker v-model="state.endDate" unlink-panels type="date" placeholder="结束日期" value-format="YYYY-MM-DD" format="YYYY/MM/DD"> </el-date-picker>

        <el-button @click="search"><i class="icon"></i> 搜索</el-button>
      </div>

      <el-table :data="state.tableData" border height="405" highlight-current-row stripe @sort-change="sortTable" @row-dblclick="handleScenarioEdit">
        <!-- <el-table-column align="center" type="index" label="序号" width="60">
          <template #default="scope">
            <span>{{ scope.$index + 1 + (state.currentPage - 1) * state.pageSize }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="想定ID" prop="id" align="center" width="98"> </el-table-column>
        <el-table-column label="仿真想定名称" prop="name" align="center" width="230" show-overflow-tooltip></el-table-column>
        <el-table-column label="仿真想定描述" prop="description" align="center" width="233" show-overflow-tooltip> </el-table-column>
        <el-table-column sortable label="创建时间" prop="createTime" align="center" width="233"> </el-table-column>
        <el-table-column sortable label="更新时间" prop="updateTime" align="center" width="233"> </el-table-column>
        <el-table-column align="center" label="操作" width="298">
          <template #default="scope">
            <div class="ctrl-box">
              <span title="编辑" :class="scope.row.type == 2 ? 'ctrl-btn disable' : 'ctrl-btn'" :size="18" @click.stop="handleScenarioEdit(scope.row)">
                <i class="icon edit-icon"></i>
                <span>编辑</span>
              </span>
              <span title="导出" class="ctrl-btn" :size="18" @click.stop="exportScript(scope.row)">
                <i class="icon export-icon"></i>
                <span>导出</span>
              </span>
              <span title="复制" :class="scope.row.type == 2 ? 'ctrl-btn disable' : 'ctrl-btn'" :size="18" @click.stop="copyScenario(scope.row)">
                <i class="icon copy-icon"></i>
                <span>复制</span>
              </span>
              <!-- <el-button size="small" @click.stop="handleScenarioEdit(scope.$index, scope.row, 0)"
              >查看
            </el-button> -->
              <span title="删除" class="ctrl-btn" :size="18" @click.stop="handleScenarioDelet(scope.$index, scope.row)">
                <i class="icon delete-icon"></i>
                <span>删除</span>
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-box">
        <el-pagination v-model:current-page="state.currentPage" :page-sizes="[5, 10, 15, 20]" :page-size="state.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="state.count" @size-change="handleSizeChange" @current-change="handleCurrentChange"> </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.scenario-list {
  width: 636px !important;

  .search {
    display: flex;
    width: 100%;
    margin-bottom: 10px;
    color: #fff;
    align-items: center;
    justify-content: space-between;

    :deep(.el-input) {
      flex: 1;
      height: 29px !important;
    }

    :deep(.el-date-editor) {
      flex: 1;
      height: auto;
      margin: 0 5px;
      color: #fff;
    }

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 10px;
      background: url('@/assets/images/serach.png') no-repeat;
    }
  }

  .disabled {
    color: #999 !important;
    background-color: #ccc !important;
  }

  .disabled:hover {
    color: #000;
  }

  .ctrl-box {
    display: flex;
    align-items: center;
  }

  .ctrl-btn {
    display: flex;
    align-items: center;
    margin: 0 5px !important;
    color: var(--font-color);
    cursor: pointer;

    span {
      margin-top: 2px;
    }
  }

  .disable {
    color: #ccc;
    cursor: not-allowed;
  }

  .pagination-box {
    height: 50px;
    border-top: var(--border);
  }
}

:deep(.scenario-list .el-table thead) {
  height: 30px;
  font-size: 15px !important;
}

:deep(.scenario-list .el-table .el-table__row) {
  height: 37px;
}
</style>
