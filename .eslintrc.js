/*
 * @Author: 老范
 * @Date: 2023-05-10 10:09:31
 * @LastEditors: 老范
 * @LastEditTime: 2024-09-02 14:44:27
 * @Description: 请填写简介
 */
// eslint-disable-next-line node/no-unpublished-require
const {defineConfig} = require('eslint-define-config');
module.exports = defineConfig({
  extends: ['eslint:recommended', 'plugin:node/recommended', 'plugin:vue/vue3-recommended', 'plugin:@typescript-eslint/recommended', 'prettier'],
  parser: '@typescript-eslint/parser',
  plugins: ['node', 'prettier', 'vue', '@typescript-eslint'],
  rules: {
    'prettier/prettier': 'error',
    'block-scoped-var': 'error',
    eqeqeq: 'error',
    'no-var': 'error',
    'prefer-const': 'warn',
    'eol-last': 'error',
    'prefer-arrow-callback': 'error',
    'no-trailing-spaces': 'error',
    'no-restricted-properties': [
      'error',
      {
        object: 'describe',
        property: 'only',
      },
      {
        object: 'it',
        property: 'only',
      },
    ],
    '@typescript-eslint/no-this-alias': ['off'],
    '@typescript-eslint/no-non-null-assertion': 'off',
    '@typescript-eslint/no-use-before-define': 'off',
    '@typescript-eslint/no-warning-comments': 'off',
    '@typescript-eslint/no-empty-function': 'error',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/camelcase': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    'node/no-missing-import': 'off',
    'node/no-empty-function': 'off',
    'node/no-unsupported-features/es-syntax': 'off',
    'node/no-missing-require': 'off',
    'node/shebang': 'off',
    'no-dupe-class-members': 'off',
    'require-atomic-updates': 'off',
    'node/no-unpublished-import': 'off',
  },
  overrides: [
    {
      files: ['**/*.vue', '**/*.ts'],
      parser: 'vue-eslint-parser',
      extends: ['eslint:recommended', 'plugin:node/recommended', 'plugin:vue/vue3-recommended', 'plugin:@typescript-eslint/recommended', 'prettier'],
      plugins: ['node', 'prettier', 'vue', '@typescript-eslint', 'html'],
      rules: {
        'prettier/prettier': 'error',
        'block-scoped-var': 'error',
        eqeqeq: 'error',
        'no-var': 'error',
        'prefer-const': 'warn',
        'eol-last': 'error',
        'prefer-arrow-callback': 'error',
        'no-trailing-spaces': 'error',
        'no-restricted-properties': [
          'error',
          {
            object: 'describe',
            property: 'only',
          },
          {
            object: 'it',
            property: 'only',
          },
        ],
        '@typescript-eslint/no-this-alias': ['off'],
        '@typescript-eslint/no-non-null-assertion': 'off',
        '@typescript-eslint/no-use-before-define': 'off',
        '@typescript-eslint/no-warning-comments': 'off',
        '@typescript-eslint/no-empty-function': 'warn',
        '@typescript-eslint/no-var-requires': 'off',
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/explicit-module-boundary-types': 'off',
        '@typescript-eslint/ban-types': 'off',
        '@typescript-eslint/camelcase': 'off',
        'node/no-missing-import': 'off',
        'node/no-empty-function': 'off',
        'node/no-unsupported-features/es-syntax': 'off',
        'node/no-missing-require': 'off',
        'node/shebang': 'off',
        'no-dupe-class-members': 'off',
        'require-atomic-updates': 'off',
        'node/no-unpublished-import': 'off',
      },
      parserOptions: {
        parser: {
          js: 'espree',
          ts: '@typescript-eslint/parser',
          '<template>': 'espree',
        },
        sourceType: 'module',
        ecmaVersion: 2020,
      },
      globals: {
        // 全局变量忽略检测
        globalConfig: 'readonly',
        Plotly: 'readonly',
        G2Plot: 'readonly',
        envValueMap: 'readonly',
        paramsMap: 'readonly',
        configMap: 'readonly',
        satellite: 'readonly',
      },
    },
  ],
});
