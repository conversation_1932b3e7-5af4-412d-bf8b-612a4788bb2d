import pinia from '@/pinia/index';
import {useStateStore} from '@/pinia/state/index';

import {ElMessage} from 'element-plus';
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import {CircleMode, DragCircleMode, DirectMode, SimpleSelectMode} from 'mapbox-gl-draw-circle';
import DrawRectangle from 'mapbox-gl-draw-rectangle-restrict-area';

import {throttle} from '@/utils/tools.js';
import * as turf from '@turf/turf';
import {addMapLayer} from '@/modules/mapLayerControler';

const Measure = require('../utils/draw/measure');

const stateControler = useStateStore(pinia);

const url = globalConfig.MapUrl + configMap.InitMap;
MapboxDraw.constants.classes.CONTROL_BASE = 'maplibregl-ctrl';
MapboxDraw.constants.classes.CONTROL_PREFIX = 'maplibregl-ctrl-';
MapboxDraw.constants.classes.CONTROL_GROUP = 'maplibregl-ctrl-group';
//生成创建draw控件时中style 所用的paint(draw控件的颜色生成)
const getFillColor = () => {
  const judgingResults = ['case', ['boolean', ['has', 'user_color'], false], ['get', 'user_color'], '#ffffff'];
  return judgingResults;
};
const getOutlineColor = () => {
  const judgingResults = ['case', ['boolean', ['has', 'user_outline_color'], false], ['get', 'user_outline_color'], '#ffffff'];
  return judgingResults;
};

export default class Maplibre {
  constructor() {
    //* 地图实例
    this.map;
    //* draw控件实例
    this.drawInstance;
    //* 地图弹出框
    this.mapPop;
    this.canvas;
    this.clickEntityIndex; //点击并高亮的实体在geojson中的index
    this.highLightId = 0; //当前高亮的实体id

    //* 全局geojson
    this.geojson = {
      type: 'FeatureCollection',
      features: [],
    };
    // 鼠标移动所经过的经纬度列表
    this.pointArr = [];
    //* 点线面（图形）的geojson
    this.graphicalGeojson = {
      type: 'FeatureCollection',
      features: [],
    };
    //* 图标高亮元素geojson+
    this.clickGeojson = {
      type: 'FeatureCollection',
      features: [],
    };
    //* 当前点击的实体的feature
    this.featureClick = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [],
      },
      id: 0,
    };
    //*图标高亮元素的geojson
    this.clickGeojson = {
      type: 'FeatureCollection',
      features: [],
    };
    //*当前移动的实体数据
    this.currentEntity = {};
    //*有地理位置的实体配置弹窗dom
    this.entityConfigDom = '';
    this.ctrlKey = false;
    this.mapDrawState = '';
    this.currentModelId = '';
    this.selectFeatures = []; //多选的实体
    this.isDragging = false; //多选状态
    this.dragStartPoint = null; //拖动起点
    this.currentOffset = [0, 0]; //移动偏移量
  }
  //初始化地图
  initMap(mapContainer, isPreview = false) {
    // 创建地图
    this.map = new maplibregl.Map({
      container: mapContainer,
      style: `${url}`,
      center: [104, 35],
      zoom: 2.3,
      minZoom: 2.3,
      pitchWithRotate: false, //倾斜
      antialias: false,
      dragRotate: false, //拖动旋转
    });
    const map = this.map;
    // 阻止地图默认双击事件
    map.doubleClickZoom.disable();
    // map.dragPan.disable();
    map.dragRotate.disable();
    //想定预演不生成控件
    if (isPreview) return;
    // 创建比例尺
    map.addControl(this.creatScale()); //addControl添加控件
    // draw控件实例化
    this.drawInstance = this.creatDraw();
    this.map.addControl(this.drawInstance);
    this.initMapEvents();
    this.registerMapClick();
    this.registerDeleteEvent();
  }
  // 初始化地图事件
  initMapEvents() {
    const that = this;
    const map = this.map;
    that.canvas = map.getCanvasContainer();
    // 点击图标弹出参数,有地理位置实体参数配置弹窗
    that.entityConfigDom = document.getElementById('entityParameConfig');

    // 地图加载
    map.on('load', () => {
      // 设置高亮的图标
      const pulsingDot = that.creatDot(map);
      map.addImage('highLight', pulsingDot, {pixelRatio: 3});
      // 添加图层
      addMapLayer(map, pulsingDot);
      //!!!! 测试sector加载
      // setInterval(() => {
      //   map.getSource('places').setData(that.geojson);
      // }, 200);

      // todo 鼠标按下
      // map.on('mousedown', 'places', this.mousedownEvent);
      // map.off('mousedown', 'places', this.mousedownEvent);
      // 监听点击图标所在图层事件
      // map.on('click', 'places', this.onClick);
      // map.off('click', 'places', this.onClick);

      // zone区域的layer判断不生效（地图任意位置双击都能触发）
      this.map.on('click', 'places', this.onClick);
      map.on('dblclick', 'zone', e => {
        const data = e.features[0].properties;
        //判断点击的点坐标是否在圆的范围内部
        const center = data.referencePlatform ? turf.point(e.features[0].geometry.coordinates) : turf.point([Number(data.longitude), Number(data.latitude)]);
        if (!data?.maximumRadius) return;
        const sector = turf.sector(center, data.maximumRadius, data.startAngle + data.heading, data.stopAngle + data.heading, {units: 'meters'});

        const clickPoint = turf.point([e.lngLat.lng, e.lngLat.lat]);
        const isInSector = turf.booleanPointInPolygon(clickPoint, sector, {ignoreBoundary: false});
        if (!isInSector) return;

        stateControler.currentZoneData = {
          id: data.id,
          properties: data,
          position: e.features[0].geometry.coordinates,
        };
        stateControler.drawDialogVisible = true;
      });
    });
  }
  displayFeatureList(features, lngLat) {
    this.clearHighlight();
    const container = document.createElement('div');
    container.style.maxHeight = '200px';
    container.style.overflow = 'auto';
    let activePop = null;
    features.forEach(feature => {
      const item = document.createElement('div');
      item.style.padding = '8px';
      item.style.cursor = 'pointer';
      item.style.borderBottom = '1px solid #ccc';
      item.textContent = feature.properties.entityDisplayName;
      item.addEventListener('click', () => {
        this.selectFeatures.push(feature.properties.id);
        this.highLightId = feature.properties.id;
        this.setHighLight(feature.geometry.coordinates, false, feature.properties.id);
        if (activePop) activePop.remove();
      });
      container.appendChild(item);
    });
    activePop = new maplibregl.Popup().setLngLat(lngLat).setDOMContent(container).addTo(this.map);
  }
  displayCtrlFeatureList(features, lngLat) {
    const container = document.createElement('div');
    container.style.maxHeight = '200px';
    container.style.overflow = 'auto';
    let activePop = null;
    features.forEach(feature => {
      const item = document.createElement('div');
      item.style.padding = '8px';
      item.style.cursor = 'pointer';
      item.style.borderBottom = '1px solid #ccc';
      item.textContent = feature.properties.entityDisplayName;
      item.addEventListener('click', () => {
        // this.selectFeatures.push(feature.properties.id);
        // this.highLightId = feature.properties.id;
        // this.setHighLight(feature.geometry.coordinates, false, feature.properties.id);

        if (!this.selectFeatures.includes(feature.properties.id)) {
          this.selectFeatures.push(feature.properties.id);
          this.ctrlSetHighLight(feature.geometry.coordinates, feature.properties.id);
        } else {
          this.selectFeatures = this.selectFeatures.filter(id => id !== feature.properties.id);
          this.unSetHighLight(feature.properties.id);
        }

        if (activePop) activePop.remove();
      });
      container.appendChild(item);
    });
    activePop = new maplibregl.Popup().setLngLat(lngLat).setDOMContent(container).addTo(this.map);
  }
  //todo 点击实体图标事件
  onClick = e => {
    // 多选状态
    if (e.originalEvent.ctrlKey) {
      // 已单选别的时,先清空别的
      // && this.selectFeatures.length === 0
      if (this.highLightId !== 0) {
        let lastID = this.highLightId;
        this.clearHighlight();
        this.ctrlSetHighLight(this.featureClick.geometry.coordinates, lastID);
        this.selectFeatures.push(lastID);
      }
      const features = this.map.queryRenderedFeatures(e.point, {layers: ['places']});
      //
      if (features.length > 1) {
        this.displayCtrlFeatureList(features, e.lngLat);
      } else {
        const feature = features[0];
        if (!this.selectFeatures.includes(feature.properties.id)) {
          this.selectFeatures.push(feature.properties.id);
          this.ctrlSetHighLight(feature.geometry.coordinates, feature.properties.id);
        } else {
          this.selectFeatures = this.selectFeatures.filter(id => id !== feature.properties.id);
          this.unSetHighLight(feature.properties.id);
        }
      }
    } else {
      // 单选状态
      const features = this.map.queryRenderedFeatures(e.point, {layers: ['places']});
      const featuresId = features.map(item => item.properties.id);
      const nowClickId = e.features[0].properties.id;
      // 多个重叠则出现列表
      if (features.length > 1 && this.highLightId === 0) {
        this.displayFeatureList(features, e.lngLat);
        return;
      } else if (features.length > 1 && featuresId.includes(this.highLightId)) {
        features.forEach(item => {
          if (this.highLightId === item.properties.id) {
            const closeBtn = document.querySelector('.maplibregl-popup-close-button');
            // 关掉参数配置列表
            if (closeBtn) closeBtn.click();
            this.map.doubleClickZoom.disable();
            //获取的当前点击的实体的数据
            const entityData = item.properties;
            this.clickForcesideId = entityData.side;
            this.clickEntityId = entityData.entityId;
            const coordinates = [entityData.longitude, entityData.latitude];
            stateControler.currentModelData = {
              properties: entityData,
              coordinates,
            };
            this.map.getSource('places').setData(this.geojson);
            // 创建pop 实体参数配置弹窗显示
            this.entityConfigDom.style.display = 'block';
            this.mapPop = this.creatPop(coordinates, this.entityConfigDom, this.map);
          }
        });
        return;
      } else if (features.length > 1 && this.highLightId !== nowClickId) {
        this.displayFeatureList(features, e.lngLat);
        return;
      }
      if (this.highLightId === e.features[0].properties.id) {
        const closeBtn = document.querySelector('.maplibregl-popup-close-button');
        // 关掉参数配置列表
        if (closeBtn) closeBtn.click();
        this.map.doubleClickZoom.disable();
        //获取的当前点击的实体的数据
        const entityData = e.features[0].properties;
        this.clickForcesideId = entityData.side;
        this.clickEntityId = entityData.entityId;
        const coordinates = [entityData.longitude, entityData.latitude];
        stateControler.currentModelData = {
          properties: entityData,
          coordinates,
        };
        this.map.getSource('places').setData(this.geojson);
        // 创建pop 实体参数配置弹窗显示
        this.entityConfigDom.style.display = 'block';
        this.mapPop = this.creatPop(coordinates, this.entityConfigDom, this.map);
        // 图标高亮处理
        this.featureClick.geometry.coordinates = coordinates;
        this.featureClick.id = e.features[0].properties.id;
        this.clickGeojson.features = [];
        this.clickGeojson.features.push(this.featureClick);
        this.map.getSource('points').setData(this.clickGeojson);
      } else {
        this.highLightId = e.features[0].properties.id;
        this.setHighLight(e.features[0].geometry.coordinates, false, this.highLightId);
        this.selectFeatures = [];
      }
    }
    // return;
  };
  //todo 鼠标移动事件
  onMove = throttle(e => {
    if (!this.ctrlKey) return;
    // 多选状态
    if (this.selectFeatures.length > 0) {
      const offsetLng = e.lngLat.lng - this.dragStartPoint.lng;
      this.dragStartPoint.lng += offsetLng;
      const offsetLat = e.lngLat.lat - this.dragStartPoint.lat;
      this.dragStartPoint.lat += offsetLat;
      this.currentOffset[0] += offsetLng;
      this.currentOffset[1] += offsetLat;
      // 更新实体位置
      this.geojson.features.forEach(item => {
        if (this.selectFeatures.includes(item.properties.id)) {
          item.geometry.coordinates[0] = Number(item.geometry.coordinates[0]) + offsetLng;
          item.geometry.coordinates[1] = Number(item.geometry.coordinates[1]) + offsetLat;
          if (item.geometry.coordinates[0] < -180) item.geometry.coordinates[0] += 360;
          if (item.geometry.coordinates[0] > 180) item.geometry.coordinates[0] -= 360;
          item.properties.longitude = item.geometry.coordinates[0].toFixed(6);
          item.properties.latitude = item.geometry.coordinates[1].toFixed(6);
        }
      });
      // 更新高亮框
      this.clickGeojson.features.forEach(item => {
        item.geometry.coordinates[0] += offsetLng;
        item.geometry.coordinates[1] += offsetLat;
        if (item.geometry.coordinates[0] < -180) item.geometry.coordinates[0] += 360;
        if (item.geometry.coordinates[0] > 180) item.geometry.coordinates[0] -= 360;
      });
      this.map.getSource('points').setData(this.clickGeojson);

      this.map.getSource('places').setData(this.geojson);
    } else {
      // 单选移动状态
      const closeBtn = document.querySelector('.maplibregl-popup-close-button');
      // 关掉参数配置列表
      if (closeBtn) closeBtn.click();

      const coords = e.lngLat;
      if (coords.lng < -180) coords.lng += 360;
      if (coords.lng > 180) coords.lng -= 360;

      const coordinates = [coords.lng, coords.lat];

      // 单击事件会触发鼠标移动事件 误拖动实体图标 通过累加的坐标来区分是单击还是拖动
      this.pointArr.push(e.lngLat);
      if (this.pointArr.length < 4) return;
      // 防止   this.pointArr过大
      if (this.pointArr.length > 4) this.pointArr.shift();

      // 给拖动的实体图标重新设置经纬度
      this.currentEntity.geometry.coordinates = coordinates;
      this.currentEntity.properties.latitude = coordinates[1].toFixed(6);
      this.currentEntity.properties.longitude = coordinates[0].toFixed(6);
      //给拖动的实体对应的绑定区域重新设置经纬度
      this.geojson.features.forEach(item => {
        if (!item.properties.isEntity && this.currentEntity.properties.id === item.properties.referencePlatform) {
          item.geometry.coordinates = coordinates;
        }
      });
      // 设置实体图标高亮
      this.featureClick.geometry.coordinates = coordinates;
      this.clickGeojson.features = [];
      this.clickGeojson.features.push(this.featureClick);
      this.map.getSource('points').setData(this.clickGeojson);
      // 重新渲染地图数据
      this.map.getSource('places').setData(this.geojson);
    }
  }, 20);
  //todo 鼠标弹起事件
  onUp = () => {
    this.canvas.style.cursor = '';
    // this.moveBatchHighLight();
    // 关闭鼠标移动事件
    this.map.off('mousemove', this.onMove);
  };
  mousedownEvent = e => {
    const that = this;
    const featuresId = e.features.map(item => item.properties.id);
    if (that.selectFeatures.length > 0) {
      that.isDragging = true;
      that.dragStartPoint = e.lngLat;
      this.map.on('mousemove', this.onMove);
      // 监听鼠标弹起事件
      this.map.on('mouseup', this.onUp);
    } else if (that.highLightId === e.features[0].properties.id) {
      // } else if (featuresId.includes(this.highLightId)) {
      // if (that.selectFeatures.length > 0) {
      //   that.isDragging = true;
      //   that.dragStartPoint = e.lngLat;
      //   this.map.on('mousemove', this.onMove);
      //   // 监听鼠标弹起事件
      //   this.map.on('mouseup', this.onUp);
      // } else if (that.highLightId === e.features[0].properties.id) {
      e.preventDefault();
      // 该实体id
      that.currentModelId = e.features[0].properties.entityId;
      // 鼠标样式
      that.canvas.style.cursor = 'move';
      that.pointArr = [];
      // 设置当前操作的实体
      that.geojson.features.forEach(el => {
        if (el.properties.entityId === that.currentModelId) {
          that.currentEntity = el;
        }
      });
      // 监听鼠标移动事件
      this.map.on('mousemove', this.onMove);
      // 监听鼠标弹起事件
      this.map.on('mouseup', this.onUp);
    }
  };
  setCtrlKey(value) {
    this.ctrlKey = value;
    if (value) {
      this.map.on('mousedown', 'places', this.mousedownEvent);
    } else {
      this.map.off('mousedown', 'places', this.mousedownEvent);
    }
  }
  // 获取经纬度
  getLngLat(callback) {
    this.map.on('mousemove', e => {
      if (e.lngLat.lng < -180) e.lngLat.lng += 360;
      if (e.lngLat.lng > 180) e.lngLat.lng -= 360;
      const lng = e.lngLat.lng;
      const lat = e.lngLat.lat;
      callback({lng, lat});
    });
  }
  //隐藏实体配置弹框
  closeParamsBox() {
    //* 清除vuex实体参数配置列表
    stateControler.currentModelData = {
      properties: '',
      coordinates: [],
    };
    // 关闭参数配置列表
    const closeBtn = document.querySelector('.maplibregl-popup-close-button');
    if (closeBtn) closeBtn.click();
  }
  // 清除高亮
  clearHighlight() {
    this.clickGeojson.features.length = 0;
    this.map.getSource('points').setData(this.clickGeojson);
    this.selectFeatures = [];
    this.highLightId = 0;
    //清除实体列表对应行高亮
    const tree = document.querySelectorAll('#entity-tree');
    tree.forEach(el => {
      const treeNode = el.querySelectorAll('.el-tree-node__content');
      treeNode.forEach(ele => {
        ele.style.backgroundColor = 'transparent';
      });
    });
  }
  //设置高亮
  setHighLight(coordinates, ifCtrl, id) {
    const feature = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [],
      },
      id,
    };
    this.featureClick.geometry.coordinates = coordinates;
    if (!ifCtrl) {
      this.clickGeojson.features = []; // 清除历史
      this.clickGeojson.features.push(this.featureClick);
    } else {
      feature.geometry.coordinates = coordinates;
      this.clickGeojson.features.push(feature);
    }
    this.map.getSource('points').setData(this.clickGeojson);
  }
  // 去掉高亮
  unSetHighLight(deleteId) {
    this.clickGeojson.features = this.clickGeojson.features.filter(item => {
      return item.id !== deleteId;
    });
    // this.clickGeojson.features = this.clickGeojson.features.filter(item => {
    //   return item.geometry.coordinates[0] !== coordinates[0] && item.geometry.coordinates[1] !== coordinates[1];
    // });
    this.map.getSource('points').setData(this.clickGeojson);
  }
  // moveBatchHighLight() {
  //   this.clickGeojson.features.forEach(item => {
  //     // if(item)
  //     item.geometry.coordinates[0] += this.currentOffset[0];
  //     item.geometry.coordinates[1] += this.currentOffset[1];
  //   });
  //   this.currentOffset = [0, 0];
  //   this.map.getSource('points').setData(this.clickGeojson);
  // }
  //设置高亮
  ctrlSetHighLight(coordinates, id) {
    const feature = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates,
      },
      id,
    };
    this.clickGeojson.features.push(feature);
    this.map.getSource('points').setData(this.clickGeojson);
  }
  //清除地图数据
  clearMapData() {
    this.geojson = {
      type: 'FeatureCollection',
      features: [],
    };
    const nullGeojson = {
      type: 'FeatureCollection',
      features: [],
    };
    this.drawInstance.deleteAll();
    this.map.getSource('points').setData(nullGeojson);
    this.map.getSource('places').setData(this.geojson);
    // 清空路径信息
    this.graphicalGeojson.features = [];
    this.clearHighlight();
    this.map.getSource('satellite-track').setData({
      type: 'FeatureCollection',
      features: [],
    });
  }
  //添加实体
  addEntity(feature) {
    this.clearHighlight();
    // 添加到geojson
    this.geojson.features.push(feature);
    this.clickEntityIndex = this.geojson.features.length;
    // this.highLightId = feature.properties.id;
    // 渲染地图数据
    this.map.getSource('places').setData(this.geojson);
  }
  //删除实体
  deleteEntity(entityData, isCategory = false) {
    // 删除高亮
    this.clearHighlight();
    //是否为删除一类实体
    if (isCategory) {
      this.geojson.features = this.geojson.features.filter(el => {
        if (el.properties.side === entityData.side && el.properties.platformTypeId === entityData.id) {
          stateControler.entityModPar.delete(el.properties.id);
        }
        //删掉当前阵营的这类实体
        return el.properties.side !== entityData.side || el.properties.platformTypeId !== entityData.id;
      });
    } else {
      // 删除有地理位置实体数据
      this.geojson.features = this.geojson.features.filter(el => {
        //如果是实体
        if (el.isEntity || el.properties.isEntity) {
          if (el.properties.entityId === entityData.id) {
            stateControler.entityModPar.delete(el.properties.id);
          }
          return el.properties.entityId !== entityData.id;
        } else {
          return el.properties.sourceEntityId !== entityData.id;
        }
      });
    }
    // 重新渲染地图数据
    this.map.getSource('places').setData(this.geojson);
  }
  //点击地图的时候清除高亮,如果处于编辑状态则退出
  registerMapClick() {
    const that = this;
    this.map.on('click', e => {
      const features = that.map.queryRenderedFeatures(e.point, {layers: ['places']});
      if (features.length > 0) {
        return;
      } else {
        const closeBtn = document.querySelector('.maplibregl-popup-close-button');
        if (!closeBtn) {
          that.clearHighlight();
          that.highLightId = 0;
          that.clickEntityIndex = '';
          that.clickGeojson.features.length = 0;
          that.map.getSource('points').setData(that.clickGeojson);
          if (stateControler.drawTipsShow) stateControler.drawTipsShow = false;
          stateControler.shapeInfo = '';
        }
      }
    });
  }
  //!-------------------------Draw控件的相关事件注册-----------------------------
  // 点线面绘制组件数据
  getDrawData(params, isCreate = false) {
    const drawGeoJson = this.drawInstance.getAll();
    //遍历查找当前修改的图形的数据
    drawGeoJson.features.forEach(item => {
      if (item.id === params.id) {
        item.properties = params.properties;
        item.geometry.coordinates = params.geometry.coordinates;

        const type = item.geometry.type;
        item.properties.camp_id = params.properties.side;
        //设置阵营颜色
        const routeColor = stateControler.entityListData.filter(i => {
          return i.side === params.properties.side;
        })[0]?.color;
        item.properties.color = type === 'LineString' ? routeColor : params.properties.color;
        item.properties.color = item.properties.rect ? routeColor : params.properties.color;
      }
    });
    //不是创建时更新速度、高度数据的表格；如果是创建，则不进行表格数据更新
    if (!isCreate) {
      this.graphicalGeojson.features.forEach(item => {
        if (item.id === params.id) {
          item.properties = params.properties;
          item.geometry.coordinates = params.geometry.coordinates;
        }
      });
    }
    this.drawInstance.set(drawGeoJson);
    //绘制完之后切换为simple_select模式,并且将选择的feature置为空
    this.drawInstance.changeMode('simple_select', {featureIds: []});
  }
  //注册选择、更新点线面数据事件
  registerSelectionAndUpdateEvent(callback) {
    // 选择点线面数据
    this.map.on('draw.selectionchange', () => this.selectDrawing(callback));
    this.map.on('draw.update', () => this.selectDrawing(callback));
    this.map.on('draw.modechange', event => {
      const drawLineBtn = document.querySelector('.mapbox-gl-draw_line');
      const drawPolygonBtn = document.querySelector('.mapbox-gl-draw_polygon');
      const drawPointBtn = document.querySelector('.mapbox-gl-draw_point');

      switch (event.mode) {
        case 'draw_line_string':
          this.mapDrawState = '绘制航路';
          drawLineBtn.classList.add('drawActive');
          drawPolygonBtn.classList.remove('drawActive');
          drawPointBtn.classList.remove('drawActive');
          break;
        case 'draw_polygon':
          this.mapDrawState = '绘制区域';
          drawPolygonBtn.classList.add('drawActive');
          drawLineBtn.classList.remove('drawActive');
          drawPointBtn.classList.remove('drawActive');

          break;
        case 'draw_point':
          this.mapDrawState = '绘制点';
          drawPointBtn.classList.add('drawActive');
          drawLineBtn.classList.remove('drawActive');
          drawPolygonBtn.classList.remove('drawActive');

          break;
        default:
          this.mapDrawState = '';
          drawLineBtn.classList.remove('drawActive');
          drawPolygonBtn.classList.remove('drawActive');
          drawPointBtn.classList.remove('drawActive');
          break;
      }
    });
  }
  // 创建点线面数据事件
  registerCreateEvent(callback) {
    // 创建点线面数据
    this.map.on('draw.create', e => {
      //todo 如果为批量部署，不进行数据存储
      if (stateControler.isMassDeployment) {
        //将批量部署的位置存储到vuex
        const coordinates = e.features[0].geometry.coordinates;
        const data = {
          id: e.features[0].id,
          isPoint: coordinates.length > 2,
          coordinates,
        };
        stateControler.massDeploymentData = data;
        callback('massDeploymentShow');
        return;
      }
      //绘制操作提示
      stateControler.drawTipsShow = true;
      if (this.mapDrawState === `测距`) return;
      stateControler.graphicId++;
      callback('createDrawForm', {drawForm: e.features[0]});
      callback('drawWayVisible', true);
    });
  }
  // 删除点线面数据事件
  registerDeleteEvent() {
    const that = this;
    this.map.on('draw.delete', e => {
      //!如果为批量部署的区域删除，不进行数据存储
      if (stateControler.isMassDeployment) return;
      e.features[0].uuid ??= e.features[0].id;
      that.deleteGraphical(e.features[0]);
      // 面积提示内容置空
      stateControler.shapeInfo = '';
      //todo 清空实体的route字段
      // that.geojson.features.forEach(item => {
      //   item.properties.params.forEach(element => {
      //     //判断实体是否有用到点线面
      //   });
      // });
    });
  }
  //选择点线面事件
  selectDrawing(callback) {
    const that = this;
    const map = this.map;
    const draw = this.drawInstance;
    // 处于选择状态的数据
    const data = draw.getSelected();

    // 面积/长度提示
    if (data.features.length > 0) {
      //禁止编辑
      // if (data.features[0].geometry.type === 'Polygon') draw.changeMode('simple_select', {featureIds: []});
      //todo 设置uuid
      data.features[0].uuid ??= data.features[0].id;
      //设置显示提示
      stateControler.drawTipsShow = true;
      if (data.features[0].geometry.type === 'LineString') {
        const length = turf.length(data);
        const shapeInfo = '<p>长度：<strong>' + Math.round(length * 100) / 100 + '</strong>km</p>';

        stateControler.shapeInfo = shapeInfo;
      } else {
        const area = (turf.area(data) / 1000000).toFixed(2); // 将平方米转换为平方千米
        const shapeInfo = '<p>面积：<strong>' + area + '</strong>km²</p>';
        stateControler.shapeInfo = shapeInfo;
      }
    } else {
      const shapeInfo = '';
      stateControler.shapeInfo = shapeInfo;
    }
    if (data.features.length === 0) return;
    // 因为创建完成后，不走selectDrawing事件导致可能无法渲染航路颜色

    //!处于更新状态的数据(这里是获取组件的数据而不是draw控件的数据)
    const currentEditRoute = that.graphicalGeojson.features.find(el => el.id === data.features[0].id);
    if (currentEditRoute) {
      const currentGeoJsonData = currentEditRoute.properties;
      //获取最新的路径点赋值给弹窗组件(编辑模式下用户可拖拽增加路径点)
      const currentShapeData = data.features[0].geometry; //当前选择的形状数据
      const lastViapointData = [];

      switch (currentShapeData.type) {
        case 'LineString':
          currentShapeData.coordinates.forEach((item, index) => {
            lastViapointData.push({
              latitude: Number(item[1].toFixed(6)),
              longitude: Number(item[0].toFixed(6)),
              altitude: currentGeoJsonData?.tableData[index]?.altitude ?? 0,
              altitudeUnit: currentGeoJsonData?.tableData[index]?.altitudeUnit ?? 0,
              speed: currentGeoJsonData?.tableData[index]?.speed ?? 0,
              speedUnit: currentGeoJsonData?.tableData[index]?.speedUnit ?? 0,
            });
          });
          break;
        // case 'Polygon':
        //   //多边形多一层数据
        //   currentShapeData.coordinates[0].forEach((item, index) => {
        //     lastViapointData.push({
        //       latitude: Number(item[1].toFixed(6)),
        //       longitude: Number(item[0].toFixed(6)),
        //       altitude: currentGeoJsonData?.tableData[index]?.altitude ?? 0,
        //       altitudeUnit: currentGeoJsonData?.tableData[index]?.altitudeUnit ?? 0,
        //     });
        //   });
        //   break;
        case 'Point':
          // stateControler.zzPicVisible = true;
          lastViapointData.push({
            latitude: Number(currentShapeData.coordinates[1].toFixed(6)),
            longitude: Number(currentShapeData.coordinates[0].toFixed(6)),
            altitude: currentGeoJsonData?.tableData[0]?.altitude ?? 0,
            altitudeUnit: currentGeoJsonData?.tableData[0]?.altitudeUnit ?? 0,
          });
          break;
        default:
          break;
      }

      // 更新
      callback('updateDrawForm', {
        drawForm: data.features[0],
        currentEditRoute,
        lastViapointData,
      });
      //如果用户已经选择数据显示绘制操作提示
      map.once('dblclick', () => {
        const selectionEntity = draw.getSelected().features;
        console.log('🚀 ~ Maplibre ~ map.once ~ selectionEntity:', selectionEntity);

        if (selectionEntity.length > 0) {
          console.log(selectionEntity);
          //绘制点线面组件显示

          if (selectionEntity[0].geometry.type === 'Polygon') {
            stateControler.currentZoneData = selectionEntity[0];
            draw.changeMode('simple_select', {featureIds: []});
            return (stateControler.drawDialogVisible = true);
          }
          if (selectionEntity[0].geometry.type === 'LineString') {
            stateControler.currentRouteData = selectionEntity[0];
            draw.changeMode('simple_select', {featureIds: []});
            callback('drawWayVisible', true);
          }
          if (selectionEntity[0].geometry.type === 'Point') {
            stateControler.currentRouteData = selectionEntity[0];
            draw.changeMode('simple_select', {featureIds: []});
            callback('drawWayVisible', true);
          }
        }
      });
    } else {
      //添加到graphicalGeojson
      this.addGraphical(data.features[0]);
      // 创建
      callback('createDrawForm', {drawForm: data.features[0]});
    }
  }
  //删除图形(包括点、多边形，圆)
  deleteGraphical(data) {
    if (!data) return;
    const id = data.uuid ? data.uuid : data.id;
    //删除geojson
    this.graphicalGeojson.features = this.graphicalGeojson.features.filter(el => {
      return el.uuid ? el.uuid !== id : el.id === id;
    });
    //删除控件的显示
    if (this.drawInstance.get(id)) {
      this.drawInstance.delete(id);
    }
  }
  //更新图形(包括点、多边形，圆，航路)
  updateGraphical(data) {
    //更新graphicalGeojson的数据
    const index = this.graphicalGeojson.features.findIndex(i => i.id === data.id);
    if (index !== -1) {
      this.graphicalGeojson.features[index] = JSON.parse(JSON.stringify(data));
    }
    // 更新绘制控件的数据
    if (data.properties?.zoneType !== 'circular') {
      const drawGeoJson = this.drawInstance.getAll();
      drawGeoJson.features.forEach(i => {
        if (i.id === data.id) {
          i.geometry.coordinates = data.geometry.coordinates;
          i.properties = data.properties;
        }
      });

      // console.log(drawGeoJson);
      this.drawInstance.set(drawGeoJson);
    } else {
      // 如果是圆的更新
      const index = this.geojson.features.findIndex(i => i.id === data.id);
      if (index !== -1) {
        this.geojson.features[index] = JSON.parse(JSON.stringify(data));
      }
      this.map.getSource('places').setData(this.geojson);
    }
    this.drawInstance.changeMode('simple_select', {featureIds: [data.id]});
    this.drawInstance.changeMode('simple_select', {featureIds: []});
  }
  //添加图形(包括点、多边形，圆)
  addGraphical(data) {
    //控件是否添加
    const drawIsAdd = this.drawInstance.get(data.uuid);
    //判断图形数据中是否已经被添加过
    const hasDuplicate = this.graphicalGeojson.features.some(i => i.properties.displayName === data.properties.displayName);
    if (data.properties.zoneType === 'circular' && !hasDuplicate) {
      // 将数据添加到draw控件(也就是通过draw控件去显示点线,多边形等数据)
      this.geojson.features.push(JSON.parse(JSON.stringify(data)));
      this.graphicalGeojson.features.push(JSON.parse(JSON.stringify(data)));
      this.map.getSource('places').setData(this.geojson);
    } else if (!hasDuplicate && !drawIsAdd) {
      // 控件和图形geojson都没有
      const addIds = this.drawInstance.add(data);
      data.id ? '' : (data.id = addIds[0]);
      data.uuid = data.id;
      data.properties.id = data.id; //property存id用于获取
      this.graphicalGeojson.features.push(data);
      // stateControler.graphicId++;
    } else if (!hasDuplicate && drawIsAdd) {
      // 控件有，geojson没有
      this.graphicalGeojson.features.push(data);
      // stateControler.graphicId++;
    }
  }
  //创建draw控件
  creatDraw() {
    const draw = new MapboxDraw({
      // defaultMode: "draw_rectangle",
      userProperties: true,
      displayControlsDefault: false,
      modes: {
        ...MapboxDraw.modes,
        draw_circle: CircleMode,
        drag_circle: DragCircleMode,
        direct_select: DirectMode,
        simple_select: SimpleSelectMode,
        draw_rectangle: DrawRectangle,
        draw_measure: Measure,
      },
      controls: {
        polygon: true, //面
        line_string: true, //线
        point: true, //点
        trash: true, //删除
      },
      styles: [
        {
          id: 'gl-draw-polygon-fill-inactive',
          type: 'fill',
          filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
          paint: {
            'fill-color': getFillColor(),
            'fill-outline-color': getFillColor(),
            'fill-opacity': 0.5,
          },
        },
        {
          id: 'gl-draw-polygon-fill-active',
          type: 'fill',
          filter: ['all', ['==', 'active', 'true'], ['==', '$type', 'Polygon']],
          paint: {
            'fill-color': getFillColor(),
            'fill-outline-color': getFillColor(),
            'fill-opacity': 0.1,
          },
        },
        {
          id: 'gl-draw-polygon-midpoint',
          type: 'circle',
          filter: ['all', ['==', '$type', 'Point'], ['==', 'meta', 'midpoint']],
          paint: {
            'circle-radius': 3,
            'circle-color': getFillColor(),
          },
        },
        {
          id: 'gl-draw-polygon-stroke-inactive',
          type: 'line',
          filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round',
          },
          paint: {
            'line-color': getFillColor(),
            'line-width': 2,
          },
        },
        {
          id: 'gl-draw-polygon-stroke-active',
          type: 'line',
          filter: ['all', ['==', 'active', 'true'], ['==', '$type', 'Polygon']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round',
          },
          paint: {
            'line-color': getFillColor(),
            'line-dasharray': [0.2, 2],
            'line-width': 2,
          },
        },
        {
          id: 'gl-draw-line-inactive',
          type: 'line',
          filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'LineString'], ['!=', 'mode', 'static']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round',
          },
          paint: {
            'line-color': getFillColor(),
            'line-width': 2,
          },
        },
        {
          id: 'gl-draw-line-active',
          type: 'line',
          filter: ['all', ['==', '$type', 'LineString'], ['==', 'active', 'true']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round',
          },
          paint: {
            'line-color': getFillColor(),
            'line-dasharray': [0.2, 2],
            'line-width': 2,
          },
        },
        {
          id: 'gl-draw-polygon-and-line-vertex-stroke-inactive',
          type: 'circle',
          filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point'], ['!=', 'mode', 'static']],
          paint: {
            'circle-radius': 5,
            'circle-color': getFillColor(),
          },
        },
        {
          id: 'gl-draw-polygon-and-line-vertex-inactive',
          type: 'circle',
          filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point'], ['!=', 'mode', 'static']],
          paint: {
            'circle-radius': 3,
            'circle-color': getFillColor(),
          },
        },
        {
          id: 'gl-draw-point-point-stroke-inactive',
          type: 'circle',
          filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'Point'], ['==', 'meta', 'feature'], ['!=', 'mode', 'static']],
          paint: {
            'circle-radius': 5,
            'circle-opacity': 1,
            'circle-color': getFillColor(),
          },
        },
        {
          id: 'gl-draw-point-inactive',
          type: 'circle',
          filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'Point'], ['==', 'meta', 'feature'], ['!=', 'mode', 'static']],
          paint: {
            'circle-radius': 3,
            'circle-color': getFillColor(),
          },
        },
        {
          id: 'gl-draw-point-stroke-active',
          type: 'circle',
          filter: ['all', ['==', '$type', 'Point'], ['==', 'active', 'true'], ['!=', 'meta', 'midpoint']],
          paint: {
            'circle-radius': 7,
            'circle-color': '#fff',
          },
        },
        {
          id: 'gl-draw-point-active',
          type: 'circle',
          filter: ['all', ['==', '$type', 'Point'], ['!=', 'meta', 'midpoint'], ['==', 'active', 'true']],
          paint: {
            'circle-radius': 5,
            'circle-color': getFillColor(),
          },
        },
        {
          id: 'gl-draw-polygon-fill-static',
          type: 'fill',
          filter: ['all', ['==', 'mode', 'static'], ['==', '$type', 'Polygon']],
          paint: {
            'fill-color': getFillColor(),
            'fill-outline-color': getFillColor(),
            'fill-opacity': 0.1,
          },
        },
        {
          id: 'gl-draw-polygon-stroke-static',
          type: 'line',
          filter: ['all', ['==', 'mode', 'static'], ['==', '$type', 'Polygon']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round',
          },
          paint: {
            'line-color': getFillColor(),
            'line-width': 2,
          },
        },
        {
          id: 'gl-draw-line-static',
          type: 'line',
          filter: ['all', ['==', 'mode', 'static'], ['==', '$type', 'LineString']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round',
          },
          paint: {
            'line-color': getFillColor(),
            'line-width': 2,
          },
        },
        {
          id: 'gl-draw-point-static',
          type: 'circle',
          filter: ['all', ['==', 'mode', 'static'], ['==', '$type', 'Point']],
          paint: {
            'circle-radius': 5,
            'circle-color': getFillColor(),
          },
        },
        {
          id: 'gl-draw-symbol',
          type: 'symbol',
          layout: {
            'text-line-height': 1.1,
            'text-size': 15,
            'text-anchor': 'left',
            'text-justify': 'left',
            'text-offset': [0.8, 0.8],
            'text-field': ['get', 'radius'],
            'text-font': ['NotoSansSC-Regular'],
            'text-max-width': 50,
            'text-allow-overlap': true, //要加这行代码 防止字体由于和其他图层碰撞 显示不出来的问题
          },
          paint: {
            'text-color': 'hsl(0, 0%, 95%)',
            'text-halo-color': 'hsl(0, 5%, 0%)',
            'text-halo-width': 1,
            'text-halo-blur': 1,
          },
          filter: ['==', 'meta', 'currentPosition'],
        },
        {
          id: 'gl-draw-polygon-rotate-point-icon',
          type: 'symbol',
          filter: ['all', ['==', 'meta', 'midpoint'], ['==', '$type', 'Point'], ['!=', 'mode', 'static'], ['==', 'showRotate', true]],
          layout: {
            'icon-image': 'rotate',
            'icon-allow-overlap': true,
            'icon-ignore-placement': true,
            'icon-rotation-alignment': 'map',
            'icon-rotate': ['get', 'heading'],
          },
          paint: {
            'icon-opacity': 1.0,
            'icon-opacity-transition': {
              delay: 0,
              duration: 0,
            },
          },
        },
      ],
    });
    return draw;
  }
  //创建一个pop(实体编辑弹窗)
  creatPop(coordinates, domContainer) {
    if (this.mapPop) this.mapPop.remove();
    const markerHeight = 20,
      markerRadius = 10,
      linearOffset = 10;
    const popupOffsets = {
      top: [0, markerHeight + 20],
      'top-left': [0, markerHeight + 20],
      'top-right': [0, markerHeight + 20],
      bottom: [0, -markerHeight],
      'bottom-left': [linearOffset, (markerHeight - markerRadius + linearOffset) * -1],
      'bottom-right': [-linearOffset, (markerHeight - markerRadius + linearOffset) * -1],
      left: [markerHeight - markerRadius + 10, 0],
      right: [-markerRadius - 10, 0],
    };

    const popup = new maplibregl.Popup({
      // offset: popupOffsets,
      // closeOnMove: false,
      closeOnClick: false,
    })
      .setLngLat(coordinates)
      .setDOMContent(domContainer)
      .addTo(this.map);

    return popup;
  }
  // 创建比例尺
  creatScale() {
    return new maplibregl.ScaleControl({maxWidth: 80, unit: 'metric'});
  }
  //创建图标高亮框
  creatDot() {
    const size = 200;
    const pulsingDot = {
      width: size,
      height: size,
      data: new Uint8Array(size * size * 4),
      onAdd: function () {
        const elementCanvas = document.createElement('canvas');
        elementCanvas.width = this.width;
        elementCanvas.height = this.height;
        this.context = elementCanvas.getContext('2d');
        //getContext('2d')方法让我们拿到一个CanvasRenderingContext2D对象，所有的绘图操作都需要通过这个对象完成
      },
      render: function () {
        const context = this.context;
        context.clearRect(0, 0, this.width, this.height);
        // 擦除(0,0)位置大小为200x200的矩形，擦除的意思是把该区域变为透明
        context.beginPath();
        //新建一条路径，生成之后，图形绘制命令被指向到路径上生成路径。
        context.rect(40, 40, 120, 120);
        //画圆使用arc（中心点X，中心点Y，半径，起始角度，结束角度）
        // arc(x,y,radius,startAngle,endAngle,anticlockwise)
        //画一个以（x,y）为圆心的以radius为半径的圆弧（圆），从startAngle开始到endAngle结束，按照anticlockwise给定的方向（默认为顺时针）来生成。
        context.fillStyle = 'rgb(255,255,255,0)';
        context.strokeStyle = 'yellow';
        context.lineWidth = 5;
        context.fill(); //通过填充路径的内容区域生成实心的图形。
        context.stroke(); //通过线条来绘制图形轮廓。
        context.beginPath();
        this.data = context.getImageData(0, 0, this.width, this.height).data;
        // map.triggerRepaint();
        return true;
      },
    };
    return pulsingDot;
  }
  showEdit(coordinates) {
    this.map.setCenter(coordinates);
    this.entityConfigDom.style.display = 'block';
    this.mapPop = this.creatPop(coordinates, this.entityConfigDom, this.map);
    this.setHighLight(coordinates);
  }
  // 测距模式
  draw_measure() {
    // 如果是测绘工具的话 先画出来线段 双击的时候直接清除
    this.drawInstance.changeMode('draw_measure');
    this.map.once('draw.create', e => {
      this.clearDistanceLine(e);
    });
  }
  // 清除测量距离时绘制的线段
  clearDistanceLine(e) {
    const geojson = this.drawInstance.getAll();
    const index = geojson.features.findIndex(item => item.id === e.features[0].id);
    if (index > -1) {
      geojson.features.splice(index, 1);
      this.drawInstance.set(geojson);
    }
  }
}
