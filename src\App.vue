<!--
 * @Description: 请填写简介
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-12 15:39:30
-->
<script setup lang="ts">
import Header from '@/components/HeaderContainer.vue';
import Footer from '@/components/FooterContainer.vue';
import IframeViewer from '@/components/IframeViewer.vue';
</script>

<template>
  <div class="content">
    <Header />
    <router-view />
    <Footer />
    <IframeViewer />
  </div>
</template>

<style lang="less" scoped>
.content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
