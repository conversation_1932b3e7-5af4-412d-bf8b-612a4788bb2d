/*
 * <AUTHOR> 老范
 * @Date         : 2022-08-09 09:30:45
 * @LastEditors: tzs
 * @LastEditTime: 2024-02-28 13:34:18
 * @Description  : 请填写简介
 */
import {createRouter, createWebHistory} from 'vue-router';

const routes = [
  {
    path: '/',
    name: '',
    redirect: '/Index',
  },
  {
    path: '/Index',
    name: 'Index',
    component: () => import('@/views/BodyContainer.vue'),
  },
];
const router = createRouter({
  history: createWebHistory(),
  routes,
});
export default router;
