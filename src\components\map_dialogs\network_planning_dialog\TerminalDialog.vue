<!--
 * @Description: 终端
 * @Author: tian<PERSON><PERSON><PERSON>
 * @Date: 2023-08-11 09:18:09
 * @LastEditors: tzs
 * @LastEditTime: 2024-10-14 14:01:06
-->
<script setup>
import {reactive, computed, ref, watch, nextTick, onMounted} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';

const emit = defineEmits(['update:terminalVisible', 'addTerminal', 'editTerminal']);
const props = defineProps({
  terminalVisible: {
    type: Boolean,
    default: false,
  },
  networkType: {
    type: Number,
    default: 1,
  },
  currentSide: {
    type: String,
    default: '',
  },
  currentSideEntityList: {
    type: Array,
    default: () => [],
  },
  comms: {
    type: Array,
    default: () => [],
  },
  editData: {
    type: Object,
    default: () => ({}),
  },
  selectedNet: {
    type: Object,
    default: () => ({}),
  },
  netData: {
    type: Array,
    default: () => [],
  },
});
const dialogVisible = computed(() => props.terminalVisible);

const form = ref({
  platformName: '',
  name: '',
  type: '',
  network: '',
  location: '',
});
const commList = ref([]);
const isEdit = ref(false);
const currentEntity = computed(() => props.currentSideEntityList.find(i => i.entityName === form.value.platformName));
// 过滤掉已经被添加过的终端
const filterComms = computed(() => {
  return props.netData
    .map(i => i.children.map(i => i))
    .flat(2)
    .map(i => i.id);
});

watch(dialogVisible, value => {
  if (!value) return clearData();
  form.value.network = props.selectedNet.displayName;
});

watch(
  () => isEdit.value,
  val => {
    if (!val) return;
    nextTick(() => {
      form.value.name = props.editData.name;
      form.value.platformName = props.editData.platformName;
      form.value.type = props.editData.type;
      form.value.network = props.editData.network;
      if (!currentEntity.value) return;
      commList.value = props.comms.get(currentEntity.value.id).comms;
    });
  }
);
const clearData = () => {
  Object.keys(form.value).forEach(i => (form.value[i] = ''));
  isEdit.value = false;
};
// 选择平台
const selectedPlatform = () => {
  commList.value = props.comms.get(currentEntity.value.id).comms.filter(i => !filterComms.value.includes(i.id));
  form.value.name = '';
};
// 选择终端
const selectedTerminal = value => {
  const data = commList.value.find(i => i.name === value);
  form.value.type = data.templateTypeName;
};
// 保存终端
const saveTerminal = () => {
  if (form.value.name === '') return ElMessage.warning('请选择平台');
  if (form.value.type === '') return ElMessage.warning('请选择终端');

  const terminal = commList.value.find(i => i.name === form.value.name);
  console.log(terminal, '编辑的终端');
  if (isEdit.value) {
    emit('editTerminal', {
      displayName: terminal.displayName,
      name: form.value.name,
      platformName: terminal.platformName,
      id: terminal.id,
      templateTypeName: terminal.templateTypeName,
      uniqueId: terminal.uniqueId,
    });
  } else {
    emit('addTerminal', terminal);
  }
  emit('update:terminalVisible', false);
};
defineExpose({isEdit});
</script>

<template>
  <el-dialog v-model="dialogVisible" title="终端" :close-on-click-modal="false" draggable center :before-close="() => emit('update:terminalVisible', false)" width="516px">
    <div class="terminal">
      <el-form :model="form">
        <el-form-item label="平台名称">
          <el-select v-model="form.platformName" disabled @change="selectedPlatform">
            <el-option v-for="item in props.currentSideEntityList" :key="item.id" :label="item.entityDisplayName" :value="item.entityName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="终端名称">
          <el-select v-model="form.name" type="number" disabled @change="selectedTerminal">
            <el-option v-for="(item, index) in commList" :key="index" :label="item.displayName" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="终端类型">
          <el-input v-model="form.type" disabled></el-input>
        </el-form-item>
        <el-form-item label="所属网络">
          <el-input v-model="form.network" disabled></el-input>
        </el-form-item>
        <!-- <el-form-item v-if="props.networkType === 1" label="网络地址">
          <el-input v-model="form.location" disabled type="text"></el-input>
        </el-form-item> -->
      </el-form>
    </div>
    <template #footer>
      <el-button @click="() => emit('update:terminalVisible', false)">取消</el-button>
      <el-button @click="saveTerminal">保存</el-button>
    </template>
  </el-dialog>
</template>

<style lang="less">
.terminal {
  width: 496px;
  height: 175px;
  padding-left: 10px;
}
</style>
