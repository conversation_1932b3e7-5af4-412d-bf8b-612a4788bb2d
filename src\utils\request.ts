/*
 * @Description: 请填写简介
 * @Author: tzs
 * @Date: 2024-02-28 10:35:59
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-09 16:59:05
 *
 */
import axios, {AxiosInstance, AxiosRequestConfig} from 'axios'; // 引入axios
import {ElMessage} from 'element-plus';
import type {MyResponseType} from '@/types/index';
interface serviceConfigType extends AxiosInstance {
  urlType: string;
}
const {BaseResourceUrl, LocalRun, PreviewControlUrl} = globalConfig;

const service: any = axios.create({
  timeout: 5000,
});

// http request 拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig | any) => {
    //服务区分
    switch (config.urlType) {
      case 'exe':
        config.url = LocalRun + config.url;
        break;
      case 'control':
        config.url = PreviewControlUrl + config.url;
        break;
      case 'control3d':
        config.url = PreviewControlUrl + config.url;
        break;
      case 'download':
        config.url = BaseResourceUrl + config.url;
        config.responseType = 'blob';
        config.headers['Content-Type'] = 'application/json; application/octet-stream';
        break;
      case 'upLoad':
        config.url = BaseResourceUrl + config.url;
        config.headers['Content-Type'] = 'multipart/form-data';
        break;
      default:
        config.url = BaseResourceUrl + config.url;
        break;
    }

    return config;
  },
  (error: any) => {
    ElMessage({
      showClose: true,
      message: error,
      type: 'error',
    });
    return error;
  }
);

const successCodeAry: number[] = [200, 201, 202, 204];
const errorCodeAry: number[] = [400, 401, 403, 404, 406, 410, 422, 500];
// http response 拦截器
service.interceptors.response.use(
  (response: MyResponseType) => {
    if (successCodeAry.includes(response.status)) return response.data;
  },
  (error: any) => {
    if (error.response) {
      if (errorCodeAry.includes(error.response.status))
        ElMessage({
          showClose: true,
          message: error.response.data.msg,
          type: 'error',
        });
      if (error.response.status === 401) {
        window.top?.postMessage(
          {
            tokenFailure: true,
          },
          '*'
        );
      }
    } else {
      ElMessage({
        showClose: true,
        message: error.message,
        type: 'error',
      });
    }
    return Promise.reject(error);
  }
);
export default service;
