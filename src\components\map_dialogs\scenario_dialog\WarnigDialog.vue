<!--
 * @Description: 请填写简介
 * @Author: tzs
 * @Date: 2024-07-09 11:38:18
 * @LastEditors: tzs
 * @LastEditTime: 2024-07-09 11:39:40
-->
<script setup lang="ts">
import {ref} from 'vue';
const dialogVisible = ref(false);
const title = ref('');
const content = ref('');
const emits = defineEmits(['saveTemplate', 'save', 'notSave']);
const show = (data: {title: string; content: string}) => {
  title.value = data.title;
  content.value = data.content;
  dialogVisible.value = true;
};
const close = () => {
  dialogVisible.value = false;
};
// 保存
const handleSave = () => {
  emits('save');
};
// 不保存
const handlenotSave = () => {
  emits('notSave');
};
defineExpose({
  show,
  close,
});
</script>
<template>
  <el-dialog v-model="dialogVisible" :close-on-click-modal="false" :title="title" align-center width="380" show-close class="doubt-dialog">
    <el-row class="align-items flex-dir-column doubt-box">
      <img src="@/assets/images/warning_img.png" alt="" />
      <p class="f-8 doubt-text">{{ content }}</p>
      <el-row>
        <el-button @click="handleSave">保存</el-button>
        <el-button @click="handlenotSave">不保存</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </el-row>
      <p></p>
    </el-row>
  </el-dialog>
</template>
<style lang="less" scoped>
.doubt-dialog {
  .doubt-box {
    padding: 10px 5px 15px;
    color: #fff;
    background: var(--panel-bg-color);

    .doubt-text {
      margin: 20px 0;
      /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
      font-family: Regular;
      line-height: 1.2;
      text-align: center;
    }
  }
}
</style>
