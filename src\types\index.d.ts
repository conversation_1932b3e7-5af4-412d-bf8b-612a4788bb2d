/*
 * <AUTHOR> 老范
 * @Date         : 2022-08-12 13:38:01
 * @LastEditors: tzs
 * @LastEditTime: 2024-08-21 09:21:25
 * @Description  : 公用types
 */

export interface MyResponseType<T = any> {
  code?: number;
  message?: string;
  data: T;
  status: number;
}
export interface GeoJsonType {
  type: string;
  data: GeoJsonDataType;
}
export interface GeoJsonDataType {
  type: string;
  features: featureType[];
}
export interface featureType {
  type: string;
  geometry: geometryType;
  id?: string;
  isEntity?: boolean;
  properties: propertiesType;
}
export interface propertiesType {
  id: string;
  entityId: number | string;
  name: string;
  latitude: number;
  altitude: number | string;
  longitude: number;
  platformTypeId: number;
  entityName: string;
  isEntity: boolean;
  entityDisplayName: string;
  name: string;
  displayName: string;
  color: string;
  angle?: number; // 张角
  yaw?: number; //俯仰角
  speed?: number; //限速信息
  side: string; //限速信息
  sourceEntityId?: number;
}

interface geometryType {
  coordinates: Array<number>[] | number[];
  type: string;
}

export interface entityListDataType {
  title: string;
  side: string;
  color: string;
  content: entityContent[];
}
export interface entityContent {
  id: number;
  label: string;
  children: {id: number; label: string; platformTypeId: number}[];
}

export interface scenarioFormType {
  name: string; //任务/想定名称
  description: string; //想定描述
  endTime: string;
  unit: string;
  geoJson?: GeoJsonDataType;
  scenarioId?: string | number;
  globalScripts?: number[];
  routes?: [];
  zones?: [];
  environment?: [] | string;
  networks?: [];
  customScriptFileId?: string;
}

export interface missionPlanType {
  displayName: string;
  id: number;
  name: string;
  scenarioId: number;
  side: string;
  stages: stageType[];
}
export interface stageType {
  condition: string;
  conditionType: string;
  displayName: string;
  entityInfo: string;
  id: number;
  name: string;
  planId: number;
  eventParamValue: string;
  sort: number;
}
