export class COE2TLEConversionAlg {
  /**
   * @Description :卫星开普勒六根数转换到两行轨道根数;
   * @Method_Name : simpleCoe2Tle;
   * @param semiMajorAxis: 半长轴 (km)
   * @param eccentricity: 偏心率
   * @param inclination: 轨道倾角 (deg)
   * @param raan: 赤交点赤经 (deg)
   * @param argPerigee: 近地点幅角 (deg)
   * @param trueAnomaly: 真近点角 (deg)
   * @param epoch: 历元时刻 (格式：YYDDD.DDDDDDDD, 例如2022年1月1日为22001.00000000)
   * @param name: 卫星两位数编号，例如 "01"
   * @return : 两行轨道根数 (TLE)
   * @return : String[]
   */
  simpleCoe2Tle(sixParam, epoch, name) {
    const {semiMajorAxis, eccentricity, inclination, raan, argPerigee, trueAnomaly} = sixParam;
    const mu = 398600.4418; // 地球标准重力参数, 单位: km^3/s^2

    // 计算平均运动 (Mean motion)
    const meanMotion = (Math.sqrt(mu / semiMajorAxis ** 3) * 86400) / (2 * Math.PI); // 单位: 圈/天

    // 计算平近点角 (Mean anomaly)
    const E = Math.atan2(Math.sqrt(1 - eccentricity ** 2) * Math.sin(trueAnomaly * (Math.PI / 180)), eccentricity + Math.cos(trueAnomaly * (Math.PI / 180)));
    const meanAnomaly = (E - eccentricity * Math.sin(E)) * (180 / Math.PI); // 转换为度数

    // 初始化TLE数组
    const tle = ['', ''];
    let sBuffer0 = '1 '; // 第一行的开头
    let sBuffer1 = '2 '; // 第二行的开头
    let sTemp; // 临时变量，存储各个参数的字符串形式
    let length; // 临时变量，存储字符串的长度

    // 处理卫星编号，使其成为5位数格式
    sTemp = name;
    length = sTemp.length;
    if (length === 5) {
      sBuffer0 += sTemp;
      sBuffer1 += sTemp;
    } else if (length < 5) {
      for (let i = 0; i < 5 - length; i++) {
        sBuffer0 += '0';
        sBuffer1 += '0';
      }
      sBuffer0 += sTemp;
      sBuffer1 += sTemp;
    } else {
      console.log('satellite name error');
      return null;
    }

    // 构建第一行TLE
    sBuffer0 += `U          ${epoch}  .00000000  00000-0  00000-0 0 0000`;

    // 构建第二行TLE
    sBuffer1 += ' ';

    // 轨道倾角 (inclination)
    sTemp = inclination.toFixed(4); // 格式化为小数点后4位
    sBuffer1 += sTemp.padStart(8, '0'); // 填充为8位，前面补0
    sBuffer1 += ' ';

    // 赤交点赤经 (raan)
    sTemp = raan.toFixed(4); // 格式化为小数点后4位
    sBuffer1 += sTemp.padStart(8, '0'); // 填充为8位，前面补0
    sBuffer1 += ' ';

    // 偏心率 (eccentricity)
    sTemp = eccentricity.toFixed(7).substring(2); // 格式化为小数点后7位，并去掉"0."
    sBuffer1 += sTemp.padEnd(7, '0'); // 填充为7位，后面补0
    sBuffer1 += ' ';

    // 近地点幅角 (argPerigee)
    sTemp = argPerigee.toFixed(4); // 格式化为小数点后4位
    sBuffer1 += sTemp.padStart(8, '0'); // 填充为8位，前面补0
    sBuffer1 += ' ';

    // 平近点角 (meanAnomaly)
    sTemp = meanAnomaly.toFixed(4); // 格式化为小数点后4位
    sBuffer1 += sTemp.padStart(8, '0'); // 填充为8位，前面补0
    sBuffer1 += ' ';

    // 平均运动 (meanMotion)
    sTemp = meanMotion.toFixed(8); // 格式化为小数点后8位
    sBuffer1 += sTemp.padStart(11, '0'); // 填充为11位，前面补0
    sBuffer1 += '0'; // 最后一位固定为0

    tle[0] = sBuffer0;
    tle[1] = sBuffer1;

    // 计算第一行校验和
    let sum = 0;
    for (let i = 0; i < tle[0].length; i++) {
      let subTLE = tle[0].substring(i, i + 1);
      if (subTLE !== 'U' && subTLE !== ' ' && subTLE !== '.' && subTLE !== '+') {
        sum += subTLE === '-' ? 1 : parseInt(subTLE);
      }
    }
    tle[0] += sum % 10;

    // 计算第二行校验和
    sum = 0;
    for (let i = 0; i < tle[1].length; i++) {
      let subTLE = tle[1].substring(i, i + 1);
      if (subTLE !== 'U' && subTLE !== ' ' && subTLE !== '.' && subTLE !== '+') {
        sum += subTLE === '-' ? 1 : parseInt(subTLE);
      }
    }
    tle[1] += sum % 10;

    return tle;
  }
}

// 示例使用
const sixParam = {
  semiMajorAxis: 7778, // 半长轴，单位: km
  eccentricity: 0.1, // 偏心率
  inclination: 30, // 轨道倾角，单位: deg
  raan: 10, // 赤交点赤经，单位: deg
  argPerigee: 20, // 近地点幅角，单位: deg
  trueAnomaly: 0, // 真近点角，单位: deg
};
// console.log(sixParam, '六根数');

const epoch = '22001.00000000'; // 历元时刻，例：2022年1月1日
const sname = '01'; // 卫星编号

const conversionAlg = new COE2TLEConversionAlg();

const tle = conversionAlg.simpleCoe2Tle(sixParam, epoch, sname);

// console.log(tle[0]);
// console.log(tle[1]);
