/*
 * @Description: 状态管理
 * @Author: tzs
 * @Date: 2024-02-28 14:18:04
 * @LastEditors: tzs
 * @LastEditTime: 2024-10-14 16:18:16
 */
import {defineStore} from 'pinia';
import timer from '@/utils/timer';
import type {entityListDataType, scenarioFormType} from '@/types/index';
export interface envDataType {
  type: string;
  displayName: string;
  name: string;
  defaultValue: string;
  unitType?: string;
  defaultType: string;
  templateParamId: string;
  unitId: number;
  sortNum: number;
  groupId: string;
  value: any;
  configureJson?: string;
}
interface dataType {
  scenarioEditFlag: boolean;
  scenarioId: number | null | string;
  shapeInfo: string;
  graphicId: number;
  currentEntityId: number | string;
  currentZoneData: {};
  currentModelData: {
    properties: {};
    coordinates: [];
  };
  entity2GroupKeys: number[];
  isMassDeployment: boolean;
  massDeploymentModelID: null | number;
  massDeploymentData: {id: string | number; isPoint: boolean; coordinates: []};
  currentSide: string; //实体绑定的阵营
  entityListData: entityListDataType[];
  commandChainData: any[];
  missionPlanData: [];
  globalScript: number[];
  localScript: string;
  entityModPar: Map<string, {}>;
  weatherCheckList: envDataType[];
  routesNetworks: number[];
  terrain: number[];
  unitConfig: Map<number, {}>;
  startTime: string; //作战时间
  networks: any[];
}

export const useStateStore = defineStore('state', () => {
  // 面板显隐控制参数集合
  const visibleController = {
    controlModel: true, //模型资源面板
    controlEntity: true, //实体管理面板
    groupVisible: false, //编组管理面板
    controlLayer: false, //图层管理面板
    graphicManagerVisible: false, //图形元素管理面板
    graphicEditPanelVisible: false, //图形编辑面板
    drawTipsShow: false, //绘制操作提示
    massDeploymentVisible: false, //批量添加模型组件显隐
    globalScriptVisble: false,
    drawDialogVisible: false,
    showCheckbox: false,
  };

  // 数据集合
  const data: dataType = {
    scenarioEditFlag: false, //是否编辑想定
    scenarioId: null,
    shapeInfo: '', //显示的面积长度参数
    graphicId: 0, //绘制图形的id
    currentEntityId: 0, //地图实体的id
    currentZoneData: {}, //当前编辑的图形数据
    currentModelData: {
      properties: {},
      coordinates: [],
    }, //当前编辑的实体数据
    entity2GroupKeys: [], // 被选中的entityid
    isMassDeployment: false, //是否为批量部署
    massDeploymentModelID: null, //要批量部署的模型uuid
    massDeploymentData: {id: '', isPoint: false, coordinates: []}, //批量部署区域或点的位置
    currentSide: 'red', //实体绑定的阵营
    entityListData: [
      {title: '红方', side: 'red', color: '#F80404', content: []},
      {title: '蓝方', side: 'blue', color: '#00B1FF', content: []},
    ],
    commandChainData: [], //指挥链数据
    missionPlanData: [], //任务规划数据
    globalScript: [], //全局脚本数据
    localScript: '', //本地脚本数据
    weatherCheckList: [], //自然环境
    routesNetworks: [], //路网
    terrain: [], //地形
    entityModPar: new Map(), //实体的参数和组件 {id:'',{params:[],modules:[]}}
    unitConfig: new Map(),
    startTime: timer.dateFormatting(new Date()),
    networks: [], //网络规划数据
  };

  return {...visibleController, ...data};
});
