<!--
 * @Description: 有地理位置实体参数配置弹窗
 * @Author: tian<PERSON><PERSON><PERSON>
 * @Date: 2022-12-16 10:34:59
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-13 14:01:47
-->
<script setup>
import {ElMessage, ElMessageBox} from 'element-plus';
import EntityParamConfig from '@/components/map_dialogs/param_edit/EntityParamConfig.vue';
import {sixParamToTwoLine, getRouteGeojson} from '@/utils/data_process';
import {ref} from 'vue';
import {useMapStore} from '@/pinia/map/index';
import {useStateStore} from '@/pinia/state/index';
import {storeToRefs} from 'pinia';
import timer from '@/utils/timer';
import bus from '@/utils/bus';

const {mapInstance} = storeToRefs(useMapStore());
const stateControler = useStateStore();

const entityParamConfig = ref(null);

//!---------- 实体数据的操作部分 -------------------!
// 保存实体参数配置列表数据
const saveModel = async entity => {
  console.log('🚀 ~ saveModel ~ entity:', entity);
  const Null = await entityParamConfig.value.validateForm();
  if (!Null) return;
  //将阵营id对应的颜色更新
  entity.properties.color = stateControler.entityListData.filter(i => i.side === entity.properties.side)[0].color;
  const name = entity.properties.entityDisplayName;
  const entityId = entity.properties.entityId;
  const coordinates = [entity.properties.longitude, entity.properties.latitude];

  const allEntitys = stateControler.entityListData.flatMap(item => item.content.flatMap(element => element.children.map(i => i)));
  const isDuplicateName = allEntitys.some(i => i.label === name && i.id !== entityId);
  if (isDuplicateName) return ElMessage.error('实体名称重复');

  // 如果实体为卫星且参数为六根数形式
  if (entity.properties.modelType === 2) {
    const tles = sixParamToTwoLine(entity);
    entityParamConfig.value.satelliteTLE(tles[0], tles[1], position => {
      //更新卫星位置
      coordinates[0] = position[0];
      coordinates[1] = position[1];
      entity.properties.altitude = position[2];
    });

    entity.properties.altitudeUnit = 'km';
  }
  // 如果实体为卫星且参数为两行根数
  if (entity.properties.modelType === 1) {
    // 获取到卫星的机动组件
    const data = stateControler.entityModPar.get(entity.properties.id).modules.find(i => i.mainCategoryId === 4);
    let moverParams;
    if (entity.properties.params) {
      moverParams = entity.properties.modules.find(i => i.mainCategoryId === 4).params[0]?.params;
    } else {
      moverParams = data?.params[0]?.params;
    }
    if (moverParams) {
      const tle1 = moverParams.find(i => i.displayName === '第一行根数').value;
      const tle2 = moverParams.find(i => i.displayName === '第二行根数').value;
      entityParamConfig.value.satelliteTLE(tle1, tle2, position => {
        //更新卫星位置
        coordinates[0] = position[0];
        coordinates[1] = position[1];
        entity.properties.altitude = position[2];
      });

      entity.properties.altitudeUnit = 'km';
    }
  }
  //todo 实体绑定航路处理
  const routeGeojson = await getRouteGeojson(entity.properties, entity.properties.side, stateControler.entityListData);
  if (routeGeojson) mapInstance.value.addGraphical(routeGeojson);

  mapInstance.value.geojson.features.forEach(el => {
    //todo 这里是geojson 的数据区分是区域还是实体
    // 重新设置实体经纬度
    if (el.properties.entityId === entityId && el.properties.isEntity) {
      if (!el.id) el.id = 'entity';
      // 更新当前实体的配置
      stateControler.entityModPar.set(el.properties.id, {
        params: entity.properties.params,
        modules: entity.properties.modules,
      });
      console.log('🚀 ~ saveModel ~ entity.properties.modules:', entity.properties.modules);
      el.properties = entity.properties;
      // geojson 不存储参数和组件数据
      delete el.properties.params;
      delete el.properties.modules;
      const entityParams = stateControler.entityModPar.get(el.properties.id).params;
      // console.log(stateControler.entityModPar.get(el.properties.id), '参数');
      //获取朝向更新
      const heading = entityParams.find(i => i.name === 'heading');
      if (heading) el.properties.yaw = Number(heading.value);

      //获取高度更新高度单位
      const altitudeIndex = entityParams.findIndex(i => i.name === 'altitude');
      entityParams[altitudeIndex].unitType = el.properties.modelType === 2 ? 'km' : el.properties.altitudeUnit; //设置高度单位
      entityParams[altitudeIndex].appendValue = el.properties.appendValue; //设置高度类型
      el.geometry.coordinates = coordinates;
    }
  });

  entity.coordinates = coordinates;

  // 设置图标高亮
  mapInstance.value.setHighLight(coordinates);
  // 刷新实体列表数据(编辑为删除原数据，添加新数据)
  // addToEntityList(stateControler.entityListData, entity.properties);

  // 同步修改指挥链
  bus.emit('changeCommandChain');
  bus.emit('changeSide', entity.properties.side);

  // 关掉实体参数配置列表
  cancelBtn();
  // // 根据需要进行转义
  // if (typeof entity.properties.params === 'string') {
  //   entity.properties.params = JSON.parse(entity.properties.params);
  // }
  // 重新渲染地图数据
  mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
  mapInstance.value.clearHighlight();
  // 绘制组件实体绑定航路点设为false
  bus.emit('setAddWayPoint', false);
};
// 删除实体
const deleteMode = e => {
  ElMessageBox.confirm('确定删除该实体吗?', '提示', {
    type: 'warning',
    closeOnClickModal: false,
  })
    .then(() => {
      //删除地图实体
      mapInstance.value.deleteEntity({id: e.properties.entityId, name: e.properties.label});
      //实体列表删除实体
      const currentEntityListData = JSON.parse(JSON.stringify(stateControler.entityListData));
      currentEntityListData.forEach(ele => {
        ele.content.forEach(el => {
          el.children = el.children.filter(item => {
            return item.id !== e.properties.entityId;
          });
        });
        //删除没有实体的模型类别
        ele.content = ele.content.filter(i => i.children.length !== 0);
      });

      stateControler.entityListData = currentEntityListData;
      // 关掉实体参数配置列表
      cancelBtn();
      // 同步修改指挥链
      bus.emit('changeCommandChain');
      ElMessage.success('删除成功!');
    })
    .catch(err => console.log(err));
};
// 关闭实体参数配置列表
const cancelBtn = () => {
  // mapInstance.value.clearHighlight();
  mapInstance.value.closeParamsBox();
};
</script>
<template>
  <div class="have-geo-entity-param-config">
    <!--有地理位置实体参数配置弹窗-->
    <div id="entityParameConfig">
      <h2 class="title">
        <span> 实体参数配置</span>
        <span class="close-btn" @click="cancelBtn">
          <el-icon><Close /></el-icon
        ></span>
      </h2>
      <EntityParamConfig ref="entityParamConfig" :type="'edit'" />
      <div class="dialog-footer">
        <el-button class="delete-entity" @click="deleteMode(stateControler.currentModelData)">删除</el-button>
        <el-button @click="saveModel(stateControler.currentModelData)">保存</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
/* stylelint-disable-next-line selector-id-pattern */
#entityParameConfig {
  display: none;
  max-height: 473px;
  min-height: 473px;
  padding-bottom: 30px;

  .dialog-footer {
    position: absolute;
    top: 425px;
    left: 0;
    height: 35px;
    line-height: 38px;
    background: var(--panel-bg-color);

    .delete-entity {
      color: #f78384;
      background: url('@/assets/images/btn-del-bg.png') no-repeat 0 0 / 100% 100% !important;
    }
  }

  :deep(.el-collapse) {
    border-top: 1px solid var(--box-bg-color) !important;
    border-bottom: 1px solid var(--box-bg-color) !important;
  }

  :deep(.el-collapse-item__wrap) {
    background-color: var(--box-bg-color) !important;
    border: none !important;
  }

  :deep(.el-collapse-item__header) {
    height: 30px;
    margin-top: 5px;
    font-size: 15px;
    line-height: 30px;
    color: #63c6ff;
    background-color: var(--box-bg-color) !important;
    border: var(--border);
    border-radius: 2px 2px 0 0;
  }

  :deep(.el-collapse-item__content) {
    padding-bottom: 5px;

    .el-table--border .el-table__inner-wrapper::after {
      display: none;
    }
  }

  .title {
    padding: 5px 0;
    font-family: Medium;
    font-size: 16px;
    color: #fff;
    text-align: center;
    background-color: var(--title-bg-color);

    .close-btn {
      float: right;
      width: 35px;
      height: 25px;
      margin-top: -2px;
      font-size: 24px;
      color: var(--icon-color);
    }
  }

  .setting-param {
    position: relative;
    width: 100%;
  }

  .setting-param > .el-input {
    padding: 0 10px;
  }
}
</style>
