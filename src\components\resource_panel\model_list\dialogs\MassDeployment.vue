<!--
 * @Description: 批量添加模型组件
 * @Author: t<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-12-21 09:39:14
 * @LastEditors: 老范
 * @LastEditTime: 2024-07-24 14:34:15
-->

<script setup>
import {addToEntityList, getModelData} from '@/utils/data_process';
import {uniformDistributionPoints} from '@/utils/tools';
import EntityParamConfig from '@/components/map_dialogs/param_edit/EntityParamConfig.vue';
import {ElMessage} from 'element-plus';
import {watch, reactive} from 'vue';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
import {v4 as uuidv4} from 'uuid';
import {entityParamProcess} from '@/utils/data_process';

const stateControler = useStateStore();
const {mapInstance} = storeToRefs(useMapStore());
const state = reactive({
  deploymentMode: '', //是否为点部署
  deploymentModeList: [
    {id: 1, name: '点位部署'},
    {id: 2, name: '区域部署'},
  ],
  modelNumber: '', //要生成的模型的数量
  pointPosition: '',
  tableData: [],
  features: {properties: {displayName: ''}}, //当前模型的参数
  lastModelId: '',
  depoly: false,
});
watch(
  () => stateControler.massDeploymentVisible,
  val => {
    //关闭把数据置空
    if (!val) {
      state.pointPosition = '';
      if (!state.deploymentMode) state.modelNumber = '';
    } else {
      state.depoly = true;
    }
  }
);
watch(
  () => stateControler.massDeploymentModelID,
  async val => {
    if (!val) return;
    //获取当前模型的feature
    const data = await getModelFeature();
    state.features = data;
    stateControler.currentModelData = state.features;
  }
);
const close = () => {
  //删除掉点或区域(如果存在)
  const deleteId = stateControler.massDeploymentData.id;
  if (deleteId) {
    const deleteData = mapInstance.value.graphicalGeojson.features.find(i => i.id === deleteId);
    mapInstance.value.deleteGraphical(deleteData);
  }
  stateControler.massDeploymentVisible = false;
  stateControler.massDeploymentModelID = null;
};
//选择部署方式
const selectedModel = () => {
  stateControler.isMassDeployment = true;

  close();
  if (state.deploymentMode === '点位部署') {
    mapInstance.value.drawInstance.changeMode('draw_point');
    mapInstance.value.mapDrawState = `点位部署`;
  } else {
    mapInstance.value.drawInstance.changeMode('draw_rectangle');
    mapInstance.value.mapDrawState = `区域部署`;
  }
};

// 创建实体
const addEntity = () => {
  if (state.deploymentMode === '' || state.modelNumber === '') return ElMessage.error('请输入参数');
  // 拿到已存的数据,根据id分别存
  const baseObj = stateControler.currentModelData.properties;

  if (state.deploymentMode === '点位部署') {
    const pointPosition = stateControler.massDeploymentData.coordinates;
    const modelArr = [];
    //依据数量添加
    for (let i = 0; i < state.modelNumber; i++) {
      //todo 设置每一个模型的id、名称和经纬度
      const entityId = stateControler.currentEntityId + i + 1;
      const item = JSON.parse(JSON.stringify(state.features));
      item.properties.entityDisplayName = item.properties.displayName + entityId;
      item.properties.entityName = item.properties.name + entityId;
      item.properties.entityId = entityId;
      item.properties.id = uuidv4().replace(/-/g, '');
      item.properties.longitude = pointPosition[0].toFixed(6);
      item.properties.latitude = pointPosition[1].toFixed(6);
      item.geometry.coordinates = pointPosition;
      const data = entityParamProcess(baseObj);
      stateControler.entityModPar.set(item.properties.id, data);
      modelArr.push(item);
      state.lastModelId = entityId;
    }
    //!遍历模型数组添加
    modelArr.forEach(item => {
      const data = JSON.parse(JSON.stringify(item));
      delete data.params;
      delete data.modules;
      //先添加到map的geojson中
      mapInstance.value.geojson.features.push(data);
      // 实体列表添加
      // addToEntityList(stateControler.entityListData, item.properties);
    });
    //地图geojson重新渲染
    mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
    //更新id
    stateControler.currentEntityId = state.lastModelId++;
  } else {
    //在多边形内生成点
    const areaPositionArr = stateControler.massDeploymentData.coordinates[0];
    const pointsPositionArr = uniformDistributionPoints(areaPositionArr, state.modelNumber);
    const modelArr = [];
    //依据数量添加
    for (let i = 0; i < state.modelNumber; i++) {
      //todo 设置每一个模型的id、名称和经纬度
      const entityId = stateControler.currentEntityId + i + 1;
      const item = JSON.parse(JSON.stringify(state.features));
      item.properties.entityDisplayName = item.properties.displayName + entityId;
      item.properties.entityName = item.properties.name + entityId;
      item.properties.entityId = entityId;
      item.properties.id = uuidv4().replace(/-/g, '');
      item.properties.longitude = pointsPositionArr[i][0].toFixed(6);
      item.properties.latitude = pointsPositionArr[i][1].toFixed(6);
      item.geometry.coordinates = pointsPositionArr[i];
      const data = entityParamProcess(baseObj);
      stateControler.entityModPar.set(item.properties.id, data);
      // console.log(stateControler.entityModPar.get(item.properties.id));
      modelArr.push(item);
      state.lastModelId = entityId;
    }
    //!遍历模型数组添加
    modelArr.forEach(item => {
      const data = JSON.parse(JSON.stringify(item));
      delete data.params;
      delete data.modules;
      //先添加到map的geojson中
      mapInstance.value.geojson.features.push(data);
      // 实体列表添加
      // addToEntityList(stateControler.entityListData, item.properties);
    });
    //地图geojson重新渲染
    mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
    //更新id
    stateControler.currentEntityId = state.lastModelId;
  }
  // 删除用来获取部署参数的模板数据
  stateControler.entityModPar.delete(state.features.properties.id);
  close();
  state.modelNumber = '';
  state.depoly = false; //部署完把实体参数组件销毁
  mapInstance.value.clearHighlight();

  //删除掉点或区域
  const deleteId = stateControler.massDeploymentData.id;
  const deleteData = mapInstance.value.graphicalGeojson.features.find(i => i.id === deleteId);
  mapInstance.value.deleteGraphical(deleteData);
  //关闭部署状态
  stateControler.isMassDeployment = false;
  state.deploymentMode = '';
};
//获取模型的feature
const getModelFeature = async () => {
  const entityId = stateControler.currentEntityId + 1;
  const param = {
    isPlatFormType: true,
    platformTypeId: stateControler.massDeploymentModelID,
    currentModelId: entityId,
    side: stateControler.currentSide,
  };
  const result = await getModelData(param);

  stateControler.massDeploymentVisible = true;
  return result;
};
const cancel = () => {
  close();
  stateControler.isMassDeployment = false;
  state.deploymentMode = '';
  state.modelNumber = '';
  state.depoly = false;
  //删除掉点或区域
  mapInstance.value.drawInstance.delete(stateControler.massDeploymentData.id);
};
</script>

<template>
  <el-dialog v-model="stateControler.massDeploymentVisible" class="mass-deployment" :title="'批量部署'" width="500px" :close-on-click-modal="false" center :modal-append-to-body="false" :before-close="() => close()">
    <div class="content">
      <div class="name"><b>模型名称：</b>{{ state.features.properties.displayName }}</div>
      <div class="deployment">
        <b>部署方式：</b>
        <el-select v-model="state.deploymentMode" suffix-icon="CaretBottom" @change="selectedModel">
          <el-option v-for="item in state.deploymentModeList" :key="item.id" :label="item.name" :value="item.name"> </el-option>
        </el-select>
      </div>
      <div class="model-number"><b>模型数量：</b><el-input v-model="state.modelNumber" type="number"></el-input></div>
      <div class="param-box">
        <b>参数配置:</b>
        <EntityParamConfig v-if="state.depoly" :type="'deploy'" />
      </div>
    </div>
    <template #footer>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="addEntity">部署</el-button>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.mass-deployment {
  :deep(.el-dialog__footer) {
    padding: 0 !important;
  }
}

:deep(.el-collapse) {
  border-top: 1px solid var(--panel-bg-color) !important;
  border-bottom: 1px solid var(--panel-bg-color) !important;
}

:deep(.el-collapse-item__wrap) {
  background-color: var(--panel-bg-color) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

:deep(.el-collapse-item__header) {
  height: 25px;
  line-height: 25px;
  color: #fff;
  border-bottom: 1px solid var(--border-color);
}

:deep(.el-collapse-item__content) {
  padding-bottom: 5px;
}

.content {
  height: 450px;
  padding: 10px;
  overflow: auto;
  color: #fff;

  > div {
    margin: 10px 0;

    .el-input,
    .el-select {
      flex: 1;
    }
  }

  b {
    width: 70px;
  }

  .deployment {
    display: flex;
  }

  .model-number {
    display: flex;
  }
}

:deep(.el-dialog__close) {
  display: none;
}

.dialog-footer {
  position: relative;
  top: -52px;
  display: inline-block;
  width: 100%;
  background: linear-gradient(-45deg, transparent 10px, var(--panel-bg-color) 0) right, linear-gradient(45deg, transparent 10px, var(--panel-bg-color) 0) left;
  background-size: 50% 100%;
  background-repeat: no-repeat;
}
</style>
