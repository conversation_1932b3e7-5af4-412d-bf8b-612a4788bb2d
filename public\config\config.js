/*
 * <AUTHOR> laofan
 * @Date         : 2023-02-24 11:20:15
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-02 11:08:50
 * @Description  : 全局路径配置文件
 */
const globalConfig = {
  //home
  BaseResourceUrl: 'http://**************:38080', //资源服务接口地址
  ModelingToolUrl: 'http://************:9091', // 建模工具
  ResourceToolUrl: 'http://************:9092', // 资源工具 异构模型
  ScenarioToolUrl: 'http://*************:8686', // 想定设计
  Simulation3DToolUrl: 'http://************:8689', // 仿真推演
  ExperimentToolUrl: 'http://************:8081', // 实验管理工具
  EvaluationToolUrl: 'http://**************:7081', // 效能评估

  //模型
  WizardExeURL: 'http://localhost:8756', // 调试脚本服务地址

  //想定设计
  PreviewControlUrl: 'http://**************:38838', //预演运控
  ReviewBaseWSUrl: 'ws://**************:38838', //预演socket
  // PreviewControlUrl: 'http://************:8838', //预演运控
  // ReviewBaseWSUrl: 'ws://*************:38838', //预演socket
  LocalRun: 'http://************:8756', // (本地运行调试)
  MapUrl: 'http://**************:8888/sprite_scenario', // (地图地址)

  //三维
  ControlWSURL3D: 'ws://*************:38838', //三维运控服务socket地址

  //试验设计
  ExperimentUrl: 'http://************:8182', // 实验数据接口
  ExperimentWsUrl: 'ws://************:8182', //实验管理socket

  //评估
  BaseEvaluationUrl: 'http://**************:38088', //效能评估服务
  LocalDataUrl: 'http://**************:38088', //本地数据获取地址
  scenarioAi: 'http://localhost:8686',
};
const configUrl = {
  afURL: 'http://**************:5000', //资源服务接口地址
  commenURL: 'http://**************:5000', //资源服务接口地址
  scenarioURL: 'http://*************:9212', // 想定ai工具地址
  scenarioTaskURL: 'http://*************:9212', // 想定任务ai工具地址
};
