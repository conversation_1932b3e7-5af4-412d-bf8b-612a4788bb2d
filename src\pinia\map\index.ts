/*
 * @Description: 地图的状态管理
 * @Author: tzs
 * @Date: 2024-02-28 14:18:04
 * @LastEditors: tzs
 * @LastEditTime: 2024-03-06 16:06:00
 *
 */
import {defineStore} from 'pinia';
import {computed, ref, Ref} from 'vue';

export const useMapStore = defineStore('map', () => {
  const mapInstance: Ref = ref('');

  const setMapInstance = (map: Object) => {
    mapInstance.value = map;
  };
  const MapGl = computed(() => mapInstance.value.map);
  return {mapInstance, MapGl, setMapInstance};
});
