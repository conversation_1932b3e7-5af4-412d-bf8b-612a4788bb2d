<script setup lang="ts">
// import CreateEditCamp from './dialogs/CreateEditCamp.vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {move} from '@/utils/tools';
import bus from '@/utils/bus';
import {addToEntityList, getModelData} from '@/utils/data_process';
import {useStateStore} from '@/pinia/state/index';
import {useMapStore} from '@/pinia/map/index';
import {storeToRefs} from 'pinia';
import {onMounted, reactive, ref, watch} from 'vue';
import {featureType, propertiesType} from '@/types';
import {geoJsonEntitySort} from '@/utils/data_process';

const stateControler = useStateStore();

const {MapGl, mapInstance} = storeToRefs(useMapStore());

const createEditCamp = ref('');
const entityContent = ref('');
const entityTreeRef = ref();
const state = reactive({
  createCampVisible: false, //创建阵营弹框
  isEdit: false,
});
const resetEntityList = () => {
  stateControler.entityListData.forEach(item => {
    item.content = [];
  });
};
watch(
  () => mapInstance.value.geojson.features,
  val => {
    resetEntityList();
    val.forEach((feature: featureType) => {
      productEntitylist(feature);
    });
  },
  {deep: true, immediate: true}
);
watch(
  () => stateControler.currentSide,
  val => {
    const sideIndex = val === 'red' ? 0 : 1;
    entityTreeRef.value[sideIndex].setCheckedNodes([]);
  }
);
const productEntitylist = (modelData: featureType) => {
  //获取properties(模型)数据
  const properties = modelData.properties;
  stateControler.entityListData.forEach(item => {
    if (modelData.properties.side === item.side) {
      if (!properties.isEntity) return;
      campList(item.content, properties);
    }
  });
};

const campList = (currentCampData: any[], model: propertiesType) => {
  //获取该阵营是否有创建的该模型
  const modelArr = currentCampData.filter((ele: {id: any}) => {
    return ele.id === model.platformTypeId;
  });
  //获取当前模型在阵营数组中的index
  const index = currentCampData.findIndex((ele: {id: any}) => {
    return ele.id === model.platformTypeId;
  });

  //如果该模型没有创建过
  if (modelArr.length < 1) {
    modelArr.push({
      id: model.platformTypeId,
      label: model.displayName,
      children: [
        {
          id: model.entityId,
          label: model.entityDisplayName,
          platformTypeId: model.platformTypeId,
        },
      ],
    });
  } else {
    // 遍历该模型的实体数组
    const idx = modelArr[0].children.findIndex((i: {id: any}) => i.id === model.entityId);
    // 实体数据
    const entity = {
      id: model.entityId,
      label: model.entityDisplayName,
      platformTypeId: model.platformTypeId,
    };
    // 是否为编辑模型
    if (idx !== -1) {
      modelArr[0].children.splice(idx, 1, entity);
    } else {
      modelArr[0].children.push(entity);
    }
  }
  //将当前实体添加到实体列表中
  modelArr.forEach((el: any) => {
    //判断当前模型是否有创建过
    if (index !== -1) {
      currentCampData[index] = el;
    } else {
      currentCampData.push(el);
    }
  });
  return currentCampData;
};
// 拖拽限制
const allowDrop = (draggingNode, dropNode, type) => {
  // console.log(draggingNode.parent.id, dropNode.parent.id);
  // 只允许同级排序，不允许拖动到子节点
  if (draggingNode.level === dropNode.level) {
    if (draggingNode.parent.id === dropNode.parent.id) {
      return type === 'prev' || type === 'next';
    } else {
      return false;
    }
  } else {
    return false;
  }
};
//编辑阵营
const editCamp = (side: {title: any; color: any; id: any}) => {
  state.isEdit = true;
  state.createCampVisible = true;
  createEditCamp.value.form.name = side.title;
  createEditCamp.value.form.color = side.color;
  createEditCamp.value.form.id = Number(side.id);
};
//删除阵营
const removeTab = (targetName: string) => {
  ElMessageBox.confirm('此操作将永久删除该阵营, 是否继续?', '提示', {
    type: 'warning',
    closeOnClickModal: false,
  })
    .then(() => {
      const tabs = stateControler.entityListData;
      let activeSide = stateControler.currentSide;
      // 阵营数据列表处理
      if (activeSide === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.id === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeSide = nextTab.id;
            }
            tab.content.forEach(ele => {
              ele.children.forEach(item => {
                const name = item.label;
                // 删除实体数据
                mapInstance.value.geojson.features = mapInstance.value.geojson.features.filter((el: {properties: {entityDisplayName: string}}) => {
                  return el.properties.entityDisplayName !== name;
                });
                mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
                // 删除高亮
                mapInstance.value.clearHighlight();
              });
            });
          }
        });
      }
      // 重新渲染实体列表
      stateControler.currentSide = activeSide;
      const currentEntityListData = tabs.filter(tab => tab.id !== targetName);
      stateControler.entityListData = currentEntityListData;
    })
    .catch(() => ElMessage.info('已取消'));
};
// 聚焦实体
const focusEntity = (data: {label: any}) => {
  // 当前实体
  const entity = mapInstance.value.geojson.features.filter((el: {properties: {entityDisplayName: any}}) => {
    return el.properties.entityDisplayName === data.label;
  });
  setEntityBackground(data);
  // 如果有地理位置
  if (entity.length > 0) {
    // 重新设置地图中心点及图标高亮
    const coordinates = entity[0].geometry.coordinates;
    mapInstance.value.map.setCenter(coordinates);
    //设置高亮
    mapInstance.value.setHighLight(coordinates);
  } else {
    mapInstance.value.clearHighlight();
  }
};
// 编辑的实体
const editEntity = (data: {id: any}) => {
  console.log('🚀 ~ editEntity ~ data:', data);
  // 清除当前显示的实体
  mapInstance.value.clearHighlight();
  // 有地理位置实体
  const entity = mapInstance.value.geojson.features.filter((el: {properties: {entityId: any}}) => {
    return el.properties.entityId === data.id;
  });
  if (entity.length > 0) {
    mapInstance.value.highLightId = entity[0].properties.id;
    const coordinates = entity[0].geometry.coordinates;
    //当前实体数据传递到父组件
    stateControler.currentModelData = {coordinates, properties: JSON.parse(JSON.stringify(entity[0].properties))};
    mapInstance.value.showEdit(coordinates);
  }
  setEntityBackground(data);
};

//复制实体
const copyEntity = async (data: {id: any}) => {
  //获取到要复制的实体数据
  const res = mapInstance.value.geojson.features.filter((i: {properties: {entityId: any}}) => i.properties.entityId === data.id)[0];
  const copyEntityData = JSON.parse(JSON.stringify(res));
  copyEntityData.geometry.coordinates[0] = Number(copyEntityData.geometry.coordinates[0]);
  copyEntityData.geometry.coordinates[1] = Number(copyEntityData.geometry.coordinates[1]);
  stateControler.currentEntityId += 1;
  const param = {
    isPlatFormType: true,
    platformTypeId: copyEntityData.properties.platformTypeId,
    currentModelId: stateControler.currentEntityId,
    side: copyEntityData.properties.side,
    copySourceId: copyEntityData.properties.id,
  };
  // 调用接口
  const resData = await getModelData(param);
  const datas = resData.properties;
  copyEntityData.properties.id = datas.id;
  copyEntityData.properties.entityId = datas.entityId;
  copyEntityData.properties.entityDisplayName = datas.entityDisplayName;
  copyEntityData.properties.entityName = datas.entityName;

  //添加到地图
  mapInstance.value.addEntity(JSON.parse(JSON.stringify(copyEntityData)));

  //更新实体列表
  // addToEntityList(stateControler.entityListData, copyEntityData);
};
// 删除单个实体
const remove = (node: any, data: {id: any; label: any}) => {
  ElMessageBox.confirm('确定删除该实体吗?', '提示', {
    type: 'warning',
    closeOnClickModal: false,
  })
    .then(() => {
      //删除地图实体
      mapInstance.value.deleteEntity({id: data.id, name: data.label});
      //删除实体列表数据
      removeNode(node, data);
      // 同步修改指挥链
      bus.emit('changeCommandChain');
    })
    .catch(() => ElMessage.info('已取消'));
};
// 删除一类实体
const removeAll = (node: any, data: {id: any; label: any}) => {
  ElMessageBox.confirm('确定删除该组实体吗?', '提示', {
    type: 'warning',
    closeOnClickModal: false,
  })
    .then(() => {
      //删除一类实体，需要传入阵营id
      mapInstance.value.deleteEntity({id: data.id, name: data.label, side: stateControler.currentSide}, true);
      removeNode(node, data);
      // 同步修改指挥链
      bus.emit('changeCommandChain');
    })
    .catch(() => ElMessage.info('已取消'));
};
// 删除节点函数
const removeNode = (node: {parent: {parent: {removeChild: (arg0: any) => void}}}, data: {id: any; label: string}) => {
  // 重新渲染数据
  mapInstance.value.map.getSource('places').setData(mapInstance.value.geojson);
  // 同步删除高亮
  mapInstance.value.clearHighlight();

  // 删除子节点
  const parent = node.parent;
  const children = parent.data.children || parent.data;
  const index = children.findIndex((d: {id: any}) => d.id === data.id);
  children.splice(index, 1);
  // 没有子节点时同步删除父节点
  if (parent.data.children?.length === 0) {
    node.parent.parent.removeChild(node.parent);
    data.label.replace(data.id, '');
  }
  ElMessage.success('删除成功!');
};

//el-tree对应实体行高亮
const setEntityBackground = (data: {label: any}) => {
  // 实体列表对应数据行高亮显示
  const tree = document.querySelectorAll('#entityTree');
  tree.forEach(el => {
    const treeNode = el.querySelectorAll('.el-tree-node__content');
    treeNode.forEach(ele => {
      ele.style.backgroundColor = 'transparent';
      if (ele.innerText === data.label) {
        ele.style.backgroundColor = '#5fc5ff66';
      }
    });
  });
};
//添加阵营
const addEntityListData = (data: {newSide: string; newCampData: any}) => {
  //设置当前选择的阵营
  stateControler.currentSide = data.newSide;
  //将新阵营数据添加
  const currentEntityListData = JSON.parse(JSON.stringify(stateControler.entityListData));
  currentEntityListData.push(data.newCampData);
  //设置阵营数据
  stateControler.entityListData = currentEntityListData;
};
const addToGroup = () => {
  const sideIndex = stateControler.currentSide === 'red' ? 0 : 1;
  stateControler.entity2GroupKeys = entityTreeRef.value[sideIndex].getCheckedKeys(true);
};
// 拖拽结束后更新geojson的顺序
const dragEnd = () => {
  geoJsonEntitySort(mapInstance.value.geojson);
};
onMounted(() => {
  move(entityContent.value);
  bus.on('changeSide', side => (stateControler.currentSide = side));
});
</script>

<template>
  <div ref="entityContent" class="entity-content">
    <div class="entity-body">
      <el-tabs v-model="stateControler.currentSide" :stretch="true" type="card" @tab-remove="removeTab">
        <el-tab-pane v-for="item in stateControler.entityListData" :key="item.side" :label="item.title" :name="item.side">
          <!-- <template #label>
            <span class="custom-tabs-label">
              <span>{{item.title}}</span>
            </span>
          </template> -->
          <el-tree id="entityTree" ref="entityTreeRef" draggable :data="item.content" :show-checkbox="stateControler.showCheckbox" node-key="id" default-expand-all :expand-on-click-node="false" :allow-drop="allowDrop" @node-drag-end="dragEnd" @node-click="focusEntity">
            <template #default="{node, data}">
              <span v-if="node.level !== 1" class="custom-tree-node text-ellipsis">
                <span :title="node.label" class="node-label text-ellipsis">
                  {{ node.label }}
                </span>
                <span class="node-button">
                  <i title="复制" class="icon copy-icon" @click.stop="copyEntity(data)"> </i>
                  <i title="编辑" class="icon edit-icon" @click.stop="editEntity(data)"> </i>
                  <i title="删除" class="icon delete-icon" @click.stop="remove(node, data)"> </i>
                </span>
              </span>
              <span v-else class="custom-tree-node text-ellipsis">
                <span :title="node.label" class="pnode-label text-ellipsis"> {{ node.label }}</span>
                <i v-show="stateControler.showCheckbox" title="添加至编组" @click="addToGroup"
                  ><el-icon><Promotion /></el-icon>
                </i>
                <i title="删除" class="icon delete-icon" @click="removeAll(node, data)"> </i>
              </span>
            </template>
          </el-tree>
        </el-tab-pane>
      </el-tabs>
      <!-- <div class="add-tab">
        <el-button
          size="small"
          @click="
            createCampVisible = true;
            isEdit = false;
          "
        >
          添加阵营</el-button
        >
      </div> -->
    </div>
    <!--创建，编辑阵营-->
    <!-- <CreateEditCamp ref="createEditCamp" :create-camp-visible="createCampVisible" :current-side="currentSide" :is-edit="isEdit" @close="createCampVisible = false" @add-entity-list-data="addEntityListData" /> -->
  </div>
</template>

<style lang="less" scoped>
.entity-content {
  width: 100%;
  height: calc(100vh - 190px);
  color: #fff;
  border-radius: 5px;
  box-sizing: border-box;

  .entity-body {
    width: 100%;
    height: 100%;
    box-sizing: border-box;

    .custom-tabs-label {
      width: 100%;
    }

    .custom-tree-node {
      display: flex;
      width: 240px;
      height: 100%;
      padding-right: 10px;
      font-size: 15px;
      text-align: left !important;
      align-items: center;
      justify-content: flex-end;
    }

    .custom-tree-node:hover {
      .node-label {
        display: inline-block;
        width: 210px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .node-button {
        position: absolute;
        right: 10px;
        display: flex;
      }
    }
  }

  :deep(.node-label) {
    // position: absolute;
    // left: 30px;
    display: inline-block;
    width: 210px;
    // padding-left: 20px;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }

  :deep(.pnode-label) {
    // position: absolute;
    // left: 30px;
    display: inline-block;
    width: 190px;
  }

  :deep(.node-button) {
    display: none;
  }

  :deep(.node-button .el-button) {
    padding: 0 !important;
  }
}

:deep(.entity-body .el-tabs__content) {
  height: calc(100% - 32px) !important;
}

// :deep(.entity-content .node-label) {
//   display: inline-block;
//   width: 70%;
//   overflow: hidden;
//   text-overflow: ellipsis;
// }

:deep(.entity-body .el-button) {
  border: none !important;
}

.el-icon {
  margin: 0 2px;
  color: var(--border-color);
}

.add-tab {
  position: relative;
  z-index: 100;
  height: 5%;
  min-height: 20px;
}
</style>
